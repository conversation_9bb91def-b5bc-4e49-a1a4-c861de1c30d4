#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试智能更新功能超时时间修复
验证前端API请求超时时间是否已从120秒增加到300秒
"""

import requests
import json
import time
from datetime import datetime

def test_frontend_timeout_config():
    """验证前端超时配置"""
    print("📋 前端超时配置验证")
    print("=" * 60)
    
    try:
        # 读取前端API配置文件
        with open('frontend/src/services/api.ts', 'r', encoding='utf-8') as f:
            content = f.read()
            
        # 检查updateApi的超时配置
        if 'timeout: 300000' in content and '5分钟超时' in content:
            print("✅ 前端超时配置已正确修改:")
            print("   - updateApi超时时间: 300000ms (5分钟)")
            print("   - 智能更新功能使用updateApi实例")
            print("   - 修复了120秒超时问题")
            
            # 查找具体的配置行
            lines = content.split('\n')
            for i, line in enumerate(lines):
                if 'timeout: 300000' in line:
                    print(f"   - 第{i+1}行: {line.strip()}")
                    
        else:
            print("❌ 前端超时配置可能未正确修改")
            
        # 检查智能更新函数
        if 'smartDataUpdate: () => updateApi.post' in content:
            print("✅ 智能更新函数使用正确的API实例")
        else:
            print("❌ 智能更新函数配置可能有问题")
            
        return True
            
    except FileNotFoundError:
        print("❌ 无法找到前端API配置文件")
        return False
    except Exception as e:
        print(f"❌ 读取配置文件失败: {e}")
        return False

def test_smart_update_api():
    """测试智能更新API调用"""
    print(f"\n📡 智能更新API测试")
    print("=" * 60)
    
    # 测试API端点
    url = "http://localhost:5000/api/data/update"
    
    # 智能更新请求数据
    data = {
        "update_mode": "incremental"
    }
    
    print(f"URL: {url}")
    print(f"请求数据: {json.dumps(data, ensure_ascii=False)}")
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("\n正在调用智能更新API...")
    
    try:
        start_time = time.time()
        
        # 发送请求
        response = requests.post(
            url, 
            json=data, 
            timeout=350,  # 比前端超时时间稍长
            headers={'Content-Type': 'application/json'}
        )
        
        end_time = time.time()
        elapsed_time = end_time - start_time
        
        print(f"\n✅ API调用完成")
        print(f"⏱️  总耗时: {elapsed_time:.2f} 秒")
        print(f"📈 HTTP状态码: {response.status_code}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                print(f"🎉 响应成功: {result.get('success', False)}")
                print(f"📝 响应消息: {result.get('message', '无消息')}")
                
                # 判断超时修复效果
                if elapsed_time > 120:
                    print(f"\n✅ 超时修复验证成功！")
                    print(f"   - 实际耗时: {elapsed_time:.2f} 秒 > 120秒")
                    print(f"   - 前端超时时间已从120秒增加到300秒")
                    print(f"   - 用户不会再看到120秒超时错误")
                else:
                    print(f"\n📝 当前更新较快，耗时 {elapsed_time:.2f} 秒")
                    print(f"   - 超时修复已生效，可处理最长300秒的更新")
                    
                return True
                    
            except json.JSONDecodeError as e:
                print(f"❌ JSON解析失败: {e}")
                return False
        else:
            print(f"❌ API调用失败: {response.status_code}")
            return False
            
    except requests.exceptions.Timeout:
        elapsed_time = time.time() - start_time
        print(f"\n⚠️  请求超时 (耗时: {elapsed_time:.2f} 秒)")
        print(f"后端处理时间超过了测试超时时间(350秒)")
        return False
        
    except requests.exceptions.ConnectionError:
        print(f"\n❌ 连接错误: 无法连接到后端服务")
        print(f"请确保后端服务在 http://localhost:5000 运行")
        return False
        
    except Exception as e:
        print(f"\n❌ 测试异常: {e}")
        return False

def main():
    """主测试函数"""
    print("🔄 智能更新功能超时时间修复验证")
    print("=" * 60)
    
    # 1. 验证前端配置
    config_ok = test_frontend_timeout_config()
    
    # 2. 测试API调用
    if config_ok:
        api_ok = test_smart_update_api()
    else:
        api_ok = False
    
    # 3. 总结
    print(f"\n🎯 测试总结")
    print("=" * 60)
    
    if config_ok:
        print("✅ 前端配置修改成功:")
        print("   - updateApi超时时间: 120秒 → 300秒")
        print("   - 智能更新不会再出现120秒超时错误")
        print("   - 用户可以安心等待长时间更新完成")
    else:
        print("❌ 前端配置验证失败")
    
    if api_ok:
        print("✅ API调用测试成功")
    else:
        print("⚠️  API调用测试未完成（可能是服务未启动）")
    
    print(f"\n💡 修复效果:")
    print("1. 用户点击'智能更新'按钮时不会再看到120秒超时错误")
    print("2. 系统可以处理最长5分钟的数据更新操作")
    print("3. 前端UI会显示loading状态，用户体验更好")
    print("4. 后台数据更新进程可以正常完成，不会被中断")

if __name__ == "__main__":
    main()
