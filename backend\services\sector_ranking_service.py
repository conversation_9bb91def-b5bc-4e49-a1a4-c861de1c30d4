"""
板块排名数据收集服务（修复版）
实现每日收盘后自动获取板块数据并筛选排名前10的板块存储到数据库
支持历史数据收集
"""
import logging
import pandas as pd
from datetime import datetime, date, timedelta
from typing import List, Dict, Any, Optional
from database import db
from models.sector_ranking import SectorDailyRanking
from services.data_service import DataService
from utils.trading_calendar import trading_calendar
import akshare as ak

logger = logging.getLogger(__name__)

class SectorRankingService:
    """板块排名数据收集服务（修复版）"""
    
    def __init__(self):
        """初始化服务"""
        self.data_service = DataService()
        logger.info("板块排名数据收集服务初始化完成")
    
    def collect_daily_rankings(self, target_date: date = None, top_n: int = 10) -> bool:
        """
        收集每日板块排名数据
        
        Args:
            target_date: 目标日期，默认为今天
            top_n: 排名前N的板块，默认为10
            
        Returns:
            bool: 收集是否成功
        """
        if target_date is None:
            target_date = date.today()
        
        logger.info(f"开始收集 {target_date} 的板块排名数据（前{top_n}名）")
        
        try:
            # 检查是否为交易日
            if not trading_calendar.is_trading_day(target_date):
                logger.warning(f"{target_date} 不是交易日，跳过数据收集")
                return False
            
            # 检查是否已有当日数据
            existing_count = SectorDailyRanking.query.filter_by(ranking_date=target_date).count()
            if existing_count > 0:
                logger.info(f"{target_date} 的排名数据已存在（{existing_count}条），先清除旧数据")
                SectorDailyRanking.query.filter_by(ranking_date=target_date).delete()
                db.session.commit()
            
            # 根据日期选择数据获取方式
            today = date.today()
            if target_date == today:
                # 今天的数据使用实时数据
                logger.info("获取今日实时板块数据...")
                sector_data = self.data_service.get_enhanced_sector_realtime_data()
            else:
                # 历史数据使用历史数据API
                logger.info(f"获取 {target_date} 的历史板块数据...")
                sector_data = self._get_historical_sector_data(target_date)
            
            if sector_data.empty:
                logger.error("无法获取板块数据")
                return False
            
            logger.info(f"成功获取 {len(sector_data)} 个板块数据")
            
            # 数据预处理和排序
            processed_data = self._process_sector_data(sector_data)
            
            if processed_data.empty:
                logger.error("数据处理后为空")
                return False
            
            # 按涨跌幅排序，筛选前N名
            top_sectors = processed_data.nlargest(top_n, '涨跌幅')
            logger.info(f"筛选出排名前{top_n}的板块")
            
            # 转换为数据库格式并批量插入
            rankings_data = self._convert_to_ranking_data(top_sectors, target_date)
            success = self._bulk_insert_rankings(rankings_data)
            
            if success:
                logger.info(f"成功收集并存储 {target_date} 的板块排名数据（{len(rankings_data)}条）")
                return True
            else:
                logger.error("数据存储失败")
                return False
                
        except Exception as e:
            logger.error(f"收集板块排名数据失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def _get_historical_sector_data(self, target_date: date) -> pd.DataFrame:
        """
        获取指定日期的历史板块数据，包含领涨股票信息

        Args:
            target_date: 目标日期

        Returns:
            pd.DataFrame: 历史板块数据
        """
        try:
            logger.info(f"开始获取 {target_date} 的历史板块数据...")

            # 使用更可靠的方法获取历史数据
            # 1. 先获取该日期的A股市场整体数据
            date_str = target_date.strftime('%Y%m%d')

            # 获取该日期的A股个股数据
            try:
                stock_data = ak.stock_zh_a_hist(
                    symbol="000001",  # 使用平安银行作为参考
                    period="daily",
                    start_date=date_str,
                    end_date=date_str
                )

                if stock_data.empty:
                    logger.warning(f"{target_date} 可能不是交易日，无法获取历史数据")
                    return pd.DataFrame()

            except Exception as e:
                logger.warning(f"验证交易日失败: {e}，尝试继续获取数据")

            # 2. 获取板块历史数据
            historical_data = []

            # 使用固定的主要板块列表，避免依赖实时板块列表
            major_sectors = [
                {'板块代码': 'BK0451', '板块名称': '房地产开发'},
                {'板块代码': 'BK0437', '板块名称': '煤炭行业'},
                {'板块代码': 'BK0438', '板块名称': '食品饮料'},
                {'板块代码': 'BK0447', '板块名称': '汽车行业'},
                {'板块代码': 'BK0456', '板块名称': '电子元件'},
                {'板块代码': 'BK0464', '板块名称': '银行'},
                {'板块代码': 'BK0473', '板块名称': '证券'},
                {'板块代码': 'BK0481', '板块名称': '汽车服务'},
                {'板块代码': 'BK0493', '板块名称': '保险'},
                {'板块代码': 'BK0727', '板块名称': '军工'},
            ]

            for sector_info in major_sectors:
                try:
                    sector_code = sector_info['板块代码']
                    sector_name = sector_info['板块名称']

                    # 获取板块历史K线数据
                    hist_data = self._get_sector_historical_kline(sector_code, target_date)

                    if hist_data:
                        # 获取该板块的领涨股票信息
                        leading_stock_info = self._get_historical_leading_stock(sector_code, target_date)

                        # 合并数据
                        sector_data = {
                            '板块代码': sector_code,
                            '板块名称': sector_name,
                            '最新价': hist_data['close_price'],
                            '涨跌幅': hist_data['price_change_pct'],
                            '涨跌额': hist_data['price_change'],
                            '成交量': hist_data.get('volume', 0),
                            '成交额': hist_data.get('turnover', 0),
                            '领涨股票': leading_stock_info.get('name', ''),
                            '领涨股票代码': leading_stock_info.get('code', ''),
                            '领涨股票价格': leading_stock_info.get('price', 0),
                            '领涨股票涨跌幅': leading_stock_info.get('change_pct', 0),
                        }

                        historical_data.append(sector_data)
                        logger.debug(f"成功获取板块 {sector_name} 的历史数据")

                except Exception as e:
                    logger.warning(f"获取板块 {sector_name} 历史数据失败: {e}")
                    continue

            if historical_data:
                df = pd.DataFrame(historical_data)
                logger.info(f"成功获取 {len(df)} 个板块的历史数据")
                return df
            else:
                logger.error("未获取到任何历史数据")
                return pd.DataFrame()

        except Exception as e:
            logger.error(f"获取历史板块数据失败: {e}")
            return pd.DataFrame()

    def _get_sector_historical_kline(self, sector_code: str, target_date: date) -> dict:
        """
        获取板块历史K线数据

        Args:
            sector_code: 板块代码
            target_date: 目标日期

        Returns:
            dict: 包含价格和涨跌幅信息的字典
        """
        try:
            date_str = target_date.strftime('%Y%m%d')

            # 获取板块历史数据
            hist_data = ak.stock_board_industry_hist_em(
                symbol=sector_code,
                period="日k",
                start_date=date_str,
                end_date=date_str
            )

            if not hist_data.empty:
                latest = hist_data.iloc[-1]
                close_price = latest['收盘']
                open_price = latest['开盘']

                # 计算涨跌幅
                price_change_pct = ((close_price - open_price) / open_price) * 100
                price_change = close_price - open_price

                return {
                    'close_price': close_price,
                    'price_change_pct': price_change_pct,
                    'price_change': price_change,
                    'volume': latest.get('成交量', 0),
                    'turnover': latest.get('成交额', 0)
                }
            else:
                return None

        except Exception as e:
            logger.warning(f"获取板块 {sector_code} 历史K线数据失败: {e}")
            return None

    def _get_historical_leading_stock(self, sector_code: str, target_date: date) -> dict:
        """
        获取板块历史领涨股票信息

        Args:
            sector_code: 板块代码
            target_date: 目标日期

        Returns:
            dict: 领涨股票信息
        """
        try:
            # 由于历史数据API限制，这里使用简化的方法
            # 在实际应用中，可能需要更复杂的逻辑来获取历史领涨股票

            # 返回默认的领涨股票信息
            return {
                'name': f'{sector_code}_领涨股',
                'code': f'{sector_code}001',
                'price': 0.0,
                'change_pct': 0.0
            }

        except Exception as e:
            logger.warning(f"获取板块 {sector_code} 历史领涨股票失败: {e}")
            return {
                'name': '',
                'code': '',
                'price': 0.0,
                'change_pct': 0.0
            }
    
    def _process_sector_data(self, sector_data: pd.DataFrame) -> pd.DataFrame:
        """
        处理板块数据，确保数据格式正确
        
        Args:
            sector_data: 原始板块数据
            
        Returns:
            pd.DataFrame: 处理后的数据
        """
        try:
            if sector_data.empty:
                return pd.DataFrame()
            
            # 确保必要的列存在
            required_columns = ['板块名称', '涨跌幅', '最新价']
            missing_columns = [col for col in required_columns if col not in sector_data.columns]
            
            if missing_columns:
                logger.error(f"缺少必要的列: {missing_columns}")
                return pd.DataFrame()
            
            # 数据清洗
            processed_data = sector_data.copy()
            
            # 处理空值和异常值
            processed_data['涨跌幅'] = pd.to_numeric(processed_data['涨跌幅'], errors='coerce')
            processed_data['最新价'] = pd.to_numeric(processed_data['最新价'], errors='coerce')
            
            # 移除涨跌幅为空的记录
            processed_data = processed_data.dropna(subset=['涨跌幅'])
            
            # 移除涨跌幅异常的记录（超过±20%的可能是数据错误）
            processed_data = processed_data[
                (processed_data['涨跌幅'] >= -20) & 
                (processed_data['涨跌幅'] <= 20)
            ]
            
            logger.info(f"数据处理完成，有效数据 {len(processed_data)} 条")
            return processed_data
            
        except Exception as e:
            logger.error(f"处理板块数据失败: {e}")
            return pd.DataFrame()
    
    def _convert_to_ranking_data(self, top_sectors: pd.DataFrame, target_date: date) -> List[Dict[str, Any]]:
        """
        将板块数据转换为排名数据格式

        Args:
            top_sectors: 排名前N的板块数据
            target_date: 目标日期

        Returns:
            List[Dict]: 排名数据列表
        """
        rankings_data = []

        for idx, (_, row) in enumerate(top_sectors.iterrows()):
            ranking_data = {
                'ranking_date': target_date,
                'sector_code': row.get('板块代码', f'UNKNOWN_{idx}'),
                'sector_name': row['板块名称'],
                'ranking': idx + 1,
                'close_price': float(row['最新价']) if pd.notna(row['最新价']) else None,
                'price_change': float(row.get('涨跌额', 0)) if pd.notna(row.get('涨跌额')) else None,
                'price_change_pct': float(row['涨跌幅']) if pd.notna(row['涨跌幅']) else None,
                'volume': int(row.get('成交量', 0)) if pd.notna(row.get('成交量')) else None,
                'turnover': float(row.get('成交额', 0)) if pd.notna(row.get('成交额')) else None,
                # 添加领涨股票信息
                'leading_stock_name': row.get('领涨股票', ''),
                'leading_stock_code': row.get('领涨股票代码', ''),
                'leading_stock_price': float(row.get('领涨股票价格', 0)) if pd.notna(row.get('领涨股票价格')) else None,
                'leading_stock_change_pct': float(row.get('领涨股票涨跌幅', 0)) if pd.notna(row.get('领涨股票涨跌幅')) else None,
            }
            rankings_data.append(ranking_data)

        return rankings_data
    
    def _bulk_insert_rankings(self, rankings_data: List[Dict[str, Any]]) -> bool:
        """
        批量插入排名数据
        
        Args:
            rankings_data: 排名数据列表
            
        Returns:
            bool: 插入是否成功
        """
        try:
            for data in rankings_data:
                ranking = SectorDailyRanking(**data)
                db.session.add(ranking)
            
            db.session.commit()
            logger.info(f"成功插入 {len(rankings_data)} 条排名数据")
            return True
            
        except Exception as e:
            logger.error(f"批量插入排名数据失败: {e}")
            db.session.rollback()
            return False
    
    def get_rankings_by_date(self, target_date: date) -> List[Dict[str, Any]]:
        """
        获取指定日期的排名数据

        Args:
            target_date: 目标日期

        Returns:
            List[Dict]: 排名数据列表
        """
        try:
            rankings = SectorDailyRanking.get_rankings_by_date(target_date)
            return [ranking.to_dict() for ranking in rankings]
        except Exception as e:
            logger.error(f"获取 {target_date} 排名数据失败: {e}")
            return []

    def get_rankings_by_range(self, start_date: date, end_date: date) -> List[Dict[str, Any]]:
        """
        获取指定日期范围的排名数据

        Args:
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            List[Dict]: 排名数据列表
        """
        try:
            rankings = SectorDailyRanking.get_rankings_by_range(start_date, end_date)
            return [ranking.to_dict() for ranking in rankings]
        except Exception as e:
            logger.error(f"获取 {start_date} 到 {end_date} 排名数据失败: {e}")
            return []

    def get_active_sectors(self, days: int = 7, min_appearances: int = 3) -> List[Dict[str, Any]]:
        """
        获取连续活跃的板块

        Args:
            days: 查看最近几天
            min_appearances: 最少出现次数

        Returns:
            List[Dict]: 活跃板块列表
        """
        try:
            end_date = date.today()
            start_date = end_date - timedelta(days=days)

            active_sectors = SectorDailyRanking.get_active_sectors(
                start_date, end_date, min_appearances
            )

            # 转换为字典格式
            result = []
            for sector in active_sectors:
                result.append({
                    'sector_code': sector.sector_code,
                    'sector_name': sector.sector_name,
                    'appearances': sector.appearances,
                    'avg_ranking': float(sector.avg_ranking),
                    'best_ranking': sector.best_ranking
                })

            return result

        except Exception as e:
            logger.error(f"获取活跃板块失败: {e}")
            return []

    def get_latest_rankings(self, limit: int = 10) -> List[Dict[str, Any]]:
        """
        获取最新的排名数据

        Args:
            limit: 返回数量限制

        Returns:
            List[Dict]: 最新排名数据
        """
        try:
            rankings = SectorDailyRanking.get_latest_rankings(limit)
            return [ranking.to_dict() for ranking in rankings]
        except Exception as e:
            logger.error(f"获取最新排名数据失败: {e}")
            return []

    def collect_sector_rankings(self, target_date: str = None, top_n: int = 10) -> Dict[str, Any]:
        """
        API接口兼容方法
        """
        if target_date:
            target_date_obj = datetime.strptime(target_date, '%Y-%m-%d').date()
        else:
            target_date_obj = date.today()

        success = self.collect_daily_rankings(target_date_obj, top_n)

        return {
            'success': success,
            'target_date': target_date_obj.strftime('%Y-%m-%d'),
            'collected_count': top_n if success else 0
        }

# 创建全局服务实例
sector_ranking_service = SectorRankingService()
