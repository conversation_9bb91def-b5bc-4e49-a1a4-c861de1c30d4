#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接测试数据收集过程，添加详细调试信息
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from datetime import date
from services.sector_ranking_service import SectorRankingService
from models.sector_ranking import SectorDailyRanking
from database import db
from app import create_app

def test_direct_collection():
    """直接测试数据收集过程"""
    print("=" * 80)
    print("直接测试数据收集过程")
    print("=" * 80)
    
    # 创建应用上下文
    app = create_app()
    
    with app.app_context():
        try:
            service = SectorRankingService()
            test_date = date(2025, 7, 11)
            
            print(f"测试日期: {test_date}")
            print()
            
            # 步骤1: 删除现有数据
            print("1. 删除现有数据...")
            existing_count = SectorDailyRanking.query.filter_by(ranking_date=test_date).count()
            print(f"   现有数据: {existing_count} 条")
            
            if existing_count > 0:
                SectorDailyRanking.query.filter_by(ranking_date=test_date).delete()
                db.session.commit()
                print(f"   ✅ 已删除 {existing_count} 条旧数据")
            
            # 步骤2: 获取实时数据作为备选
            print(f"\n2. 获取实时数据作为备选...")
            sector_data = service.data_service.get_enhanced_sector_realtime_data()
            
            if not sector_data.empty:
                print(f"   ✅ 获取到 {len(sector_data)} 个板块的实时数据")
                
                # 查看证券板块的数据
                securities_data = sector_data[sector_data['板块代码'] == 'BK0473']
                if not securities_data.empty:
                    sector_row = securities_data.iloc[0]
                    print(f"\n   证券板块实时数据:")
                    print(f"     板块名称: {sector_row.get('板块名称')}")
                    print(f"     涨跌幅: {sector_row.get('涨跌幅')}")
                    print(f"     领涨股票: {sector_row.get('领涨股票')}")
                    print(f"     领涨股票代码: {sector_row.get('领涨股票代码')}")
                    print(f"     领涨股票价格: {sector_row.get('领涨股票价格')}")
                    print(f"     领涨股票-涨跌幅: {sector_row.get('领涨股票-涨跌幅')}")
            else:
                print("   ❌ 未获取到实时数据")
                return
            
            # 步骤3: 数据预处理
            print(f"\n3. 数据预处理...")
            processed_data = service._process_sector_data(sector_data)
            
            if not processed_data.empty:
                print(f"   ✅ 处理后数据 {len(processed_data)} 条")
            else:
                print("   ❌ 处理后数据为空")
                return
            
            # 步骤4: 筛选前3名
            print(f"\n4. 筛选前3名...")
            top_sectors = processed_data.nlargest(3, '涨跌幅')
            print(f"   ✅ 筛选出前3名板块")
            
            for i, (_, row) in enumerate(top_sectors.iterrows()):
                print(f"\n   第{i+1}名: {row.get('板块名称')} ({row.get('板块代码')})")
                print(f"     涨跌幅: {row.get('涨跌幅'):.2f}%")
                print(f"     领涨股票: '{row.get('领涨股票')}'")
                print(f"     领涨股票代码: '{row.get('领涨股票代码')}'")
                print(f"     领涨股票价格: {row.get('领涨股票价格')}")
                print(f"     领涨股票-涨跌幅: {row.get('领涨股票-涨跌幅')}")
            
            # 步骤5: 转换为数据库格式
            print(f"\n5. 转换为数据库格式...")
            rankings_data = service._convert_to_ranking_data(top_sectors, test_date)
            
            print(f"   ✅ 转换完成，共 {len(rankings_data)} 条记录")
            
            for i, data in enumerate(rankings_data):
                print(f"\n   记录{i+1}: {data.get('sector_name')} ({data.get('sector_code')})")
                print(f"     排名: {data.get('ranking')}")
                print(f"     涨跌幅: {data.get('price_change_pct'):.2f}%")
                print(f"     领涨股票名称: '{data.get('leading_stock_name')}'")
                print(f"     领涨股票代码: '{data.get('leading_stock_code')}'")
                print(f"     领涨股票价格: {data.get('leading_stock_price')}")
                print(f"     领涨股票涨跌幅: {data.get('leading_stock_change_pct')}")
            
            # 步骤6: 插入数据库
            print(f"\n6. 插入数据库...")
            success = service._bulk_insert_rankings(rankings_data)
            
            if success:
                print(f"   ✅ 数据插入成功")
            else:
                print(f"   ❌ 数据插入失败")
                return
            
            # 步骤7: 验证数据库中的数据
            print(f"\n7. 验证数据库中的数据...")
            rankings = SectorDailyRanking.query.filter_by(ranking_date=test_date).order_by(SectorDailyRanking.ranking).all()
            
            if rankings:
                print(f"   ✅ 数据库中找到 {len(rankings)} 条记录")
                
                for ranking in rankings:
                    print(f"\n   数据库记录 {ranking.ranking}: {ranking.sector_name} ({ranking.sector_code})")
                    print(f"     涨跌幅: {ranking.price_change_pct:.2f}%")
                    print(f"     领涨股票名称: '{ranking.leading_stock_name}'")
                    print(f"     领涨股票代码: '{ranking.leading_stock_code}'")
                    print(f"     领涨股票价格: {ranking.leading_stock_price}")
                    print(f"     领涨股票涨跌幅: {ranking.leading_stock_change_pct}")
                    
                    # 检查是否为占位符
                    if ranking.leading_stock_name and '_领涨股' in ranking.leading_stock_name:
                        print(f"     ⚠️  这是占位符数据！")
                    elif ranking.leading_stock_name and ranking.leading_stock_name.strip():
                        print(f"     ✅ 这是真实的股票名称")
                    else:
                        print(f"     ❌ 领涨股票名称为空")
            else:
                print(f"   ❌ 数据库中未找到数据")
            
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    test_direct_collection()
