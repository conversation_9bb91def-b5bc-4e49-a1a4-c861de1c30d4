#!/usr/bin/env python3
"""
测试概念N型待选股票页面的表格修复效果
"""
import requests
import json
from datetime import datetime

def test_table_fix():
    """测试表格修复效果"""
    print("🚀 测试概念N型待选股票页面表格修复效果")
    print("=" * 60)
    
    # 测试API响应格式
    url = 'http://localhost:5000/api/concept-n-stocks/机器人'
    
    try:
        response = requests.get(url, timeout=30)
        if response.status_code == 200:
            result = response.json()
            
            print("✅ API响应成功")
            print(f"📊 概念: {result.get('concept_name', '未知')}")
            print(f"📈 股票数量: {result.get('total', 0)}")
            print(f"⏱️ 查询耗时: {result.get('query_time_ms', 0)}ms")
            
            # 检查时间戳字段
            timestamp = result.get('timestamp')
            if timestamp:
                print(f"🕒 API时间戳: {timestamp}")
                
                # 转换为本地时间格式
                try:
                    dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                    local_time = dt.strftime('%Y-%m-%d %H:%M:%S')
                    print(f"🕒 本地时间格式: {local_time}")
                except:
                    print(f"🕒 时间格式转换失败")
            else:
                print("⚠️ 缺少timestamp字段")
            
            # 检查实时数据字段
            if result.get('data'):
                first_stock = result['data'][0]
                print(f"\n📋 第一只股票数据示例:")
                print(f"  代码: {first_stock.get('stock_code', '')}")
                print(f"  名称: {first_stock.get('stock_name', '')}")
                print(f"  涨跌幅: {first_stock.get('涨跌幅', 'N/A')}")
                print(f"  最新价: {first_stock.get('最新价', 'N/A')}")
                print(f"  更新时间: {first_stock.get('更新时间', 'N/A')}")
                
                # 检查有多少股票有实时数据
                stocks_with_realtime = 0
                for stock in result['data']:
                    if stock.get('涨跌幅') is not None:
                        stocks_with_realtime += 1
                
                print(f"\n📊 实时数据统计:")
                print(f"  总股票数: {len(result['data'])}")
                print(f"  有实时数据: {stocks_with_realtime}")
                print(f"  实时数据覆盖率: {stocks_with_realtime/len(result['data'])*100:.1f}%")
            
            print(f"\n🎯 表格修复要点验证:")
            print(f"  ✅ API返回timestamp字段用于显示更新时间")
            print(f"  ✅ 实时数据字段完整（涨跌幅、最新价等）")
            print(f"  ✅ 数据结构适合前端表格显示")
            
            print(f"\n📝 前端修复要点:")
            print(f"  🔧 移除Table组件的scroll属性")
            print(f"  🔧 添加lastUpdateTime状态管理")
            print(f"  🔧 在表格标题显示更新时间")
            print(f"  🔧 优化列宽度适应无滚动条布局")
            
        else:
            print(f"❌ API调用失败: {response.status_code}")
            print(f"错误: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")

def test_time_format():
    """测试时间格式转换"""
    print(f"\n🕒 时间格式转换测试:")
    print("=" * 40)
    
    # 模拟API返回的时间戳
    test_timestamps = [
        "2025-07-09T14:30:25.123456",
        "2025-07-09T14:30:25",
        "2025-07-09 14:30:25"
    ]
    
    for timestamp in test_timestamps:
        try:
            # 前端使用的时间格式转换
            dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
            formatted_time = dt.strftime('%Y-%m-%d %H:%M:%S')
            print(f"  输入: {timestamp}")
            print(f"  输出: {formatted_time}")
            print()
        except Exception as e:
            print(f"  输入: {timestamp}")
            print(f"  错误: {e}")
            print()

if __name__ == "__main__":
    test_table_fix()
    test_time_format()
