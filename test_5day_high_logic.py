#!/usr/bin/env python3
"""
测试5日新高计算逻辑的一致性
验证前端和后端的计算结果是否一致
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

import pandas as pd
from datetime import datetime, date, timedelta
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_5day_high_logic():
    """测试5日新高计算逻辑"""
    
    print("🧪 测试5日新高计算逻辑的一致性")
    print("=" * 50)
    
    # 创建测试数据（模拟降序排列的quotes数据）
    test_quotes = [
        {'quote_date': '2025-01-07', 'close_price': 105.0, 'high_price': 106.0},  # 最新
        {'quote_date': '2025-01-06', 'close_price': 103.0, 'high_price': 104.0},
        {'quote_date': '2025-01-05', 'close_price': 102.0, 'high_price': 103.0},
        {'quote_date': '2025-01-04', 'close_price': 101.0, 'high_price': 102.0},
        {'quote_date': '2025-01-03', 'close_price': 100.0, 'high_price': 101.0},  # 第5天
        {'quote_date': '2025-01-02', 'close_price': 99.0, 'high_price': 100.0},   # 更早的数据
    ]
    
    print("📊 测试数据（降序排列，最新在前）：")
    for i, quote in enumerate(test_quotes):
        print(f"  {i}: {quote['quote_date']} - 收盘价: {quote['close_price']}, 最高价: {quote['high_price']}")
    
    # 1. 前端逻辑（修复后）
    print("\n🔧 前端逻辑（修复后）：")
    latest_quote = test_quotes[0]  # 最新数据在开头
    recent_quotes = test_quotes[0:5]  # 取前5条最新数据
    recent_5day_high = max(q['close_price'] for q in recent_quotes)  # 使用收盘价
    is_new_5day_high_frontend = latest_quote['close_price'] >= recent_5day_high
    
    print(f"  最新收盘价: {latest_quote['close_price']}")
    print(f"  近5日收盘价: {[q['close_price'] for q in recent_quotes]}")
    print(f"  近5日收盘价最高值: {recent_5day_high}")
    print(f"  是否5日新高: {is_new_5day_high_frontend}")
    
    # 2. 后端逻辑（模拟）
    print("\n⚙️ 后端逻辑（模拟）：")
    # 后端接收的是升序排列的DataFrame
    df_data = []
    for quote in reversed(test_quotes):  # 转换为升序排列
        df_data.append({
            'quote_date': quote['quote_date'],
            '收盘': quote['close_price'],
            '最高': quote['high_price']
        })
    
    df = pd.DataFrame(df_data)
    print(f"  DataFrame（升序排列）:")
    for i, row in df.iterrows():
        print(f"    {i}: {row['quote_date']} - 收盘: {row['收盘']}")
    
    # 模拟后端的5日新高计算
    current_price = df['收盘'].iloc[-1]  # 最新收盘价
    period_data = df.tail(5)  # 最近5天数据
    max_price = period_data['收盘'].max()  # 5日收盘价最高值
    is_new_5day_high_backend = current_price >= max_price
    
    print(f"  当前收盘价: {current_price}")
    print(f"  近5日收盘价: {period_data['收盘'].tolist()}")
    print(f"  近5日收盘价最高值: {max_price}")
    print(f"  是否5日新高: {is_new_5day_high_backend}")
    
    # 3. 结果对比
    print("\n📋 结果对比：")
    print(f"  前端结果: {is_new_5day_high_frontend}")
    print(f"  后端结果: {is_new_5day_high_backend}")
    print(f"  结果一致: {is_new_5day_high_frontend == is_new_5day_high_backend}")
    
    if is_new_5day_high_frontend == is_new_5day_high_backend:
        print("✅ 前端和后端的5日新高计算逻辑一致！")
        return True
    else:
        print("❌ 前端和后端的5日新高计算逻辑不一致！")
        return False

def test_edge_cases():
    """测试边界情况"""
    
    print("\n🧪 测试边界情况")
    print("=" * 30)
    
    # 测试案例1：当前价格正好等于5日最高价
    test_quotes_equal = [
        {'quote_date': '2025-01-07', 'close_price': 105.0},  # 最新，等于最高价
        {'quote_date': '2025-01-06', 'close_price': 105.0},  # 之前的最高价
        {'quote_date': '2025-01-05', 'close_price': 103.0},
        {'quote_date': '2025-01-04', 'close_price': 102.0},
        {'quote_date': '2025-01-03', 'close_price': 101.0},
    ]
    
    latest_quote = test_quotes_equal[0]
    recent_quotes = test_quotes_equal[0:5]
    recent_5day_high = max(q['close_price'] for q in recent_quotes)
    is_new_high = latest_quote['close_price'] >= recent_5day_high
    
    print(f"案例1 - 当前价格等于5日最高价:")
    print(f"  当前收盘价: {latest_quote['close_price']}")
    print(f"  5日最高价: {recent_5day_high}")
    print(f"  是否新高: {is_new_high} (应该为True)")
    
    # 测试案例2：当前价格低于5日最高价
    test_quotes_lower = [
        {'quote_date': '2025-01-07', 'close_price': 104.0},  # 最新，低于最高价
        {'quote_date': '2025-01-06', 'close_price': 105.0},  # 之前的最高价
        {'quote_date': '2025-01-05', 'close_price': 103.0},
        {'quote_date': '2025-01-04', 'close_price': 102.0},
        {'quote_date': '2025-01-03', 'close_price': 101.0},
    ]
    
    latest_quote = test_quotes_lower[0]
    recent_quotes = test_quotes_lower[0:5]
    recent_5day_high = max(q['close_price'] for q in recent_quotes)
    is_new_high = latest_quote['close_price'] >= recent_5day_high
    
    print(f"\n案例2 - 当前价格低于5日最高价:")
    print(f"  当前收盘价: {latest_quote['close_price']}")
    print(f"  5日最高价: {recent_5day_high}")
    print(f"  是否新高: {is_new_high} (应该为False)")

if __name__ == "__main__":
    try:
        print("🚀 开始测试5日新高计算逻辑")
        
        # 基本逻辑测试
        basic_test_passed = test_5day_high_logic()
        
        # 边界情况测试
        test_edge_cases()
        
        print("\n" + "=" * 50)
        if basic_test_passed:
            print("✅ 所有测试通过！5日新高计算逻辑修复正确。")
        else:
            print("❌ 测试失败！需要进一步检查逻辑。")
            
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
