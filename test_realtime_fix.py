#!/usr/bin/env python3
"""
测试修复后的实时涨跌幅功能
"""
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

import akshare as ak
import json
from datetime import datetime

def convert_stock_code_to_akshare_format(stock_code):
    """
    将股票代码转换为AkShare个股查询API所需的格式
    """
    if len(stock_code) != 6:
        return None
    
    if stock_code.startswith('6'):
        return f"SH{stock_code}"  # 上海股票
    elif stock_code.startswith('0') or stock_code.startswith('3'):
        return f"SZ{stock_code}"  # 深圳股票
    elif stock_code.startswith('4') or stock_code.startswith('8'):
        return f"BJ{stock_code}"  # 北京股票
    else:
        return None

def get_stock_realtime_data_akshare(stock_code):
    """
    使用AkShare个股查询API获取单只股票的实时数据
    """
    try:
        # 转换股票代码格式
        akshare_code = convert_stock_code_to_akshare_format(stock_code)
        if not akshare_code:
            print(f"无法转换股票代码格式: {stock_code}")
            return None
        
        print(f"正在获取股票 {stock_code} ({akshare_code}) 的实时数据...")
        
        # 调用AkShare API获取个股数据
        stock_data = ak.stock_individual_spot_xq(symbol=akshare_code)
        
        if stock_data is None or stock_data.empty:
            print(f"获取股票 {akshare_code} 数据为空")
            return None
        
        # 将DataFrame转换为字典格式
        stock_dict = dict(zip(stock_data['item'], stock_data['value']))
        
        # 提取需要的字段
        result = {
            '涨跌幅': float(stock_dict.get('涨幅', 0.0)),  # 涨幅字段 → 涨跌幅
            '最新价': float(stock_dict.get('现价', 0.0)),  # 现价字段 → 最新价
            '涨跌额': float(stock_dict.get('涨跌', 0.0)),  # 涨跌字段 → 涨跌额
            '更新时间': stock_dict.get('时间', ''),        # 时间字段 → 更新时间
            '股票名称': stock_dict.get('名称', ''),        # 名称字段
            '昨收价': float(stock_dict.get('昨收', 0.0)),  # 昨收字段
        }
        
        print(f"✅ 成功获取股票 {stock_code} 实时数据:")
        print(f"   股票名称: {result['股票名称']}")
        print(f"   涨跌幅: {result['涨跌幅']}%")
        print(f"   最新价: {result['最新价']}")
        print(f"   涨跌额: {result['涨跌额']}")
        print(f"   更新时间: {result['更新时间']}")
        return result
        
    except Exception as e:
        print(f"❌ 获取股票 {stock_code} 实时数据失败: {e}")
        return None

def test_multiple_stocks():
    """测试多只股票的实时数据获取"""
    test_stocks = ['000001', '600000', '000002', '300348', '000710']
    
    print("=" * 60)
    print("🚀 测试修复后的实时涨跌幅功能")
    print("=" * 60)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"测试股票: {', '.join(test_stocks)}")
    print()
    
    results = {}
    successful_count = 0
    
    for stock_code in test_stocks:
        print(f"\n📊 测试股票: {stock_code}")
        print("-" * 40)
        
        realtime_data = get_stock_realtime_data_akshare(stock_code)
        if realtime_data:
            results[stock_code] = realtime_data
            successful_count += 1
        else:
            results[stock_code] = {
                '涨跌幅': None,
                '最新价': None,
                '涨跌额': None,
                '更新时间': '',
                '股票名称': '',
                '昨收价': None
            }
    
    print("\n" + "=" * 60)
    print("📈 测试结果汇总")
    print("=" * 60)
    print(f"总测试股票数: {len(test_stocks)}")
    print(f"成功获取数据: {successful_count}")
    print(f"成功率: {successful_count/len(test_stocks)*100:.1f}%")
    
    print("\n📋 详细结果:")
    for stock_code, data in results.items():
        if data['涨跌幅'] is not None:
            print(f"  {stock_code} ({data['股票名称']}): {data['涨跌幅']:+.2f}% | ¥{data['最新价']:.2f}")
        else:
            print(f"  {stock_code}: 数据获取失败")
    
    print("\n🔧 API响应格式示例:")
    print(json.dumps({
        "success": True,
        "data": {
            stock_code: {
                "n_shape_status": {"exists": False},
                "large_buy_status": {"exists": False},
                "realtime_data": data
            } for stock_code, data in list(results.items())[:2]
        },
        "summary": {
            "total_stocks": len(test_stocks),
            "realtime_success": successful_count
        }
    }, ensure_ascii=False, indent=2))

if __name__ == "__main__":
    test_multiple_stocks()
