"""
定时任务服务 - 处理数据的定时更新
"""
import schedule
import time
import threading
from datetime import datetime
import logging
from services.database_service import database_service

logger = logging.getLogger(__name__)

class SchedulerService:
    """定时任务服务类"""
    
    def __init__(self):
        self.running = False
        self.thread = None
        self.start_time = None
    
    def start(self):
        """启动定时任务"""
        if self.running:
            logger.warning("定时任务已在运行")
            return
        
        self.running = True
        self.start_time = datetime.now()

        # 配置定时任务
        self._setup_schedules()

        # 启动后台线程
        self.thread = threading.Thread(target=self._run_scheduler, daemon=True)
        self.thread.start()

        logger.info("定时任务服务已启动")
    
    def stop(self):
        """停止定时任务"""
        self.running = False
        if self.thread:
            self.thread.join(timeout=5)
        schedule.clear()
        logger.info("定时任务服务已停止")

    def is_running(self):
        """检查调度器是否正在运行"""
        return self.running and self.thread and self.thread.is_alive()
    
    def _setup_schedules(self):
        """设置定时任务"""
        # 工作日市场开盘前更新数据（9:00）
        schedule.every().monday.at("09:00").do(self._update_all_data_if_trading_day)
        schedule.every().tuesday.at("09:00").do(self._update_all_data_if_trading_day)
        schedule.every().wednesday.at("09:00").do(self._update_all_data_if_trading_day)
        schedule.every().thursday.at("09:00").do(self._update_all_data_if_trading_day)
        schedule.every().friday.at("09:00").do(self._update_all_data_if_trading_day)

        # 工作日收盘后更新数据（15:30）
        schedule.every().monday.at("15:30").do(self._update_all_data_if_trading_day)
        schedule.every().tuesday.at("15:30").do(self._update_all_data_if_trading_day)
        schedule.every().wednesday.at("15:30").do(self._update_all_data_if_trading_day)
        schedule.every().thursday.at("15:30").do(self._update_all_data_if_trading_day)
        schedule.every().friday.at("15:30").do(self._update_all_data_if_trading_day)

        # 每小时检查一次（交易时间内）
        for hour in range(9, 16):
            schedule.every().monday.at(f"{hour:02d}:30").do(self._update_realtime_data)
            schedule.every().tuesday.at(f"{hour:02d}:30").do(self._update_realtime_data)
            schedule.every().wednesday.at(f"{hour:02d}:30").do(self._update_realtime_data)
            schedule.every().thursday.at(f"{hour:02d}:30").do(self._update_realtime_data)
            schedule.every().friday.at(f"{hour:02d}:30").do(self._update_realtime_data)

        # 每天凌晨清理缓存
        schedule.every().day.at("02:00").do(self._clear_cache)

        # 工作日收盘后收集板块排名数据（15:30）
        schedule.every().monday.at("15:30").do(self._collect_sector_rankings_if_trading_day)
        schedule.every().tuesday.at("15:30").do(self._collect_sector_rankings_if_trading_day)
        schedule.every().wednesday.at("15:30").do(self._collect_sector_rankings_if_trading_day)
        schedule.every().thursday.at("15:30").do(self._collect_sector_rankings_if_trading_day)
        schedule.every().friday.at("15:30").do(self._collect_sector_rankings_if_trading_day)

        logger.info("定时任务已配置完成")
    
    def _run_scheduler(self):
        """运行定时任务调度器"""
        while self.running:
            try:
                schedule.run_pending()
                time.sleep(60)  # 每分钟检查一次
            except Exception as e:
                logger.error(f"定时任务执行出错: {e}")
                time.sleep(60)

    def _update_all_data_if_trading_day(self):
        """仅在交易日更新所有数据"""
        try:
            from utils.trading_calendar import trading_calendar

            # 检查是否为交易日
            if not trading_calendar.is_trading_day(datetime.now().date()):
                logger.debug("非交易日，跳过全量数据更新")
                return

            self._update_all_data()

        except Exception as e:
            logger.error(f"交易日检查失败: {e}")

    def _update_all_data(self):
        """更新所有数据"""
        try:
            logger.info("开始执行全量数据更新")
            start_time = datetime.now()
            
            result = database_service.update_all_sectors()
            
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            logger.info(f"全量数据更新完成，耗时 {duration:.2f} 秒")
            logger.info(f"更新结果: 成功 {result['success_count']}/{result['total_sectors']} 个板块")
            
            if result['failed_sectors']:
                logger.warning(f"更新失败的板块: {result['failed_sectors']}")
            
        except Exception as e:
            logger.error(f"全量数据更新失败: {e}")
    
    def _update_realtime_data(self):
        """更新实时数据（仅在交易时间内）"""
        try:
            from utils.trading_calendar import trading_calendar

            # 检查是否在交易时间内
            if not trading_calendar.is_trading_time():
                logger.debug("非交易时间，跳过实时数据更新")
                return

            logger.info("开始执行实时数据更新")
            start_time = datetime.now()

            # 这里可以实现更轻量级的实时数据更新
            # 例如只更新最新的行情数据，不重新计算技术分析
            from services.data_service import data_service
            data_service.clear_cache()  # 清理缓存以获取最新数据

            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()

            logger.info(f"实时数据更新完成，耗时 {duration:.2f} 秒")

        except Exception as e:
            logger.error(f"实时数据更新失败: {e}")
    
    def _clear_cache(self):
        """清理缓存"""
        try:
            logger.info("开始清理缓存")
            from services.data_service import data_service
            data_service.clear_cache()
            logger.info("缓存清理完成")
        except Exception as e:
            logger.error(f"缓存清理失败: {e}")

    def _collect_sector_rankings_if_trading_day(self):
        """仅在交易日收集板块排名数据"""
        try:
            from utils.trading_calendar import trading_calendar

            # 检查是否为交易日
            if not trading_calendar.is_trading_day(datetime.now().date()):
                logger.debug("非交易日，跳过板块排名数据收集")
                return

            self._collect_sector_rankings()

        except Exception as e:
            logger.error(f"交易日检查失败: {e}")

    def _collect_sector_rankings(self):
        """收集板块排名数据"""
        try:
            logger.info("开始收集板块排名数据")
            start_time = datetime.now()

            from services.sector_ranking_service import sector_ranking_service

            # 收集当日排名前10的板块数据
            success = sector_ranking_service.collect_daily_rankings(
                target_date=datetime.now().date(),
                top_n=10
            )

            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()

            if success:
                logger.info(f"板块排名数据收集完成，耗时 {duration:.2f} 秒")
            else:
                logger.error(f"板块排名数据收集失败，耗时 {duration:.2f} 秒")

            return success

        except Exception as e:
            logger.error(f"板块排名数据收集失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def manual_update(self, sector_code: str = None):
        """手动触发更新"""
        try:
            if sector_code:
                logger.info(f"手动更新板块: {sector_code}")
                success = database_service.update_sector_quotes(sector_code)
                if success:
                    success = database_service.update_sector_analysis(sector_code)
                return success
            else:
                logger.info("手动触发全量更新")
                result = database_service.update_all_sectors()
                return result
        except Exception as e:
            logger.error(f"手动更新失败: {e}")
            return False

    def manual_collect_sector_rankings(self, target_date=None, top_n=10):
        """手动触发板块排名数据收集"""
        try:
            logger.info(f"手动触发板块排名数据收集（前{top_n}名）")

            from services.sector_ranking_service import sector_ranking_service

            if target_date is None:
                target_date = datetime.now().date()

            success = sector_ranking_service.collect_daily_rankings(
                target_date=target_date,
                top_n=top_n
            )

            if success:
                logger.info(f"手动板块排名数据收集成功")
            else:
                logger.error(f"手动板块排名数据收集失败")

            return success

        except Exception as e:
            logger.error(f"手动板块排名数据收集失败: {e}")
            return False
    
    def get_next_run_time(self):
        """获取下次运行时间"""
        try:
            next_run = schedule.next_run()
            return next_run.isoformat() if next_run else None
        except Exception:
            return None
    
    def get_job_status(self):
        """获取任务状态"""
        jobs = []
        data_update_jobs = []
        ranking_collection_jobs = []
        cache_jobs = []
        realtime_jobs = []

        for job in schedule.jobs:
            job_info = {
                'job': str(job.job_func),
                'next_run': job.next_run.isoformat() if job.next_run else None,
                'interval': str(job.interval),
                'unit': job.unit
            }

            # 分类任务
            job_name = str(job.job_func)
            if '_update_all_data_if_trading_day' in job_name:
                data_update_jobs.append(job_info)
            elif '_collect_sector_rankings_if_trading_day' in job_name:
                ranking_collection_jobs.append(job_info)
            elif '_update_realtime_data' in job_name:
                realtime_jobs.append(job_info)
            elif '_clear_cache' in job_name:
                cache_jobs.append(job_info)
            else:
                jobs.append(job_info)

        return {
            'running': self.running,
            'jobs_count': len(schedule.jobs),
            'next_run': self.get_next_run_time(),
            'jobs_by_category': {
                'data_update': data_update_jobs,
                'ranking_collection': ranking_collection_jobs,
                'realtime_update': realtime_jobs,
                'cache_management': cache_jobs,
                'other': jobs
            },
            'summary': {
                'data_update_jobs': len(data_update_jobs),
                'ranking_collection_jobs': len(ranking_collection_jobs),
                'realtime_jobs': len(realtime_jobs),
                'cache_jobs': len(cache_jobs),
                'other_jobs': len(jobs)
            }
        }

# 全局调度器实例
scheduler_service = SchedulerService()

def start_scheduler():
    """启动调度器的便捷函数"""
    scheduler_service.start()

def stop_scheduler():
    """停止调度器的便捷函数"""
    scheduler_service.stop()
