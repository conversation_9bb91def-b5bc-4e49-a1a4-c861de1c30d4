#!/usr/bin/env python3
"""
测试概念N型待选股票API
"""
import requests
import json
import time

def test_concept_api():
    # 等待服务器启动
    time.sleep(2)

    # 测试概念N型待选股票API
    url = 'http://localhost:5000/api/concept-n-stocks/机器人'

    try:
        print('🚀 测试概念N型待选股票API...')
        response = requests.get(url, timeout=60)
        print(f'状态码: {response.status_code}')
        if response.status_code == 200:
            result = response.json()
            print('✅ API响应成功!')
            print(f'📊 概念: {result["concept_name"]}')
            print(f'📈 股票数量: {result["total"]}')
            print(f'⏱️ 查询耗时: {result["query_time_ms"]}ms')
            print(f'🔄 实时数据成功: {result["realtime_success"]}')
            print()
            print('📋 股票详情:')
            for stock in result['data']:
                change = stock.get('涨跌幅')
                price = stock.get('最新价')
                if change is not None and price is not None:
                    print(f'  {stock["stock_code"]} ({stock["stock_name"]}): {change:+.2f}% | ¥{price:.2f}')
                else:
                    print(f'  {stock["stock_code"]} ({stock["stock_name"]}): 实时数据获取失败')
            
            # 检查是否有涨跌幅数据
            has_realtime_data = any(stock.get('涨跌幅') is not None for stock in result['data'])
            if has_realtime_data:
                print('\n🎉 修复成功！概念N型待选股票API现在包含实时涨跌幅数据')
            else:
                print('\n⚠️ 实时数据获取失败')
                
            # 显示API响应格式示例
            print('\n🔧 API响应格式示例:')
            sample_data = result['data'][0] if result['data'] else {}
            print(json.dumps({
                "success": True,
                "data": [sample_data],
                "total": result["total"],
                "concept_name": result["concept_name"],
                "realtime_success": result["realtime_success"]
            }, ensure_ascii=False, indent=2))
                
        else:
            print(f'❌ API调用失败: {response.status_code}')
            print(f'错误: {response.text}')
    except Exception as e:
        print(f'❌ 请求失败: {e}')

if __name__ == "__main__":
    test_concept_api()
