#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速性能测试脚本
"""

import requests
import time
from datetime import datetime

def quick_test():
    print("🚀 快速性能测试")
    print("=" * 40)
    
    start_time = time.time()
    start_datetime = datetime.now()
    print(f"开始时间: {start_datetime.strftime('%H:%M:%S')}")
    
    try:
        response = requests.post(
            'http://localhost:5000/api/data/update', 
            json={'update_mode': 'incremental', 'force_user_update': True}, 
            timeout=420
        )
        
        end_time = time.time()
        end_datetime = datetime.now()
        elapsed = end_time - start_time
        
        print(f"结束时间: {end_datetime.strftime('%H:%M:%S')}")
        print(f"执行时间: {elapsed:.2f} 秒 ({elapsed/60:.2f} 分钟)")
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"成功: {data.get('success', False)}")
            print(f"消息: {data.get('message', '无消息')}")
            
            # 性能对比
            previous_time = 172.73  # 之前的时间
            improvement = (previous_time - elapsed) / previous_time * 100
            print(f"\n📊 性能对比:")
            print(f"优化前: {previous_time:.2f} 秒")
            print(f"优化后: {elapsed:.2f} 秒")
            print(f"提升: {improvement:.1f}%")
            
        else:
            print(f"错误: {response.text[:200]}")
            
    except Exception as e:
        print(f"测试失败: {e}")

if __name__ == "__main__":
    quick_test()
