"""
板块排名数据收集服务
实现每日收盘后自动获取板块数据并筛选排名前10的板块存储到数据库
"""
import logging
import pandas as pd
from datetime import datetime, date, timedelta
from typing import List, Dict, Any, Optional
from database import db
from models.sector_ranking import SectorDailyRanking
from services.data_service import DataService
from utils.trading_calendar import trading_calendar

logger = logging.getLogger(__name__)

class SectorRankingService:
    """板块排名数据收集服务"""
    
    def __init__(self):
        """初始化服务"""
        self.data_service = DataService()
        logger.info("板块排名数据收集服务初始化完成")
    
    def collect_daily_rankings(self, target_date: date = None, top_n: int = 10) -> bool:
        """
        收集每日板块排名数据
        
        Args:
            target_date: 目标日期，默认为今天
            top_n: 排名前N的板块，默认为10
            
        Returns:
            bool: 收集是否成功
        """
        if target_date is None:
            target_date = date.today()
        
        logger.info(f"开始收集 {target_date} 的板块排名数据（前{top_n}名）")
        
        try:
            # 检查是否为交易日
            if not trading_calendar.is_trading_day(target_date):
                logger.warning(f"{target_date} 不是交易日，跳过数据收集")
                return False
            
            # 检查是否已有当日数据
            existing_count = SectorDailyRanking.query.filter_by(ranking_date=target_date).count()
            if existing_count > 0:
                logger.info(f"{target_date} 的排名数据已存在（{existing_count}条），先清除旧数据")
                SectorDailyRanking.query.filter_by(ranking_date=target_date).delete()
                db.session.commit()
            
            # 获取板块实时数据
            logger.info("获取板块实时数据...")
            sector_data = self.data_service.get_enhanced_sector_realtime_data()
            
            if sector_data.empty:
                logger.error("无法获取板块数据")
                return False
            
            logger.info(f"成功获取 {len(sector_data)} 个板块数据")
            
            # 数据预处理和排序
            processed_data = self._process_sector_data(sector_data)
            
            if processed_data.empty:
                logger.error("数据处理后为空")
                return False
            
            # 按涨跌幅排序，筛选前N名
            top_sectors = processed_data.nlargest(top_n, '涨跌幅')
            logger.info(f"筛选出排名前{top_n}的板块")
            
            # 转换为数据库格式并批量插入
            rankings_data = self._convert_to_ranking_data(top_sectors, target_date)
            success = self._bulk_insert_rankings(rankings_data)
            
            if success:
                logger.info(f"成功收集并存储 {target_date} 的板块排名数据（{len(rankings_data)}条）")
                return True
            else:
                logger.error("数据存储失败")
                return False
                
        except Exception as e:
            logger.error(f"收集板块排名数据失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def _process_sector_data(self, sector_data: pd.DataFrame) -> pd.DataFrame:
        """
        处理板块数据，确保数据格式正确
        
        Args:
            sector_data: 原始板块数据
            
        Returns:
            pd.DataFrame: 处理后的数据
        """
        try:
            # 确保必要的列存在
            required_columns = ['板块代码', '板块名称', '涨跌幅', '最新价', '涨跌额', '成交量', '成交额']
            missing_columns = [col for col in required_columns if col not in sector_data.columns]
            
            if missing_columns:
                logger.warning(f"缺少必要的列: {missing_columns}")
            
            # 数据清洗：移除无效数据
            processed_data = sector_data.copy()
            
            # 确保涨跌幅为数值类型
            if '涨跌幅' in processed_data.columns:
                processed_data['涨跌幅'] = pd.to_numeric(processed_data['涨跌幅'], errors='coerce')
            
            # 移除涨跌幅为空的行
            processed_data = processed_data.dropna(subset=['涨跌幅'])
            
            # 移除板块代码或名称为空的行
            processed_data = processed_data.dropna(subset=['板块代码', '板块名称'])
            
            logger.info(f"数据处理完成，有效数据 {len(processed_data)} 条")
            return processed_data
            
        except Exception as e:
            logger.error(f"数据处理失败: {e}")
            return pd.DataFrame()
    
    def _convert_to_ranking_data(self, top_sectors: pd.DataFrame, target_date: date) -> List[Dict[str, Any]]:
        """
        将板块数据转换为排名数据格式
        
        Args:
            top_sectors: 排名前N的板块数据
            target_date: 目标日期
            
        Returns:
            List[Dict]: 排名数据列表
        """
        rankings_data = []
        
        for index, (_, row) in enumerate(top_sectors.iterrows()):
            ranking_data = {
                'ranking_date': target_date,
                'sector_code': str(row.get('板块代码', '')),
                'sector_name': str(row.get('板块名称', '')),
                'ranking': index + 1,  # 排名从1开始
                'close_price': self._safe_float(row.get('最新价')),
                'price_change': self._safe_float(row.get('涨跌额')),
                'price_change_pct': self._safe_float(row.get('涨跌幅')),
                'volume': self._safe_int(row.get('成交量')),
                'turnover': self._safe_float(row.get('成交额')),
                'ma5': self._safe_float(row.get('MA5')),
                'ma10': self._safe_float(row.get('MA10')),
                'ma20': self._safe_float(row.get('MA20')),
                'trend_direction': str(row.get('趋势方向', '')),
                'consecutive_up_days': self._safe_int(row.get('连续上涨天数')),
                'is_new_high_5d': bool(row.get('创5日新高', False)),
                'is_new_high_20d': bool(row.get('创20日新高', False))
            }
            rankings_data.append(ranking_data)
        
        return rankings_data
    
    def _safe_float(self, value) -> Optional[float]:
        """安全转换为浮点数"""
        try:
            if pd.isna(value) or value is None or value == '':
                return None
            return float(value)
        except (ValueError, TypeError):
            return None
    
    def _safe_int(self, value) -> Optional[int]:
        """安全转换为整数"""
        try:
            if pd.isna(value) or value is None or value == '':
                return None
            return int(float(value))  # 先转float再转int，处理科学计数法
        except (ValueError, TypeError):
            return None
    
    def _bulk_insert_rankings(self, rankings_data: List[Dict[str, Any]]) -> bool:
        """
        批量插入排名数据
        
        Args:
            rankings_data: 排名数据列表
            
        Returns:
            bool: 插入是否成功
        """
        try:
            # 使用SQLAlchemy的bulk_insert_mappings进行批量插入
            db.session.bulk_insert_mappings(SectorDailyRanking, rankings_data)
            db.session.commit()
            logger.info(f"成功批量插入 {len(rankings_data)} 条排名数据")
            return True
            
        except Exception as e:
            logger.error(f"批量插入排名数据失败: {e}")
            db.session.rollback()
            return False
    
    def get_rankings_by_date(self, target_date: date) -> List[Dict[str, Any]]:
        """
        获取指定日期的排名数据
        
        Args:
            target_date: 目标日期
            
        Returns:
            List[Dict]: 排名数据列表
        """
        try:
            rankings = SectorDailyRanking.get_rankings_by_date(target_date)
            return [ranking.to_dict() for ranking in rankings]
        except Exception as e:
            logger.error(f"获取 {target_date} 排名数据失败: {e}")
            return []
    
    def get_rankings_by_range(self, start_date: date, end_date: date) -> List[Dict[str, Any]]:
        """
        获取指定日期范围的排名数据
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            List[Dict]: 排名数据列表
        """
        try:
            rankings = SectorDailyRanking.get_rankings_by_range(start_date, end_date)
            return [ranking.to_dict() for ranking in rankings]
        except Exception as e:
            logger.error(f"获取 {start_date} 到 {end_date} 排名数据失败: {e}")
            return []
    
    def get_active_sectors(self, days: int = 7, min_appearances: int = 3) -> List[Dict[str, Any]]:
        """
        获取连续活跃的板块
        
        Args:
            days: 查看最近几天
            min_appearances: 最少出现次数
            
        Returns:
            List[Dict]: 活跃板块列表
        """
        try:
            end_date = date.today()
            start_date = end_date - timedelta(days=days)
            
            active_sectors = SectorDailyRanking.get_active_sectors(
                start_date, end_date, min_appearances
            )
            
            # 转换为字典格式
            result = []
            for sector in active_sectors:
                result.append({
                    'sector_code': sector.sector_code,
                    'sector_name': sector.sector_name,
                    'appearances': sector.appearances,
                    'avg_ranking': float(sector.avg_ranking),
                    'best_ranking': sector.best_ranking
                })
            
            return result
            
        except Exception as e:
            logger.error(f"获取活跃板块失败: {e}")
            return []
    
    def get_latest_rankings(self, limit: int = 10) -> List[Dict[str, Any]]:
        """
        获取最新的排名数据
        
        Args:
            limit: 返回数量限制
            
        Returns:
            List[Dict]: 最新排名数据
        """
        try:
            rankings = SectorDailyRanking.get_latest_rankings(limit)
            return [ranking.to_dict() for ranking in rankings]
        except Exception as e:
            logger.error(f"获取最新排名数据失败: {e}")
            return []

# 创建全局服务实例
sector_ranking_service = SectorRankingService()
