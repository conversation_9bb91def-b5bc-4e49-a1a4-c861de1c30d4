import React from 'react'
import { Config<PERSON>rovider, App as AntdApp } from 'antd'
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'
import zhCN from 'antd/locale/zh_CN'
import Layout from './components/Layout'
import AllSectors from './components/AllSectors'
import AllSectorDetail from './components/AllSectorDetail'
import SectorStocksList from './components/SectorStocksList'
import Dashboard from './components/Dashboard'
import ConceptAnalysis from './components/ConceptAnalysis'
import ConceptStocks from './components/ConceptStocks'
import ConceptNStocks from './components/ConceptNStocks'
import SectorCalendar from './components/SectorCalendar'
import './App.css'

function App() {
  return (
    <ConfigProvider locale={zhCN}>
      <AntdApp>
        <Router>
          <Routes>
            <Route path="/" element={<Layout />}>
              <Route index element={<AllSectors />} />
              <Route path="sectors/:sectorCode" element={<AllSectorDetail />} />
              <Route path="sectors/:sectorCode/stocks" element={<SectorStocksList />} />
              <Route path="dashboard" element={<Dashboard />} />
              <Route path="concept-analysis" element={<ConceptAnalysis />} />
              <Route path="concept-stocks/:conceptName" element={<ConceptStocks />} />
              <Route path="concept-n-stocks/:conceptName" element={<ConceptNStocks />} />
              <Route path="sector-calendar" element={<SectorCalendar />} />
            </Route>
          </Routes>
        </Router>
      </AntdApp>
    </ConfigProvider>
  )
}

export default App