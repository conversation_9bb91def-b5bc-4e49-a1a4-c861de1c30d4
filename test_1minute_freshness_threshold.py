#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试1分钟数据新鲜度阈值设置

验证修改后的智能更新功能：
- 交易时间内数据新鲜度阈值从15分钟缩短到1分钟
- 1分钟以内的数据使用缓存
- 超过1分钟的数据触发更新
"""

import requests
import json
import time
from datetime import datetime, time as dt_time

def test_current_data_freshness():
    """测试当前数据的新鲜度状态"""
    print("📊 测试当前数据新鲜度状态")
    print("=" * 60)
    
    try:
        # 获取当前板块数据
        response = requests.get("http://localhost:5000/api/sectors/all-realtime", timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            
            if data.get('success') and data.get('data'):
                sectors = data['data']
                
                # 分析数据更新时间
                update_times = [s.get('数据更新时间') for s in sectors if s.get('数据更新时间')]
                
                if update_times:
                    latest_update_str = max(update_times)
                    latest_update = datetime.strptime(latest_update_str, '%Y-%m-%d %H:%M:%S')
                    current_time = datetime.now()
                    time_diff = current_time - latest_update
                    
                    print(f"最新数据更新时间: {latest_update_str}")
                    print(f"当前时间: {current_time.strftime('%Y-%m-%d %H:%M:%S')}")
                    print(f"时间差: {time_diff.total_seconds():.1f} 秒 ({time_diff.total_seconds()/60:.1f} 分钟)")
                    
                    # 判断交易时间
                    current_time_only = current_time.time()
                    morning_start = dt_time(9, 30)
                    morning_end = dt_time(11, 30)
                    afternoon_start = dt_time(13, 0)
                    afternoon_end = dt_time(15, 0)
                    
                    is_trading_time = (morning_start <= current_time_only <= morning_end) or \
                                     (afternoon_start <= current_time_only <= afternoon_end)
                    
                    print(f"是否交易时间: {is_trading_time}")
                    
                    # 根据新的1分钟阈值判断是否需要更新
                    if is_trading_time:
                        if time_diff.total_seconds() > 60:  # 1分钟阈值
                            print(f"🔄 数据已过时（{time_diff.total_seconds():.1f}秒 > 1分钟），应该触发更新")
                            should_update = True
                        else:
                            print(f"✅ 数据较新（{time_diff.total_seconds():.1f}秒 <= 1分钟），应该使用缓存")
                            should_update = False
                    else:
                        print("ℹ️  非交易时间，使用原有缓存策略")
                        should_update = time_diff.total_seconds() > 3600  # 非交易时间1小时阈值
                    
                    return {
                        'latest_update': latest_update_str,
                        'time_diff_seconds': time_diff.total_seconds(),
                        'time_diff_minutes': time_diff.total_seconds() / 60,
                        'is_trading_time': is_trading_time,
                        'should_update': should_update
                    }
                else:
                    print("❌ 没有找到数据更新时间")
                    return None
            else:
                print(f"❌ API返回失败: {data.get('error', '未知错误')}")
                return None
        else:
            print(f"❌ API调用失败，状态码: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ 数据新鲜度测试失败: {e}")
        return None

def test_smart_update_with_1minute_threshold():
    """测试1分钟阈值下的智能更新行为"""
    print(f"\n🔄 测试1分钟阈值下的智能更新行为")
    print("=" * 60)
    
    try:
        # 1. 获取更新前的数据状态
        print("1. 获取更新前的数据状态...")
        freshness_info = test_current_data_freshness()
        
        if not freshness_info:
            print("❌ 无法获取数据新鲜度信息")
            return None
        
        print(f"\n当前数据状态:")
        print(f"- 最新更新时间: {freshness_info['latest_update']}")
        print(f"- 时间差: {freshness_info['time_diff_seconds']:.1f} 秒")
        print(f"- 是否交易时间: {freshness_info['is_trading_time']}")
        print(f"- 是否应该更新: {freshness_info['should_update']}")
        
        # 2. 执行智能更新
        print(f"\n2. 执行智能更新...")
        update_start_time = datetime.now()
        print(f"更新开始时间: {update_start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        start_time = time.time()
        
        update_response = requests.post(
            "http://localhost:5000/api/data/update",
            json={
                "update_mode": "incremental",
                "force_user_update": True
            },
            timeout=350
        )
        
        end_time = time.time()
        elapsed_time = end_time - start_time
        update_end_time = datetime.now()
        
        print(f"更新结束时间: {update_end_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"更新耗时: {elapsed_time:.2f} 秒")
        
        if update_response.status_code == 200:
            update_data = update_response.json()
            print(f"更新结果: {update_data.get('success', False)}")
            print(f"更新消息: {update_data.get('message', '无消息')}")
            
            # 3. 验证1分钟阈值的效果
            print(f"\n3. 验证1分钟阈值的效果...")
            
            if freshness_info['is_trading_time']:
                if freshness_info['time_diff_seconds'] > 60:
                    print(f"✅ 预期行为: 数据超过1分钟({freshness_info['time_diff_seconds']:.1f}秒)，应该触发更新")
                    if update_data.get('success'):
                        print("✅ 实际行为: 智能更新成功执行，符合预期")
                    else:
                        print("⚠️  实际行为: 智能更新未成功，可能有其他问题")
                else:
                    print(f"✅ 预期行为: 数据在1分钟内({freshness_info['time_diff_seconds']:.1f}秒)，应该使用缓存")
                    print("ℹ️  注意: 由于设置了force_user_update=True，仍会强制更新")
            else:
                print("ℹ️  当前为非交易时间，使用原有缓存策略")
            
            return {
                'before_freshness': freshness_info,
                'update_success': update_data.get('success', False),
                'elapsed_time': elapsed_time,
                'threshold_test_passed': True
            }
        else:
            print(f"❌ 智能更新失败: {update_response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ 智能更新测试失败: {e}")
        return None

def verify_1minute_threshold_modification():
    """验证1分钟阈值修改是否生效"""
    print(f"\n📋 验证1分钟阈值修改")
    print("=" * 60)
    
    try:
        # 读取修改后的代码文件
        with open('backend/services/data_service.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否包含新的1分钟阈值设置
        if 'freshness_threshold = 60' in content:
            print("✅ 代码修改成功: freshness_threshold = 60 (1分钟)")
        else:
            print("❌ 代码修改失败: 未找到 freshness_threshold = 60")
            return False
        
        # 检查注释是否更新
        if '1分钟（从15分钟进一步缩短）' in content:
            print("✅ 注释更新成功: 说明从15分钟缩短到1分钟")
        else:
            print("⚠️  注释可能未完全更新")
        
        # 检查日志输出是否更新
        if '1分钟)，需要更新' in content and '1分钟)，使用缓存' in content:
            print("✅ 日志输出更新成功: 显示1分钟阈值")
        else:
            print("⚠️  日志输出可能未完全更新")
        
        return True
        
    except FileNotFoundError:
        print("❌ 无法找到数据服务文件")
        return False
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🔧 1分钟数据新鲜度阈值测试")
    print("=" * 60)
    print("测试时间:", datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
    print("修改内容: 交易时间内数据新鲜度阈值 15分钟 → 1分钟")
    
    # 1. 验证代码修改
    code_ok = verify_1minute_threshold_modification()
    
    # 2. 测试当前数据新鲜度
    freshness_info = test_current_data_freshness()
    
    # 3. 测试智能更新行为
    if code_ok and freshness_info:
        update_result = test_smart_update_with_1minute_threshold()
    else:
        update_result = None
    
    # 4. 总结测试结果
    print(f"\n📊 测试总结")
    print("=" * 60)
    
    if code_ok:
        print("✅ 代码修改验证通过")
    else:
        print("❌ 代码修改验证失败")
    
    if freshness_info:
        print(f"✅ 数据新鲜度测试通过")
        print(f"   - 当前数据时间差: {freshness_info['time_diff_seconds']:.1f} 秒")
        print(f"   - 1分钟阈值判断: {'需要更新' if freshness_info['should_update'] else '使用缓存'}")
    else:
        print("❌ 数据新鲜度测试失败")
    
    if update_result:
        print("✅ 智能更新测试通过")
    else:
        print("⚠️  智能更新测试未完成")
    
    print(f"\n💡 修改效果:")
    print("1. 交易时间内数据新鲜度阈值: 15分钟 → 1分钟")
    print("2. 提高数据实时性: 超过1分钟的数据会触发更新")
    print("3. 避免过度调用: 1分钟内的数据仍使用缓存")
    print("4. 其他逻辑不变: 非交易时间和强制刷新逻辑保持原状")

if __name__ == "__main__":
    main()
