#!/usr/bin/env python3
"""
测试修复后的概念N型待选股票API
"""
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from flask import Flask, jsonify, request
from flask_cors import CORS
import akshare as ak
import time
from datetime import datetime
from sqlalchemy import create_engine, text
import urllib.parse

app = Flask(__name__)
CORS(app)

# 数据库连接
DATABASE_URL = "mysql+pymysql://root:Flameaway3.@localhost:3306/stock_analysis"
engine = create_engine(DATABASE_URL)

def convert_stock_code_to_akshare_format(stock_code):
    """将股票代码转换为AkShare个股查询API所需的格式"""
    if len(stock_code) != 6:
        return None
    
    if stock_code.startswith('6'):
        return f"SH{stock_code}"  # 上海股票
    elif stock_code.startswith('0') or stock_code.startswith('3'):
        return f"SZ{stock_code}"  # 深圳股票
    elif stock_code.startswith('4') or stock_code.startswith('8'):
        return f"BJ{stock_code}"  # 北京股票
    else:
        return None

def get_stock_realtime_data_akshare(stock_code):
    """使用AkShare个股查询API获取单只股票的实时数据"""
    try:
        # 转换股票代码格式
        akshare_code = convert_stock_code_to_akshare_format(stock_code)
        if not akshare_code:
            print(f"无法转换股票代码格式: {stock_code}")
            return None
        
        # 调用AkShare API获取个股数据
        stock_data = ak.stock_individual_spot_xq(symbol=akshare_code)
        
        if stock_data is None or stock_data.empty:
            print(f"获取股票 {akshare_code} 数据为空")
            return None
        
        # 将DataFrame转换为字典格式
        stock_dict = dict(zip(stock_data['item'], stock_data['value']))
        
        # 提取需要的字段
        result = {
            '涨跌幅': float(stock_dict.get('涨幅', 0.0)),  # 涨幅字段 → 涨跌幅
            '最新价': float(stock_dict.get('现价', 0.0)),  # 现价字段 → 最新价
            '涨跌额': float(stock_dict.get('涨跌', 0.0)),  # 涨跌字段 → 涨跌额
            '更新时间': stock_dict.get('时间', ''),        # 时间字段 → 更新时间
            '股票名称': stock_dict.get('名称', ''),        # 名称字段
            '昨收价': float(stock_dict.get('昨收', 0.0)),  # 昨收字段
        }
        
        print(f"成功获取股票 {stock_code} 实时数据: 涨跌幅={result['涨跌幅']}%, 最新价={result['最新价']}")
        return result
        
    except Exception as e:
        print(f"获取股票 {stock_code} 实时数据失败: {e}")
        return None

@app.route('/')
def index():
    return {'message': '概念N型待选股票API测试服务', 'status': 'ok'}

@app.route('/api/concept-n-stocks/<concept_name>', methods=['GET'])
def get_concept_n_stocks(concept_name):
    """获取指定概念的N型待选股票列表（包含实时数据）"""
    start_time = time.time()

    try:
        print(f"开始获取概念 '{concept_name}' 的N型待选股票数据")

        # URL解码概念名称（处理中文字符）
        decoded_concept = urllib.parse.unquote(concept_name)

        # 模拟查询数据库（简化版）
        # 这里使用一些测试数据
        test_stocks = [
            {'stock_code': '000001', 'stock_name': '平安银行'},
            {'stock_code': '600000', 'stock_name': '浦发银行'},
            {'stock_code': '000002', 'stock_name': '万科A'}
        ]

        # 转换数据格式并添加实时数据
        n_stocks_data = []
        realtime_success_count = 0
        
        for stock in test_stocks:
            stock_code = stock['stock_code']
            
            # 基础数据
            stock_dict = {
                'stock_code': stock_code,
                'stock_name': stock['stock_name'],
                'limit_up_date': '20250702',
                'decline_days': 2,
                'continuous_decline': '是',
                'full_stock_name': stock['stock_name'],
                'theme': decoded_concept,
                'theme_summary': f'{decoded_concept}相关概念',
                'theme_g': '',
                'trade_date': '20250702',
                'ths_concept_names': decoded_concept,
                'sector_name': '银行',
                'em_sector_names': '银行',
                'created_at': datetime.now().isoformat(),
                'updated_at': datetime.now().isoformat(),
                'large_buy_data': {
                    'exists': False,
                    '总买入金额': 0.0,
                    '数量': 0,
                    '总卖出金额': 0.0,
                    '买卖比': 0.0,
                    '总买入占比': 0.0
                }
            }
            
            # 获取实时数据
            realtime_info = get_stock_realtime_data_akshare(stock_code)
            
            if realtime_info:
                # 添加实时数据到股票字典中
                stock_dict.update({
                    '涨跌幅': realtime_info['涨跌幅'],
                    '最新价': realtime_info['最新价'],
                    '涨跌额': realtime_info['涨跌额'],
                    '更新时间': realtime_info['更新时间'],
                    '昨收价': realtime_info['昨收价']
                })
                realtime_success_count += 1
            else:
                # 如果获取失败，添加空值
                stock_dict.update({
                    '涨跌幅': None,
                    '最新价': None,
                    '涨跌额': None,
                    '更新时间': '',
                    '昨收价': None
                })
            
            n_stocks_data.append(stock_dict)
            
            # 添加小延迟避免API限制
            time.sleep(0.1)

        query_time = (time.time() - start_time) * 1000
        print(f"成功获取概念 '{decoded_concept}' 的 {len(n_stocks_data)} 只N型待选股票，查询耗时: {query_time:.2f}ms，实时数据成功: {realtime_success_count}")

        return jsonify({
            'success': True,
            'data': n_stocks_data,
            'total': len(n_stocks_data),
            'concept_name': decoded_concept,
            'query_time_ms': round(query_time, 2),
            'realtime_success': realtime_success_count,
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        query_time = (time.time() - start_time) * 1000
        print(f"获取概念 '{concept_name}' N型待选股票数据失败: {e}, 耗时: {query_time:.2f}ms")
        return jsonify({
            'success': False,
            'error': str(e),
            'concept_name': concept_name,
            'query_time_ms': round(query_time, 2),
            'timestamp': datetime.now().isoformat()
        }), 500

if __name__ == '__main__':
    print('🚀 概念N型待选股票API测试服务启动在 http://localhost:5000')
    app.run(host='0.0.0.0', port=5000, debug=False)
