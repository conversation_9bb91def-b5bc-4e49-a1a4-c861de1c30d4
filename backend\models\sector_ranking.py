from datetime import datetime, date
from database import db
from sqlalchemy import Index, func

class SectorDailyRanking(db.Model):
    """板块日排名数据模型"""
    __tablename__ = 'sector_daily_rankings'
    
    id = db.Column(db.Integer, primary_key=True)
    ranking_date = db.Column(db.Date, nullable=False, comment='排名日期')
    sector_code = db.Column(db.String(20), nullable=False, comment='板块代码')
    sector_name = db.Column(db.String(100), nullable=False, comment='板块名称')
    ranking = db.Column(db.Integer, nullable=False, comment='排名(1-10)')
    
    # 价格数据
    close_price = db.Column(db.Numeric(10, 4), comment='收盘价')
    price_change = db.Column(db.Numeric(10, 4), comment='涨跌额')
    price_change_pct = db.Column(db.Numeric(8, 4), comment='涨跌幅(%)')
    
    # 成交数据
    volume = db.Column(db.<PERSON>Integer, comment='成交量')
    turnover = db.Column(db.Numeric(15, 2), comment='成交额')
    
    # 技术指标数据
    ma5 = db.Column(db.Numeric(10, 4), comment='5日移动平均线')
    ma10 = db.Column(db.Numeric(10, 4), comment='10日移动平均线')
    ma20 = db.Column(db.Numeric(10, 4), comment='20日移动平均线')
    
    # 趋势数据
    trend_direction = db.Column(db.String(20), comment='趋势方向')
    consecutive_up_days = db.Column(db.Integer, default=0, comment='连续上涨天数')
    is_new_high_5d = db.Column(db.Boolean, default=False, comment='是否创5日新高')
    is_new_high_20d = db.Column(db.Boolean, default=False, comment='是否创20日新高')

    # 领涨股票数据
    leading_stock_name = db.Column(db.String(100), comment='领涨股票名称')
    leading_stock_code = db.Column(db.String(20), comment='领涨股票代码')
    leading_stock_price = db.Column(db.Numeric(10, 4), comment='领涨股票价格')
    leading_stock_change_pct = db.Column(db.Numeric(8, 4), comment='领涨股票涨跌幅(%)')

    # 时间戳
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='创建时间')
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='更新时间')
    
    # 唯一约束和索引
    __table_args__ = (
        db.UniqueConstraint('ranking_date', 'sector_code', name='uq_ranking_date_sector'),
        Index('idx_ranking_date_sector', 'ranking_date', 'sector_code'),
        Index('idx_ranking_date', 'ranking_date'),
        Index('idx_sector_code', 'sector_code'),
        Index('idx_ranking_date_ranking', 'ranking_date', 'ranking'),
    )
    
    def __repr__(self):
        return f'<SectorDailyRanking {self.ranking_date} {self.sector_name} #{self.ranking}>'
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'ranking_date': self.ranking_date.isoformat() if self.ranking_date else None,
            'sector_code': self.sector_code,
            'sector_name': self.sector_name,
            'ranking': self.ranking,
            'close_price': float(self.close_price) if self.close_price else None,
            'price_change': float(self.price_change) if self.price_change else None,
            'price_change_pct': float(self.price_change_pct) if self.price_change_pct else None,
            'volume': self.volume,
            'turnover': float(self.turnover) if self.turnover else None,
            'ma5': float(self.ma5) if self.ma5 else None,
            'ma10': float(self.ma10) if self.ma10 else None,
            'ma20': float(self.ma20) if self.ma20 else None,
            'trend_direction': self.trend_direction,
            'consecutive_up_days': self.consecutive_up_days,
            'is_new_high_5d': self.is_new_high_5d,
            'is_new_high_20d': self.is_new_high_20d,
            'leading_stock_name': self.leading_stock_name,
            'leading_stock_code': self.leading_stock_code,
            'leading_stock_price': float(self.leading_stock_price) if self.leading_stock_price else None,
            'leading_stock_change_pct': float(self.leading_stock_change_pct) if self.leading_stock_change_pct else None,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
    
    @classmethod
    def get_rankings_by_date(cls, ranking_date):
        """获取指定日期的排名数据"""
        return cls.query.filter_by(ranking_date=ranking_date)\
                       .order_by(cls.ranking.asc())\
                       .all()
    
    @classmethod
    def get_rankings_by_range(cls, start_date, end_date):
        """获取指定日期范围的排名数据"""
        return cls.query.filter(cls.ranking_date >= start_date,
                               cls.ranking_date <= end_date)\
                       .order_by(cls.ranking_date.desc(), cls.ranking.asc())\
                       .all()
    
    @classmethod
    def get_sector_ranking_history(cls, sector_code, start_date=None, end_date=None, limit=None):
        """获取指定板块的排名历史"""
        query = cls.query.filter_by(sector_code=sector_code)
        
        if start_date:
            query = query.filter(cls.ranking_date >= start_date)
        if end_date:
            query = query.filter(cls.ranking_date <= end_date)
        
        query = query.order_by(cls.ranking_date.desc())
        
        if limit:
            query = query.limit(limit)
        
        return query.all()
    
    @classmethod
    def get_active_sectors(cls, start_date, end_date, min_appearances=3):
        """获取在指定时间范围内连续活跃的板块"""
        from sqlalchemy import func
        
        # 查询在指定时间范围内出现次数超过阈值的板块
        subquery = cls.query.filter(cls.ranking_date >= start_date,
                                   cls.ranking_date <= end_date)\
                           .with_entities(cls.sector_code, 
                                        cls.sector_name,
                                        func.count(cls.id).label('appearances'),
                                        func.avg(cls.ranking).label('avg_ranking'),
                                        func.min(cls.ranking).label('best_ranking'))\
                           .group_by(cls.sector_code, cls.sector_name)\
                           .having(func.count(cls.id) >= min_appearances)\
                           .order_by(func.count(cls.id).desc(), func.avg(cls.ranking).asc())\
                           .all()
        
        return subquery
    
    @classmethod
    def get_latest_rankings(cls, limit=10):
        """获取最新的排名数据"""
        latest_date = db.session.query(func.max(cls.ranking_date)).scalar()
        if not latest_date:
            return []
        
        return cls.query.filter_by(ranking_date=latest_date)\
                       .order_by(cls.ranking.asc())\
                       .limit(limit)\
                       .all()
    
    @classmethod
    def bulk_insert_rankings(cls, rankings_data):
        """批量插入排名数据"""
        try:
            db.session.bulk_insert_mappings(cls, rankings_data)
            db.session.commit()
            return True
        except Exception as e:
            db.session.rollback()
            raise e
