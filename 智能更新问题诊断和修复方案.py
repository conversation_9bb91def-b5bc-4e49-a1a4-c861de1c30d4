#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能更新功能问题诊断和修复方案

问题分析：
1. 数据更新逻辑问题：交易时间内智能更新仍使用缓存而不是获取最新API数据
2. 时间显示问题：前端"最后更新时间"没有更新到实际的数据处理时间
3. 缓存策略问题：18.7分钟的数据被认为是"新鲜"的，但用户期望获取最新数据
"""

import requests
import json
import time
from datetime import datetime, time as dt_time

def diagnose_trading_time_logic():
    """诊断交易时间判断逻辑"""
    print("🕐 诊断交易时间判断逻辑")
    print("=" * 60)
    
    try:
        # 测试当前时间的交易时间判断
        current_time = datetime.now()
        print(f"当前时间: {current_time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 模拟交易时间判断逻辑
        current_time_only = current_time.time()
        
        # 上午交易时间：9:30-11:30
        morning_start = dt_time(9, 30)
        morning_end = dt_time(11, 30)
        
        # 下午交易时间：13:00-15:00
        afternoon_start = dt_time(13, 0)
        afternoon_end = dt_time(15, 0)
        
        is_trading_time = (morning_start <= current_time_only <= morning_end) or \
                         (afternoon_start <= current_time_only <= afternoon_end)
        
        print(f"是否为交易时间: {is_trading_time}")
        
        if is_trading_time:
            print("✅ 当前为交易时间，应该获取最新API数据")
        else:
            print("⚠️  当前为非交易时间，可以使用缓存数据")
        
        return is_trading_time
        
    except Exception as e:
        print(f"❌ 交易时间判断失败: {e}")
        return False

def diagnose_data_freshness_logic():
    """诊断数据新鲜度判断逻辑"""
    print(f"\n🔍 诊断数据新鲜度判断逻辑")
    print("=" * 60)
    
    try:
        # 获取当前板块数据
        response = requests.get("http://localhost:5000/api/sectors/all-realtime", timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            
            if data.get('success') and data.get('data'):
                sectors = data['data']
                
                # 分析数据更新时间
                update_times = [s.get('数据更新时间') for s in sectors if s.get('数据更新时间')]
                
                if update_times:
                    latest_update_str = max(update_times)
                    latest_update = datetime.strptime(latest_update_str, '%Y-%m-%d %H:%M:%S')
                    current_time = datetime.now()
                    time_diff = current_time - latest_update
                    
                    print(f"最新数据更新时间: {latest_update_str}")
                    print(f"当前时间: {current_time.strftime('%Y-%m-%d %H:%M:%S')}")
                    print(f"时间差: {time_diff.total_seconds()/60:.1f} 分钟")
                    
                    # 分析新鲜度判断逻辑
                    if time_diff.total_seconds() > 1800:  # 30分钟
                        print("❌ 数据已过时（超过30分钟），应该更新")
                        freshness_status = "过时"
                    else:
                        print("✅ 数据较新（30分钟内），使用缓存")
                        freshness_status = "新鲜"
                    
                    # 问题分析
                    print(f"\n🔍 问题分析:")
                    print(f"- 数据新鲜度状态: {freshness_status}")
                    print(f"- 时间差: {time_diff.total_seconds()/60:.1f} 分钟")
                    
                    if time_diff.total_seconds() < 1800:
                        print("⚠️  问题发现: 18.7分钟的数据被认为是'新鲜'的")
                        print("   但用户在交易时间内点击智能更新期望获取最新数据")
                        print("   建议: 在交易时间内，用户主动更新时应该强制获取最新数据")
                    
                    return {
                        'latest_update': latest_update_str,
                        'time_diff_minutes': time_diff.total_seconds() / 60,
                        'is_fresh': time_diff.total_seconds() <= 1800,
                        'should_update': time_diff.total_seconds() > 1800
                    }
                else:
                    print("❌ 没有找到数据更新时间")
                    return None
            else:
                print(f"❌ API返回失败: {data.get('error', '未知错误')}")
                return None
        else:
            print(f"❌ API调用失败，状态码: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ 数据新鲜度诊断失败: {e}")
        return None

def test_smart_update_behavior():
    """测试智能更新行为"""
    print(f"\n🔄 测试智能更新行为")
    print("=" * 60)
    
    try:
        # 记录更新前的数据时间
        print("1. 获取更新前的数据时间...")
        before_response = requests.get("http://localhost:5000/api/sectors/all-realtime", timeout=30)
        before_data = before_response.json()
        
        before_update_times = []
        if before_data.get('success') and before_data.get('data'):
            before_update_times = [s.get('数据更新时间') for s in before_data['data'] if s.get('数据更新时间')]
        
        before_latest = max(before_update_times) if before_update_times else "无"
        print(f"更新前最新时间: {before_latest}")
        
        # 执行智能更新
        print("\n2. 执行智能更新...")
        start_time = time.time()
        
        update_response = requests.post(
            "http://localhost:5000/api/data/update",
            json={"update_mode": "incremental"},
            timeout=350
        )
        
        end_time = time.time()
        elapsed_time = end_time - start_time
        
        print(f"智能更新耗时: {elapsed_time:.2f} 秒")
        
        if update_response.status_code == 200:
            update_data = update_response.json()
            print(f"更新结果: {update_data.get('success', False)}")
            print(f"更新消息: {update_data.get('message', '无消息')}")
            
            # 获取更新后的数据时间
            print("\n3. 获取更新后的数据时间...")
            after_response = requests.get("http://localhost:5000/api/sectors/all-realtime", timeout=30)
            after_data = after_response.json()
            
            after_update_times = []
            if after_data.get('success') and after_data.get('data'):
                after_update_times = [s.get('数据更新时间') for s in after_data['data'] if s.get('数据更新时间')]
            
            after_latest = max(after_update_times) if after_update_times else "无"
            print(f"更新后最新时间: {after_latest}")
            
            # 分析结果
            print(f"\n📊 结果分析:")
            print(f"- 更新前时间: {before_latest}")
            print(f"- 更新后时间: {after_latest}")
            print(f"- 时间是否变化: {'是' if before_latest != after_latest else '否'}")
            
            if before_latest == after_latest:
                print("⚠️  问题确认: 智能更新后数据时间没有变化")
                print("   这说明智能更新使用了缓存数据而不是获取最新API数据")
            else:
                print("✅ 数据时间已更新，智能更新正常工作")
            
            return {
                'before_time': before_latest,
                'after_time': after_latest,
                'time_changed': before_latest != after_latest,
                'elapsed_time': elapsed_time
            }
        else:
            print(f"❌ 智能更新失败，状态码: {update_response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ 智能更新测试失败: {e}")
        return None

def analyze_problems_and_solutions():
    """分析问题并提供解决方案"""
    print(f"\n🎯 问题分析和解决方案")
    print("=" * 60)
    
    print("📋 问题总结:")
    print("1. 数据更新逻辑问题:")
    print("   - 智能更新在交易时间内仍使用缓存（18.7分钟内认为新鲜）")
    print("   - 用户期望在交易时间内主动更新时获取最新数据")
    print("   - 30分钟的新鲜度阈值对于用户主动更新来说太长")
    
    print("\n2. 时间显示问题:")
    print("   - 前端显示的是缓存数据的时间戳，不是实际处理时间")
    print("   - 智能更新重新计算技术指标但没有更新数据时间戳")
    print("   - 用户无法确认数据是否真正更新")
    
    print("\n3. 缓存策略问题:")
    print("   - 交易时间内用户主动更新应该强制获取最新数据")
    print("   - 技术指标重新计算后应该更新数据时间戳")
    print("   - 需要区分自动更新和用户主动更新的策略")
    
    print(f"\n🔧 解决方案:")
    print("1. 修改智能更新逻辑:")
    print("   - 在交易时间内，用户主动更新时强制获取最新API数据")
    print("   - 添加force_user_update参数区分用户主动更新")
    print("   - 缩短交易时间内的新鲜度阈值（从30分钟改为15分钟）")
    
    print("\n2. 修复时间显示问题:")
    print("   - 智能更新完成后更新所有板块的数据更新时间")
    print("   - 技术指标重新计算后更新时间戳")
    print("   - 确保前端显示的是最新的处理时间")
    
    print("\n3. 优化缓存策略:")
    print("   - 用户主动更新时设置force_refresh=True")
    print("   - 更新后立即刷新缓存中的时间戳")
    print("   - 提供更明确的用户反馈")

def main():
    """主诊断函数"""
    print("🔍 智能更新功能问题诊断")
    print("=" * 60)
    print("问题描述: 交易时间内智能更新使用缓存，前端时间显示不更新")
    print("诊断时间:", datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
    
    # 1. 诊断交易时间判断
    is_trading = diagnose_trading_time_logic()
    
    # 2. 诊断数据新鲜度逻辑
    freshness_info = diagnose_data_freshness_logic()
    
    # 3. 测试智能更新行为
    update_result = test_smart_update_behavior()
    
    # 4. 分析问题并提供解决方案
    analyze_problems_and_solutions()
    
    print(f"\n📊 诊断总结:")
    print(f"- 当前是否交易时间: {is_trading}")
    if freshness_info:
        print(f"- 数据新鲜度: {'新鲜' if freshness_info['is_fresh'] else '过时'}")
        print(f"- 数据时间差: {freshness_info['time_diff_minutes']:.1f} 分钟")
    if update_result:
        print(f"- 智能更新时间变化: {'是' if update_result['time_changed'] else '否'}")
        print(f"- 更新耗时: {update_result['elapsed_time']:.2f} 秒")
    
    print(f"\n💡 关键发现:")
    print("1. 智能更新在交易时间内仍使用缓存数据（18.7分钟被认为新鲜）")
    print("2. 用户主动更新时应该强制获取最新数据，而不是依赖新鲜度判断")
    print("3. 需要修改智能更新逻辑，区分用户主动更新和自动更新")

if __name__ == "__main__":
    main()
