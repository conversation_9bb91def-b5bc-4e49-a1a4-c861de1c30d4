/**
 * 板块日历数据缓存工具
 * 实现前端本地缓存机制，提升用户体验
 */

import type { SectorRanking, ActiveSector } from '../types/SectorCalendar'

// 缓存键名常量
const CACHE_KEYS = {
  CALENDAR_DATA: 'sector_calendar_data',
  ACTIVE_SECTORS: 'sector_active_sectors',
  LAST_UPDATE: 'sector_calendar_last_update',
  CACHE_VERSION: 'sector_calendar_cache_version'
} as const

// 缓存版本，用于缓存失效
const CACHE_VERSION = '1.0.0'

// 缓存配置
const CACHE_CONFIG = {
  // 缓存过期时间（毫秒）
  TTL: 30 * 60 * 1000, // 30分钟
  // 最大缓存条目数
  MAX_ENTRIES: 100,
  // 数据压缩阈值（字节）
  COMPRESSION_THRESHOLD: 10240 // 10KB
} as const

// 缓存数据接口
interface CachedCalendarData {
  data: SectorRanking[]
  timestamp: number
  dateRange: {
    startDate: string
    endDate: string
  }
  version: string
}

interface CachedActiveSectors {
  data: ActiveSector[]
  timestamp: number
  params: {
    days: number
    min_appearances: number
  }
  version: string
}

/**
 * 板块日历缓存管理器
 */
export class SectorCalendarCache {
  private static instance: SectorCalendarCache
  private storage: Storage

  private constructor() {
    // 优先使用sessionStorage，页面关闭时自动清理
    this.storage = window.sessionStorage
    this.initCache()
  }

  /**
   * 获取缓存管理器单例
   */
  public static getInstance(): SectorCalendarCache {
    if (!SectorCalendarCache.instance) {
      SectorCalendarCache.instance = new SectorCalendarCache()
    }
    return SectorCalendarCache.instance
  }

  /**
   * 初始化缓存
   */
  private initCache(): void {
    try {
      // 检查缓存版本，版本不匹配时清理缓存
      const cachedVersion = this.storage.getItem(CACHE_KEYS.CACHE_VERSION)
      if (cachedVersion !== CACHE_VERSION) {
        this.clearAll()
        this.storage.setItem(CACHE_KEYS.CACHE_VERSION, CACHE_VERSION)
      }

      // 清理过期缓存
      this.cleanExpiredCache()
    } catch (error) {
      console.warn('缓存初始化失败:', error)
      this.clearAll()
    }
  }

  /**
   * 生成缓存键
   */
  private generateCacheKey(baseKey: string, params?: Record<string, any>): string {
    if (!params) return baseKey
    
    const paramString = Object.keys(params)
      .sort()
      .map(key => `${key}=${params[key]}`)
      .join('&')
    
    return `${baseKey}_${paramString}`
  }

  /**
   * 检查数据是否过期
   */
  private isExpired(timestamp: number): boolean {
    return Date.now() - timestamp > CACHE_CONFIG.TTL
  }

  /**
   * 压缩数据（简单的JSON压缩）
   */
  private compressData(data: any): string {
    const jsonString = JSON.stringify(data)
    
    // 如果数据量小于阈值，不压缩
    if (jsonString.length < CACHE_CONFIG.COMPRESSION_THRESHOLD) {
      return jsonString
    }

    // 简单的压缩：移除不必要的空格
    return JSON.stringify(data, null, 0)
  }

  /**
   * 解压数据
   */
  private decompressData(compressedData: string): any {
    try {
      return JSON.parse(compressedData)
    } catch (error) {
      console.warn('数据解压失败:', error)
      return null
    }
  }

  /**
   * 缓存日历数据
   */
  public setCachedCalendarData(
    data: SectorRanking[],
    startDate: string,
    endDate: string
  ): void {
    try {
      const cacheKey = this.generateCacheKey(CACHE_KEYS.CALENDAR_DATA, {
        startDate,
        endDate
      })

      const cachedData: CachedCalendarData = {
        data,
        timestamp: Date.now(),
        dateRange: { startDate, endDate },
        version: CACHE_VERSION
      }

      const compressedData = this.compressData(cachedData)
      this.storage.setItem(cacheKey, compressedData)

      // 更新最后更新时间
      this.storage.setItem(CACHE_KEYS.LAST_UPDATE, Date.now().toString())
    } catch (error) {
      console.warn('缓存日历数据失败:', error)
    }
  }

  /**
   * 获取缓存的日历数据
   */
  public getCachedCalendarData(
    startDate: string,
    endDate: string
  ): SectorRanking[] | null {
    try {
      const cacheKey = this.generateCacheKey(CACHE_KEYS.CALENDAR_DATA, {
        startDate,
        endDate
      })

      const cachedDataString = this.storage.getItem(cacheKey)
      if (!cachedDataString) return null

      const cachedData = this.decompressData(cachedDataString) as CachedCalendarData
      if (!cachedData || cachedData.version !== CACHE_VERSION) {
        this.storage.removeItem(cacheKey)
        return null
      }

      // 检查是否过期
      if (this.isExpired(cachedData.timestamp)) {
        this.storage.removeItem(cacheKey)
        return null
      }

      return cachedData.data
    } catch (error) {
      console.warn('获取缓存日历数据失败:', error)
      return null
    }
  }

  /**
   * 缓存活跃板块数据
   */
  public setCachedActiveSectors(
    data: ActiveSector[],
    days: number,
    min_appearances: number
  ): void {
    try {
      const cacheKey = this.generateCacheKey(CACHE_KEYS.ACTIVE_SECTORS, {
        days,
        min_appearances
      })

      const cachedData: CachedActiveSectors = {
        data,
        timestamp: Date.now(),
        params: { days, min_appearances },
        version: CACHE_VERSION
      }

      const compressedData = this.compressData(cachedData)
      this.storage.setItem(cacheKey, compressedData)
    } catch (error) {
      console.warn('缓存活跃板块数据失败:', error)
    }
  }

  /**
   * 获取缓存的活跃板块数据
   */
  public getCachedActiveSectors(
    days: number,
    min_appearances: number
  ): ActiveSector[] | null {
    try {
      const cacheKey = this.generateCacheKey(CACHE_KEYS.ACTIVE_SECTORS, {
        days,
        min_appearances
      })

      const cachedDataString = this.storage.getItem(cacheKey)
      if (!cachedDataString) return null

      const cachedData = this.decompressData(cachedDataString) as CachedActiveSectors
      if (!cachedData || cachedData.version !== CACHE_VERSION) {
        this.storage.removeItem(cacheKey)
        return null
      }

      // 检查是否过期
      if (this.isExpired(cachedData.timestamp)) {
        this.storage.removeItem(cacheKey)
        return null
      }

      return cachedData.data
    } catch (error) {
      console.warn('获取缓存活跃板块数据失败:', error)
      return null
    }
  }

  /**
   * 获取最后更新时间
   */
  public getLastUpdateTime(): number | null {
    try {
      const lastUpdate = this.storage.getItem(CACHE_KEYS.LAST_UPDATE)
      return lastUpdate ? parseInt(lastUpdate, 10) : null
    } catch (error) {
      console.warn('获取最后更新时间失败:', error)
      return null
    }
  }

  /**
   * 清理过期缓存
   */
  public cleanExpiredCache(): void {
    try {
      const keysToRemove: string[] = []
      
      for (let i = 0; i < this.storage.length; i++) {
        const key = this.storage.key(i)
        if (!key || !key.startsWith('sector_calendar_')) continue

        const value = this.storage.getItem(key)
        if (!value) continue

        try {
          const data = this.decompressData(value)
          if (data && data.timestamp && this.isExpired(data.timestamp)) {
            keysToRemove.push(key)
          }
        } catch (error) {
          // 数据格式错误，也删除
          keysToRemove.push(key)
        }
      }

      keysToRemove.forEach(key => this.storage.removeItem(key))
      
      if (keysToRemove.length > 0) {
        console.log(`清理了 ${keysToRemove.length} 个过期缓存项`)
      }
    } catch (error) {
      console.warn('清理过期缓存失败:', error)
    }
  }

  /**
   * 清理所有缓存
   */
  public clearAll(): void {
    try {
      const keysToRemove: string[] = []
      
      for (let i = 0; i < this.storage.length; i++) {
        const key = this.storage.key(i)
        if (key && key.startsWith('sector_calendar_')) {
          keysToRemove.push(key)
        }
      }

      keysToRemove.forEach(key => this.storage.removeItem(key))
      console.log('已清理所有板块日历缓存')
    } catch (error) {
      console.warn('清理所有缓存失败:', error)
    }
  }

  /**
   * 获取缓存统计信息
   */
  public getCacheStats(): {
    totalItems: number
    totalSize: number
    lastUpdate: number | null
  } {
    let totalItems = 0
    let totalSize = 0

    try {
      for (let i = 0; i < this.storage.length; i++) {
        const key = this.storage.key(i)
        if (key && key.startsWith('sector_calendar_')) {
          totalItems++
          const value = this.storage.getItem(key)
          if (value) {
            totalSize += value.length
          }
        }
      }
    } catch (error) {
      console.warn('获取缓存统计失败:', error)
    }

    return {
      totalItems,
      totalSize,
      lastUpdate: this.getLastUpdateTime()
    }
  }
}

// 导出缓存管理器实例
export const sectorCalendarCache = SectorCalendarCache.getInstance()
