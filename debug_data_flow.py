#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试数据流，追踪领涨股票信息的处理过程
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from datetime import date
from services.sector_ranking_service import SectorRankingService
import pandas as pd

def debug_data_flow():
    """调试数据流"""
    print("=" * 80)
    print("调试数据流 - 追踪领涨股票信息处理过程")
    print("=" * 80)
    
    try:
        service = SectorRankingService()
        test_date = date(2025, 7, 11)
        
        print(f"测试日期: {test_date}")
        print()
        
        # 步骤1: 获取原始数据
        print("1. 获取原始板块数据...")
        sector_data = service.data_service.get_enhanced_sector_realtime_data()
        
        if not sector_data.empty:
            print(f"   ✅ 获取到 {len(sector_data)} 个板块数据")
            
            # 查看证券板块的原始数据
            securities_data = sector_data[sector_data['板块代码'] == 'BK0473']
            if not securities_data.empty:
                sector_row = securities_data.iloc[0]
                print(f"\n   证券板块原始数据:")
                print(f"     板块名称: {sector_row.get('板块名称')}")
                print(f"     涨跌幅: {sector_row.get('涨跌幅')}")
                print(f"     领涨股票: {sector_row.get('领涨股票')}")
                print(f"     领涨股票代码: {sector_row.get('领涨股票代码')}")
                print(f"     领涨股票价格: {sector_row.get('领涨股票价格')}")
                print(f"     领涨股票-涨跌幅: {sector_row.get('领涨股票-涨跌幅')}")
        else:
            print("   ❌ 未获取到原始数据")
            return
        
        # 步骤2: 数据预处理
        print(f"\n2. 数据预处理...")
        processed_data = service._process_sector_data(sector_data)
        
        if not processed_data.empty:
            print(f"   ✅ 处理后数据 {len(processed_data)} 条")
            
            # 查看证券板块的处理后数据
            securities_processed = processed_data[processed_data['板块代码'] == 'BK0473']
            if not securities_processed.empty:
                sector_row = securities_processed.iloc[0]
                print(f"\n   证券板块处理后数据:")
                print(f"     板块名称: {sector_row.get('板块名称')}")
                print(f"     涨跌幅: {sector_row.get('涨跌幅')}")
                print(f"     领涨股票: {sector_row.get('领涨股票')}")
                print(f"     领涨股票代码: {sector_row.get('领涨股票代码')}")
                print(f"     领涨股票价格: {sector_row.get('领涨股票价格')}")
                print(f"     领涨股票-涨跌幅: {sector_row.get('领涨股票-涨跌幅')}")
        else:
            print("   ❌ 处理后数据为空")
            return
        
        # 步骤3: 筛选前N名
        print(f"\n3. 筛选前3名...")
        top_sectors = processed_data.nlargest(3, '涨跌幅')
        print(f"   ✅ 筛选出前3名板块")
        
        for i, (_, row) in enumerate(top_sectors.iterrows()):
            print(f"\n   第{i+1}名: {row.get('板块名称')} ({row.get('板块代码')})")
            print(f"     涨跌幅: {row.get('涨跌幅'):.2f}%")
            print(f"     领涨股票: {row.get('领涨股票')}")
            print(f"     领涨股票代码: {row.get('领涨股票代码')}")
            print(f"     领涨股票价格: {row.get('领涨股票价格')}")
            print(f"     领涨股票-涨跌幅: {row.get('领涨股票-涨跌幅')}")
        
        # 步骤4: 转换为数据库格式
        print(f"\n4. 转换为数据库格式...")
        rankings_data = service._convert_to_ranking_data(top_sectors, test_date)
        
        print(f"   ✅ 转换完成，共 {len(rankings_data)} 条记录")
        
        for i, data in enumerate(rankings_data):
            print(f"\n   记录{i+1}: {data.get('sector_name')} ({data.get('sector_code')})")
            print(f"     排名: {data.get('ranking')}")
            print(f"     涨跌幅: {data.get('price_change_pct'):.2f}%")
            print(f"     领涨股票名称: '{data.get('leading_stock_name')}'")
            print(f"     领涨股票代码: '{data.get('leading_stock_code')}'")
            print(f"     领涨股票价格: {data.get('leading_stock_price')}")
            print(f"     领涨股票涨跌幅: {data.get('leading_stock_change_pct')}")
        
        # 检查字段是否为空
        print(f"\n5. 检查领涨股票字段是否为空...")
        for i, data in enumerate(rankings_data):
            leading_name = data.get('leading_stock_name', '')
            leading_code = data.get('leading_stock_code', '')
            
            if not leading_name or leading_name.strip() == '':
                print(f"   ⚠️  记录{i+1} 领涨股票名称为空")
            if not leading_code or leading_code.strip() == '':
                print(f"   ⚠️  记录{i+1} 领涨股票代码为空")
            
            if leading_name and leading_name.strip():
                print(f"   ✅ 记录{i+1} 领涨股票名称: '{leading_name}'")
            if leading_code and leading_code.strip():
                print(f"   ✅ 记录{i+1} 领涨股票代码: '{leading_code}'")
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_data_flow()
