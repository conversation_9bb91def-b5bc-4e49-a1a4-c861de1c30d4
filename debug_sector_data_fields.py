#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试板块数据字段名
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from services.data_service import DataService
import pandas as pd

def debug_sector_data_fields():
    """调试板块数据字段名"""
    print("=" * 60)
    print("调试板块数据字段名")
    print("=" * 60)
    
    try:
        data_service = DataService()
        
        # 获取实时数据
        print("1. 获取实时板块数据...")
        realtime_data = data_service.get_enhanced_sector_realtime_data()
        
        if not realtime_data.empty:
            print(f"   ✅ 获取到 {len(realtime_data)} 个板块的实时数据")
            print(f"   数据列: {list(realtime_data.columns)}")
            
            # 查看证券板块的数据
            securities_data = realtime_data[realtime_data['板块代码'] == 'BK0473']
            if not securities_data.empty:
                sector_row = securities_data.iloc[0]
                print(f"\n   证券板块数据:")
                print(f"     板块名称: {sector_row.get('板块名称', 'N/A')}")
                print(f"     涨跌幅: {sector_row.get('涨跌幅', 'N/A')}")
                print(f"     领涨股票: {sector_row.get('领涨股票', 'N/A')}")
                print(f"     领涨股票代码: {sector_row.get('领涨股票代码', 'N/A')}")
                print(f"     领涨股票价格: {sector_row.get('领涨股票价格', 'N/A')}")
                print(f"     领涨股票-涨跌幅: {sector_row.get('领涨股票-涨跌幅', 'N/A')}")
                
                # 显示所有包含"领涨"的字段
                leading_fields = [col for col in realtime_data.columns if '领涨' in col]
                print(f"\n   所有包含'领涨'的字段: {leading_fields}")
                
                for field in leading_fields:
                    print(f"     {field}: {sector_row.get(field, 'N/A')}")
            else:
                print(f"   ❌ 未找到证券板块数据")
        else:
            print(f"   ❌ 未获取到实时数据")
            
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_sector_data_fields()
