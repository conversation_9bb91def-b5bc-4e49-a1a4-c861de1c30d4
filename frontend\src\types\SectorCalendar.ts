/**
 * 板块日历相关的TypeScript类型定义
 */

// 板块排名数据接口
export interface SectorRanking {
  id: number
  ranking_date: string
  sector_code: string
  sector_name: string
  ranking: number
  close_price?: number
  price_change?: number
  price_change_pct?: number
  volume?: number
  turnover?: number
  ma5?: number
  ma10?: number
  ma20?: number
  trend_direction?: string
  consecutive_up_days?: number
  is_new_high_5d?: boolean
  is_new_high_20d?: boolean
  // 领涨股票信息
  leading_stock_name?: string
  leading_stock_code?: string
  leading_stock_price?: number
  leading_stock_change_pct?: number
  created_at?: string
  updated_at?: string
}

// 活跃板块数据接口
export interface ActiveSector {
  sector_code: string
  sector_name: string
  appearances: number
  avg_ranking: number
  best_ranking: number
}

// API响应基础接口
export interface ApiResponse<T> {
  success: boolean
  data: T
  total?: number
  query_type?: string
  query_params?: Record<string, any>
  query_time_ms?: number
  timestamp: string
  error?: string
}

// 板块排名查询参数接口
export interface SectorRankingsParams {
  start_date?: string
  end_date?: string
  date?: string
  limit?: number
}

// 活跃板块查询参数接口
export interface ActiveSectorsParams {
  days?: number
  min_appearances?: number
}

// 数据收集触发参数接口
export interface CollectionTriggerParams {
  target_date?: string
  top_n?: number
}

// 数据收集响应接口
export interface CollectionResponse {
  target_date: string
  top_n: number
  collected_count: number
}

// 板块排名API响应类型
export type SectorRankingsResponse = ApiResponse<SectorRanking[]>

// 活跃板块API响应类型
export type ActiveSectorsResponse = ApiResponse<ActiveSector[]>

// 数据收集API响应类型
export type CollectionTriggerResponse = ApiResponse<CollectionResponse>

// 日历数据聚合接口（用于日历视图）
export interface CalendarData {
  date: string
  rankings: SectorRanking[]
  active_count: number
  top_sector?: {
    sector_name: string
    price_change_pct: number
  }
}

// 板块连续活跃统计接口
export interface SectorActivityStats {
  sector_code: string
  sector_name: string
  total_appearances: number
  consecutive_days: number
  avg_ranking: number
  best_ranking: number
  latest_ranking: number
  activity_dates: string[]
}

// 日历视图配置接口
export interface CalendarViewConfig {
  start_date: string
  end_date: string
  highlight_active: boolean
  min_appearances: number
}

// 板块日历服务接口
export interface SectorCalendarService {
  getSectorCalendarRankings: (params?: SectorRankingsParams) => Promise<SectorRankingsResponse>
  triggerSectorCalendarCollection: (params?: CollectionTriggerParams) => Promise<CollectionTriggerResponse>
  getActiveSectors: (params?: ActiveSectorsParams) => Promise<ActiveSectorsResponse>
}

// 所有类型已通过interface和type关键字导出，无需重复导出
