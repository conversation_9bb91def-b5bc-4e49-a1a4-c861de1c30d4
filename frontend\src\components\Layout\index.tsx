import React, { useState, useEffect } from 'react'
import { Layout as AntLayout, Typography, Card, Row, Col, Spin, Alert, Button, Menu } from 'antd'
import { Outlet, useNavigate, useLocation } from 'react-router-dom'
import { ReloadOutlined, <PERSON><PERSON><PERSON>Outlined, A<PERSON>toreOutlined, <PERSON><PERSON><PERSON>Outlined, <PERSON>Outlined, CalendarOutlined } from '@ant-design/icons'
import ThemeToggle from '../ThemeToggle'
import { useTheme } from '../../theme/ThemeProvider'

const { Header, Content } = AntLayout
const { Title } = Typography

const Layout: React.FC = () => {
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const navigate = useNavigate()
  const location = useLocation()
  const { colorScheme } = useTheme()

  // 根据当前路径确定选中的菜单项
  const getCurrentPage = () => {
    if (location.pathname === '/dashboard') return 'dashboard'
    if (location.pathname === '/concept-analysis') return 'concept-analysis'
    if (location.pathname === '/sector-calendar') return 'sector-calendar'
    if (location.pathname.startsWith('/sectors/')) return 'all-sectors'
    return 'all-sectors'
  }

  useEffect(() => {
    // 模拟初始化加载
    const timer = setTimeout(() => {
      setLoading(false)
    }, 1000)

    return () => clearTimeout(timer)
  }, [])

  const renderContent = () => {
    if (loading) {
      return (
        <div style={{ textAlign: 'center', padding: '100px' }}>
          <Spin size="large" />
          <div style={{ marginTop: 16 }}>
            <Title level={3}>系统初始化中...</Title>
            <p>正在加载股票分析数据，请稍候...</p>
          </div>
        </div>
      )
    }

    if (error) {
      return (
        <div style={{ padding: '50px' }}>
          <Alert
            message="系统错误"
            description={error}
            type="error"
            showIcon
            action={
              <Button
                size="small"
                danger
                icon={<ReloadOutlined />}
                onClick={() => window.location.reload()}
              >
                重新加载
              </Button>
            }
          />
        </div>
      )
    }

    // 使用React Router的Outlet渲染子路由
    return <Outlet />
  }

  return (
    <AntLayout style={{ minHeight: '100vh', backgroundColor: colorScheme.background }}>
      <Header
        className="md3-surface-container-high"
        style={{
          background: colorScheme.surfaceContainerHigh,
          padding: 0,
          borderBottom: `1px solid ${colorScheme.outlineVariant}`,
          boxShadow: '0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24)',
        }}
      >
        <div
          style={{
            maxWidth: '1400px',
            margin: '0 auto',
            padding: '0 24px',
            width: '100%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
          }}
        >
          <div style={{ display: 'flex', alignItems: 'center', gap: '24px' }}>
            <Title
              level={3}
              className="md3-title-large"
              style={{
                color: colorScheme.onSurface,
                margin: 0,
                fontWeight: 500,
              }}
            >
              股票分析系统
            </Title>

            {/* 导航菜单 */}
            <Menu
              mode="horizontal"
              selectedKeys={[getCurrentPage()]}
              onClick={({ key }) => {
                if (key === 'dashboard') {
                  navigate('/dashboard')
                } else if (key === 'all-sectors') {
                  window.open('/', '_blank')
                } else if (key === 'concept-analysis') {
                  window.open('/concept-analysis', '_blank')
                } else if (key === 'sector-calendar') {
                  window.open('/sector-calendar', '_blank')
                } else if (key === 'external-analysis') {
                  window.open('https://fromozu-stock-analysis.hf.space/', '_blank')
                }
              }}
              style={{
                backgroundColor: 'transparent',
                borderBottom: 'none',
                minWidth: '500px',
              }}
              items={[
                // 隐藏申万31行业菜单项，但保留代码以备后用
                // {
                //   key: 'dashboard',
                //   icon: <BarChartOutlined />,
                //   label: '申万31行业',
                //   style: {
                //     color: getCurrentPage() === 'dashboard' ? colorScheme.primary : colorScheme.onSurface
                //   },
                // },
                {
                  key: 'all-sectors',
                  icon: <AppstoreOutlined />,
                  label: '板块概览',
                  style: {
                    color: getCurrentPage() === 'all-sectors' ? colorScheme.primary : colorScheme.onSurface
                  },
                },
                {
                  key: 'concept-analysis',
                  icon: <BulbOutlined />,
                  label: '概念分析',
                  style: {
                    color: getCurrentPage() === 'concept-analysis' ? colorScheme.primary : colorScheme.onSurface
                  },
                },
                {
                  key: 'sector-calendar',
                  icon: <CalendarOutlined />,
                  label: '板块日历',
                  style: {
                    color: getCurrentPage() === 'sector-calendar' ? colorScheme.primary : colorScheme.onSurface
                  },
                },
                {
                  key: 'external-analysis',
                  icon: <LinkOutlined />,
                  label: '高级分析',
                  style: {
                    color: colorScheme.onSurface
                  },
                },
              ]}
            />
          </div>
          <ThemeToggle />
        </div>
      </Header>
      <Content
        className="md3-surface"
        style={{
          background: colorScheme.background,
          padding: 0,
          margin: 0,
          minHeight: 'calc(100vh - 64px)',
        }}
      >
        <div
          style={{
            maxWidth: '1400px',
            margin: '0 auto',
            padding: '24px',
            width: '100%',
          }}
        >
          {renderContent()}
        </div>
      </Content>
    </AntLayout>
  )
}

export default Layout 