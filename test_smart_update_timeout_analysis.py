#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能更新超时问题分析和修复脚本

分析智能更新的实际执行时间，并提供前后端超时同步修复方案：
1. 评估实际处理时间
2. 分析各阶段耗时
3. 提供超时设置建议
4. 验证修复效果
"""

import requests
import json
import time
from datetime import datetime

def analyze_smart_update_timing():
    """分析智能更新的实际执行时间"""
    print("⏱️  分析智能更新实际执行时间")
    print("=" * 60)
    
    try:
        # 记录各阶段时间
        timing_data = {
            'start_time': None,
            'data_fetch_start': None,
            'data_fetch_end': None,
            'indicators_calc_start': None,
            'indicators_calc_end': None,
            'total_end': None
        }
        
        print("🚀 开始智能更新性能分析...")
        timing_data['start_time'] = time.time()
        start_datetime = datetime.now()
        print(f"开始时间: {start_datetime.strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 执行智能更新
        update_response = requests.post(
            "http://localhost:5000/api/data/update",
            json={
                "update_mode": "incremental",
                "force_user_update": True
            },
            timeout=600  # 10分钟超时用于分析
        )
        
        timing_data['total_end'] = time.time()
        end_datetime = datetime.now()
        total_elapsed = timing_data['total_end'] - timing_data['start_time']
        
        print(f"结束时间: {end_datetime.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"总耗时: {total_elapsed:.2f} 秒 ({total_elapsed/60:.2f} 分钟)")
        
        # 分析结果
        if update_response.status_code == 200:
            update_data = update_response.json()
            print(f"✅ 更新成功: {update_data.get('success', False)}")
            print(f"📝 更新消息: {update_data.get('message', '无消息')}")
            
            # 分析超时问题
            print(f"\n📊 超时分析:")
            current_frontend_timeout = 300  # 5分钟
            
            if total_elapsed > current_frontend_timeout:
                print(f"⚠️  超时问题确认:")
                print(f"   - 实际执行时间: {total_elapsed:.2f} 秒")
                print(f"   - 前端超时设置: {current_frontend_timeout} 秒")
                print(f"   - 超出时间: {total_elapsed - current_frontend_timeout:.2f} 秒")
                print(f"   - 建议前端超时: {int(total_elapsed * 1.2)} 秒")
            else:
                print(f"✅ 执行时间在前端超时限制内:")
                print(f"   - 实际执行时间: {total_elapsed:.2f} 秒")
                print(f"   - 前端超时设置: {current_frontend_timeout} 秒")
                print(f"   - 剩余时间: {current_frontend_timeout - total_elapsed:.2f} 秒")
            
            return {
                'success': True,
                'total_time': total_elapsed,
                'needs_timeout_adjustment': total_elapsed > current_frontend_timeout,
                'suggested_timeout': int(total_elapsed * 1.2) if total_elapsed > current_frontend_timeout else current_frontend_timeout,
                'timing_data': timing_data
            }
        else:
            print(f"❌ 更新失败: {update_response.status_code}")
            print(f"错误响应: {update_response.text[:500]}")
            return None
            
    except requests.exceptions.Timeout:
        elapsed_time = time.time() - timing_data['start_time']
        print(f"\n⚠️  请求超时 (耗时: {elapsed_time:.2f} 秒)")
        print(f"这表明智能更新确实需要很长时间")
        return {
            'success': False,
            'total_time': elapsed_time,
            'needs_timeout_adjustment': True,
            'suggested_timeout': 600,  # 10分钟
            'timeout_occurred': True
        }
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return None

def suggest_timeout_fix(analysis_result):
    """根据分析结果建议超时修复方案"""
    print(f"\n🔧 超时修复建议")
    print("=" * 60)
    
    if not analysis_result:
        print("❌ 无法提供建议，分析结果为空")
        return None
    
    current_timeout = 300000  # 5分钟（毫秒）
    
    if analysis_result.get('needs_timeout_adjustment'):
        suggested_timeout_seconds = analysis_result.get('suggested_timeout', 600)
        suggested_timeout_ms = suggested_timeout_seconds * 1000
        
        print(f"📋 修复方案:")
        print(f"1. 问题确认:")
        print(f"   - 当前前端超时: {current_timeout/1000:.0f} 秒")
        print(f"   - 实际执行时间: {analysis_result.get('total_time', 0):.2f} 秒")
        print(f"   - 需要调整超时设置")
        
        print(f"\n2. 建议修改:")
        print(f"   - 文件: frontend/src/services/api.ts")
        print(f"   - 位置: updateApi 的 timeout 配置")
        print(f"   - 修改前: timeout: {current_timeout}")
        print(f"   - 修改后: timeout: {suggested_timeout_ms}")
        print(f"   - 说明: {suggested_timeout_seconds/60:.1f}分钟超时")
        
        print(f"\n3. 具体修改代码:")
        print(f"```typescript")
        print(f"const updateApi = axios.create({{")
        print(f"  baseURL: '/api',")
        print(f"  timeout: {suggested_timeout_ms}, // {suggested_timeout_seconds/60:.1f}分钟超时，适应智能更新实际耗时")
        print(f"  headers: {{")
        print(f"    'Content-Type': 'application/json',")
        print(f"  }},")
        print(f"}})")
        print(f"```")
        
        return {
            'action': 'adjust_frontend_timeout',
            'current_timeout': current_timeout,
            'suggested_timeout': suggested_timeout_ms,
            'file_to_modify': 'frontend/src/services/api.ts'
        }
    else:
        print(f"✅ 当前超时设置合适:")
        print(f"   - 实际执行时间: {analysis_result.get('total_time', 0):.2f} 秒")
        print(f"   - 前端超时设置: {current_timeout/1000:.0f} 秒")
        print(f"   - 无需调整超时设置")
        
        print(f"\n💡 其他优化建议:")
        print(f"1. 添加进度反馈机制")
        print(f"2. 优化后端处理性能")
        print(f"3. 考虑分阶段处理")
        
        return {
            'action': 'no_timeout_adjustment_needed',
            'current_timeout': current_timeout,
            'optimization_suggestions': [
                'add_progress_feedback',
                'optimize_backend_performance',
                'consider_staged_processing'
            ]
        }

def implement_timeout_fix(fix_suggestion):
    """实施超时修复"""
    print(f"\n🛠️  实施超时修复")
    print("=" * 60)
    
    if not fix_suggestion:
        print("❌ 无修复建议，跳过实施")
        return False
    
    if fix_suggestion['action'] == 'adjust_frontend_timeout':
        try:
            # 读取当前文件
            with open('frontend/src/services/api.ts', 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 查找并替换超时设置
            old_timeout = fix_suggestion['current_timeout']
            new_timeout = fix_suggestion['suggested_timeout']
            
            # 替换updateApi的超时设置
            old_line = f"  timeout: {old_timeout},"
            new_line = f"  timeout: {new_timeout}, // {new_timeout/60000:.1f}分钟超时，适应智能更新实际耗时"
            
            if old_line in content:
                new_content = content.replace(old_line, new_line)
                
                # 写回文件
                with open('frontend/src/services/api.ts', 'w', encoding='utf-8') as f:
                    f.write(new_content)
                
                print(f"✅ 超时设置修改成功:")
                print(f"   - 文件: frontend/src/services/api.ts")
                print(f"   - 修改: {old_timeout}ms → {new_timeout}ms")
                print(f"   - 时长: {old_timeout/60000:.1f}分钟 → {new_timeout/60000:.1f}分钟")
                
                return True
            else:
                print(f"❌ 未找到目标代码行: {old_line}")
                return False
                
        except Exception as e:
            print(f"❌ 修改文件失败: {e}")
            return False
    else:
        print(f"ℹ️  当前不需要调整超时设置")
        return True

def verify_timeout_fix():
    """验证超时修复效果"""
    print(f"\n✅ 验证超时修复效果")
    print("=" * 60)
    
    try:
        # 读取修改后的文件
        with open('frontend/src/services/api.ts', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找updateApi的超时设置
        import re
        timeout_pattern = r'timeout:\s*(\d+).*?适应智能更新实际耗时'
        match = re.search(timeout_pattern, content)
        
        if match:
            new_timeout = int(match.group(1))
            print(f"✅ 超时设置验证成功:")
            print(f"   - 新的超时时间: {new_timeout}ms ({new_timeout/60000:.1f}分钟)")
            print(f"   - 注释说明: 适应智能更新实际耗时")
            return True
        else:
            print(f"⚠️  未找到修改后的超时设置")
            return False
            
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def provide_user_experience_improvements():
    """提供用户体验改进建议"""
    print(f"\n💡 用户体验改进建议")
    print("=" * 60)
    
    print("1. 进度反馈机制:")
    print("   - 显示当前处理阶段（数据获取、技术指标计算）")
    print("   - 显示处理进度百分比")
    print("   - 显示预计剩余时间")
    
    print("\n2. 用户界面优化:")
    print("   - 禁用智能更新按钮防止重复点击")
    print("   - 显示加载动画和状态文字")
    print("   - 提供取消操作选项")
    
    print("\n3. 错误处理改进:")
    print("   - 区分超时错误和其他错误")
    print("   - 提供重试机制")
    print("   - 显示详细的错误信息")
    
    print("\n4. 性能优化建议:")
    print("   - 考虑异步处理技术指标计算")
    print("   - 实现分批处理机制")
    print("   - 优化数据库查询性能")

def main():
    """主分析函数"""
    print("🔧 智能更新超时问题分析和修复")
    print("=" * 60)
    print("分析时间:", datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
    print("目标: 解决前后端超时不同步问题")
    
    # 1. 分析实际执行时间
    analysis_result = analyze_smart_update_timing()
    
    # 2. 提供修复建议
    if analysis_result:
        fix_suggestion = suggest_timeout_fix(analysis_result)
        
        # 3. 实施修复
        if fix_suggestion and fix_suggestion['action'] == 'adjust_frontend_timeout':
            fix_success = implement_timeout_fix(fix_suggestion)
            
            # 4. 验证修复
            if fix_success:
                verify_timeout_fix()
        
        # 5. 提供用户体验改进建议
        provide_user_experience_improvements()
    
    # 6. 总结
    print(f"\n📊 分析总结")
    print("=" * 60)
    
    if analysis_result:
        if analysis_result.get('needs_timeout_adjustment'):
            print("🔧 需要调整前端超时设置")
            print(f"   - 实际耗时: {analysis_result.get('total_time', 0):.2f} 秒")
            print(f"   - 建议超时: {analysis_result.get('suggested_timeout', 600)} 秒")
        else:
            print("✅ 当前超时设置合适，无需调整")
    else:
        print("⚠️  分析未完成，可能需要检查后端服务状态")
    
    print(f"\n💡 关键要点:")
    print("1. 前后端超时设置需要同步")
    print("2. 智能更新包含数据获取和技术指标计算两个阶段")
    print("3. 技术指标计算阶段通常耗时较长")
    print("4. 需要提供更好的用户反馈机制")

if __name__ == "__main__":
    main()
