#!/usr/bin/env python3
"""
灵活技术分析生成策略优化方案
实现根据可用数据量灵活生成不同技术指标的功能
"""

import sys
import os

# 添加backend目录到Python路径
backend_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'backend')
sys.path.insert(0, backend_dir)

from app import create_app
from database import db
from models import Sector, DailyQuote, TechnicalAnalysis
from services.database_service import database_service
from services.analysis_service import analysis_service
from datetime import datetime, timedelta
import pandas as pd
import logging

logger = logging.getLogger(__name__)

class FlexibleTechnicalAnalysisService:
    """
    灵活的技术分析服务
    根据可用数据量动态生成技术指标
    """
    
    def __init__(self):
        self.min_data_requirements = {
            'ma5': 5,
            'ma10': 10, 
            'ma20': 20,
            'ma60': 60,
            'basic_trend': 3,
            'advanced_trend': 10,
            'oscillation': 10,
            'consecutive': 3,
            'highs_lows': 5
        }
    
    def generate_flexible_historical_analysis(self, sector_code: str, days: int = 30) -> bool:
        """
        灵活生成历史技术分析数据
        根据可用数据量动态调整生成策略
        """
        try:
            # 获取板块信息
            sector = Sector.get_by_code(sector_code)
            if not sector:
                logger.warning(f"未找到板块: {sector_code}")
                return False

            # 获取历史行情数据
            quotes = DailyQuote.get_sector_quotes(sector.id, limit=days)
            if len(quotes) < 3:  # 最少需要3天数据
                logger.warning(f"{sector_code}行情数据严重不足，无法进行任何技术分析 (当前{len(quotes)}条，最少需要3条)")
                return False

            # 按时间正序排列
            quotes_sorted = sorted(quotes, key=lambda x: x.quote_date)

            # 灵活生成技术分析 - 从第3天开始，根据数据量逐步增加指标
            generated_count = 0
            
            for i in range(2, len(quotes_sorted)):  # 从第3个数据开始（最少需要3天数据）
                target_date = quotes_sorted[i].quote_date
                available_data_count = i + 1  # 到当前日期为止的数据量

                # 检查是否已存在该日期的分析
                existing_analysis = TechnicalAnalysis.query.filter_by(
                    sector_id=sector.id,
                    analysis_date=target_date
                ).first()

                if existing_analysis:
                    continue  # 跳过已存在的分析

                # 获取到该日期为止的所有数据
                historical_data = quotes_sorted[:i+1]

                # 转换为DataFrame
                quotes_data = []
                for quote in historical_data:
                    quotes_data.append({
                        '日期': quote.quote_date,
                        '开盘': float(quote.open_price),
                        '最高': float(quote.high_price),
                        '最低': float(quote.low_price),
                        '收盘': float(quote.close_price),
                        '成交量': quote.volume,
                        '成交额': float(quote.turnover) if quote.turnover else None
                    })

                df = pd.DataFrame(quotes_data)

                # 执行灵活的技术分析
                analysis_result = self.flexible_comprehensive_analysis(df, available_data_count)

                # 创建分析记录
                analysis = TechnicalAnalysis(
                    sector_id=sector.id,
                    analysis_date=target_date
                )
                self._update_flexible_analysis_record(analysis, analysis_result, available_data_count)
                db.session.add(analysis)
                generated_count += 1

            db.session.commit()
            logger.info(f"成功为{sector_code}灵活生成{generated_count}条历史技术分析数据")
            return True

        except Exception as e:
            logger.error(f"灵活生成{sector_code}历史技术分析失败: {e}")
            db.session.rollback()
            return False
    
    def flexible_comprehensive_analysis(self, df: pd.DataFrame, data_count: int, price_col: str = '收盘') -> dict:
        """
        根据数据量灵活执行技术分析
        """
        result = {
            'data_count': data_count,
            'available_indicators': [],
            'ma5': None,
            'ma10': None,
            'ma20': None,
            'ma60': None,
            'trend': {'direction': 'unknown', 'strength': 0},
            'oscillation': {'is_oscillating': False, 'range': 0},
            'consecutive': {'up_days': 0, 'down_days': 0},
            'highs_lows': {}
        }
        
        try:
            # 1. 计算可用的移动平均线
            ma_result = self._calculate_flexible_moving_averages(df, data_count, price_col)
            result.update(ma_result)
            
            # 2. 根据数据量执行不同级别的趋势分析
            if data_count >= self.min_data_requirements['basic_trend']:
                trend_result = self._calculate_flexible_trend(df, data_count, price_col)
                result['trend'] = trend_result
                result['available_indicators'].append('trend')
            
            # 3. 震荡分析（需要至少10天数据）
            if data_count >= self.min_data_requirements['oscillation']:
                oscillation_result = analysis_service.analyze_oscillation(df, price_col)
                result['oscillation'] = oscillation_result
                result['available_indicators'].append('oscillation')
            
            # 4. 连续涨跌分析（需要至少3天数据）
            if data_count >= self.min_data_requirements['consecutive']:
                consecutive_result = analysis_service.analyze_consecutive_moves(df, price_col)
                result['consecutive'] = consecutive_result
                result['available_indicators'].append('consecutive')
            
            # 5. 新高新低分析（需要至少5天数据）
            if data_count >= self.min_data_requirements['highs_lows']:
                highs_lows_result = analysis_service.analyze_new_highs_lows(df, price_col)
                result['highs_lows'] = highs_lows_result
                result['available_indicators'].append('highs_lows')
            
            # 6. 计算其他技术指标
            if data_count >= 10:
                df_with_indicators = analysis_service.calculate_volatility(df, price_col)
                df_with_indicators = analysis_service.calculate_atr(df_with_indicators)
                latest = df_with_indicators.iloc[-1]
                
                result['volatility'] = float(latest['volatility']) if not pd.isna(latest['volatility']) else None
                result['atr'] = float(latest['atr']) if not pd.isna(latest['atr']) else None
                
                if result['volatility'] is not None:
                    result['available_indicators'].append('volatility')
                if result['atr'] is not None:
                    result['available_indicators'].append('atr')
            
            return result
            
        except Exception as e:
            logger.error(f"灵活技术分析失败: {e}")
            return result
    
    def _calculate_flexible_moving_averages(self, df: pd.DataFrame, data_count: int, price_col: str = '收盘') -> dict:
        """
        根据数据量灵活计算移动平均线
        """
        result = {
            'ma5': None,
            'ma10': None, 
            'ma20': None,
            'ma60': None,
            'available_indicators': []
        }
        
        try:
            df_with_ma = df.copy()
            latest_idx = len(df) - 1
            
            # MA5 - 需要5天数据
            if data_count >= self.min_data_requirements['ma5']:
                df_with_ma['MA5'] = df_with_ma[price_col].rolling(window=5).mean()
                ma5_value = df_with_ma.iloc[latest_idx]['MA5']
                if not pd.isna(ma5_value):
                    result['ma5'] = float(ma5_value)
                    result['available_indicators'].append('ma5')
            
            # MA10 - 需要10天数据
            if data_count >= self.min_data_requirements['ma10']:
                df_with_ma['MA10'] = df_with_ma[price_col].rolling(window=10).mean()
                ma10_value = df_with_ma.iloc[latest_idx]['MA10']
                if not pd.isna(ma10_value):
                    result['ma10'] = float(ma10_value)
                    result['available_indicators'].append('ma10')
            
            # MA20 - 需要20天数据
            if data_count >= self.min_data_requirements['ma20']:
                df_with_ma['MA20'] = df_with_ma[price_col].rolling(window=20).mean()
                ma20_value = df_with_ma.iloc[latest_idx]['MA20']
                if not pd.isna(ma20_value):
                    result['ma20'] = float(ma20_value)
                    result['available_indicators'].append('ma20')
            
            # MA60 - 需要60天数据
            if data_count >= self.min_data_requirements['ma60']:
                df_with_ma['MA60'] = df_with_ma[price_col].rolling(window=60).mean()
                ma60_value = df_with_ma.iloc[latest_idx]['MA60']
                if not pd.isna(ma60_value):
                    result['ma60'] = float(ma60_value)
                    result['available_indicators'].append('ma60')
            
            return result
            
        except Exception as e:
            logger.error(f"灵活移动平均线计算失败: {e}")
            return result
    
    def _calculate_flexible_trend(self, df: pd.DataFrame, data_count: int, price_col: str = '收盘') -> dict:
        """
        根据数据量灵活计算趋势
        """
        try:
            if data_count < 3:
                return {"direction": "unknown", "strength": 0, "reason": "数据不足"}
            elif data_count < 5:
                # 最简单的趋势判断
                recent_prices = df[price_col].tail(3)
                if recent_prices.iloc[-1] > recent_prices.iloc[0]:
                    return {"direction": "up", "strength": 30, "reason": "短期上涨"}
                else:
                    return {"direction": "down", "strength": 30, "reason": "短期下跌"}
            elif data_count < 10:
                # 使用5日数据的趋势判断
                return analysis_service._analyze_simple_trend(df, price_col)
            elif data_count < 20:
                # 使用10日数据的中等复杂度趋势判断
                return analysis_service._analyze_medium_trend(df, price_col)
            else:
                # 完整的趋势分析
                return analysis_service.analyze_trend(df, price_col)
                
        except Exception as e:
            logger.error(f"灵活趋势分析失败: {e}")
            return {"direction": "unknown", "strength": 0, "reason": f"计算失败: {e}"}
    
    def _update_flexible_analysis_record(self, analysis: TechnicalAnalysis, result: dict, data_count: int):
        """
        根据可用数据灵活更新分析记录
        """
        # 移动平均线 - 根据数据量设置
        analysis.ma5 = result.get('ma5')
        analysis.ma10 = result.get('ma10')
        analysis.ma20 = result.get('ma20')
        analysis.ma60 = result.get('ma60')
        
        # 技术指标
        analysis.volatility = result.get('volatility')
        analysis.atr = result.get('atr')
        
        # 趋势分析
        trend = result.get('trend', {})
        analysis.trend_direction = trend.get('direction')
        analysis.trend_strength = trend.get('strength')
        
        # 震荡分析
        oscillation = result.get('oscillation', {})
        analysis.is_oscillating = oscillation.get('is_oscillating', False)
        analysis.oscillation_range = oscillation.get('range')
        
        # 连续涨跌
        consecutive = result.get('consecutive', {})
        analysis.consecutive_up_days = consecutive.get('up_days', 0)
        analysis.consecutive_down_days = consecutive.get('down_days', 0)
        
        # 新高新低
        highs_lows = result.get('highs_lows', {})
        analysis.is_new_high_5d = highs_lows.get('is_new_high_5d', False)
        analysis.is_new_high_10d = highs_lows.get('is_new_high_10d', False)
        analysis.is_new_high_20d = highs_lows.get('is_new_high_20d', False)
        analysis.is_new_high_60d = highs_lows.get('is_new_high_60d', False)
        analysis.is_new_low_5d = highs_lows.get('is_new_low_5d', False)
        analysis.is_new_low_10d = highs_lows.get('is_new_low_10d', False)
        analysis.is_new_low_20d = highs_lows.get('is_new_low_20d', False)
        analysis.is_new_low_60d = highs_lows.get('is_new_low_60d', False)
        
        # 支撑阻力位
        analysis.support_level = highs_lows.get('support_level')
        analysis.resistance_level = highs_lows.get('resistance_level')
        
        analysis.updated_at = datetime.utcnow()


def test_flexible_analysis():
    """测试灵活技术分析功能"""
    print("=" * 80)
    print("🧪 测试灵活技术分析生成策略")
    print("=" * 80)
    
    app = create_app('local')
    with app.app_context():
        flexible_service = FlexibleTechnicalAnalysisService()
        
        # 测试BK0440板块
        sector_code = 'BK0440'
        sector = Sector.query.filter_by(sector_code=sector_code).first()
        
        if not sector:
            print(f"❌ 未找到板块 {sector_code}")
            return
        
        print(f"📋 测试板块: {sector.sector_name} ({sector.sector_code})")
        
        # 获取当前数据状态
        quotes_count = DailyQuote.query.filter_by(sector_id=sector.id).count()
        analysis_count = TechnicalAnalysis.query.filter_by(sector_id=sector.id).count()
        
        print(f"📊 当前数据状态:")
        print(f"   历史行情: {quotes_count}条")
        print(f"   技术分析: {analysis_count}条")
        
        # 清除现有的技术分析数据以便重新测试
        print(f"\n🗑️ 清除现有技术分析数据...")
        TechnicalAnalysis.query.filter_by(sector_id=sector.id).delete()
        db.session.commit()
        
        # 执行灵活生成
        print(f"\n🚀 执行灵活技术分析生成...")
        success = flexible_service.generate_flexible_historical_analysis(sector_code, quotes_count)
        
        if success:
            # 检查生成结果
            new_analysis_count = TechnicalAnalysis.query.filter_by(sector_id=sector.id).count()
            print(f"✅ 成功生成 {new_analysis_count}条技术分析数据")
            
            # 显示生成的数据样本
            analyses = TechnicalAnalysis.query.filter_by(sector_id=sector.id)\
                                            .order_by(TechnicalAnalysis.analysis_date.desc())\
                                            .limit(5).all()
            
            print(f"\n📈 最新5条技术分析数据:")
            for i, analysis in enumerate(analyses):
                ma_info = []
                if analysis.ma5: ma_info.append(f"MA5:{analysis.ma5:.2f}")
                if analysis.ma10: ma_info.append(f"MA10:{analysis.ma10:.2f}")
                if analysis.ma20: ma_info.append(f"MA20:{analysis.ma20:.2f}")
                if analysis.ma60: ma_info.append(f"MA60:{analysis.ma60:.2f}")
                
                ma_str = " | ".join(ma_info) if ma_info else "无移动平均线数据"
                print(f"   {i+1}. {analysis.analysis_date} | {ma_str} | 趋势:{analysis.trend_direction}")
        else:
            print(f"❌ 灵活生成失败")


if __name__ == "__main__":
    test_flexible_analysis()
