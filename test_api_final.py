#!/usr/bin/env python3
"""
测试修复后的API
"""
import requests
import json
import time

def test_api():
    # 等待服务器启动
    time.sleep(2)

    # 测试修复后的API
    url = 'http://localhost:5000/api/stocks/special-data'
    test_data = {
        'stock_codes': ['000001', '600000', '000002']
    }

    try:
        print('🚀 测试修复后的API...')
        response = requests.post(url, json=test_data, timeout=60)
        print(f'状态码: {response.status_code}')
        if response.status_code == 200:
            result = response.json()
            print('✅ API响应成功!')
            print('📊 涨跌幅数据:')
            for code, data in result['data'].items():
                realtime = data.get('realtime_data', {})
                if realtime.get('涨跌幅') is not None:
                    name = realtime.get('股票名称', '')
                    change = realtime.get('涨跌幅', 0)
                    price = realtime.get('最新价', 0)
                    print(f'  {code} ({name}): {change:+.2f}% | ¥{price:.2f}')
                else:
                    print(f'  {code}: 涨跌幅数据为空')
            summary = result['summary']
            print(f'\n📈 成功率: {summary["realtime_success"]}/{summary["total_stocks"]}')
            
            # 检查是否修复了涨跌幅为空的问题
            empty_count = 0
            for code, data in result['data'].items():
                realtime = data.get('realtime_data', {})
                if realtime.get('涨跌幅') is None:
                    empty_count += 1
            
            if empty_count == 0:
                print('🎉 修复成功！所有股票都有涨跌幅数据')
            else:
                print(f'⚠️ 仍有 {empty_count} 只股票的涨跌幅数据为空')
                
        else:
            print(f'❌ API调用失败: {response.status_code}')
            print(f'错误: {response.text}')
    except Exception as e:
        print(f'❌ 请求失败: {e}')

if __name__ == "__main__":
    test_api()
