# 系统数据获取和缓存策略分析报告

## 1. 系统架构概述
系统采用前后端分离架构。前端为React应用，通过axios与后端API通信，并为不同操作（常规、长时、更新）设置了不同的超时策略。后端为Flask应用，包含API接口层、服务逻辑层和数据持久层。数据主要通过`akshare`库从外部获取，并利用一个云端MySQL数据库作为主要的持久化缓存层。此外，系统还为技术指标等高频访问数据设计了内存缓存，并具备通过Celery/Redis进行异步任务处理的能力。

## 2. 详细的条件判断逻辑
### 2.1 时间判断逻辑
核心是`utils/trading_calendar.py`工具，它精确定义了交易日、交易时间（周一至周五 9:30-11:30, 13:00-15:00），为所有缓存策略提供了时间依据。

### 2.2 缓存策略详解
- **主缓存 (MySQL)**: 在`config.py`中通过`CACHE_STRATEGY`为不同数据类型（如`sector_data`, `historical_data`）定义了不同的TTL（Time-To-Live）和存储表。这是系统最主要的缓存层。
- **板块成分股缓存**: 这是一个特例，它不使用通用缓存装饰器，而是在`routes.py`的`/sectors/<sector_code>/stocks`接口中独立实现。它将成分股列表缓存在`SectorStock`模型对应的表中，并仅以“天”为单位判断缓存是否有效，而非TTL。
- **技术指标缓存**: `technical_indicators_cache_service.py`负责此部分，它使用内存字典（`local_cache`）作为一级缓存以实现快速读取，并定时将数据持久化到数据库，兼顾了性能和数据安全。

### 2.3 数据新鲜度判断
这是“智能更新”功能的核心，主要体现在`data_service.py`的`_should_update_sector_data`方法中：
- **交易时间内**: 阈值极短，仅为**1分钟**。只要缓存数据超过1分钟，即被视为“陈旧”，需要从API获取新数据，以此确保用户看到的是准实时行情。
- **非交易时间**: 逻辑更为复杂，旨在避免不必要的API调用。
    - 首先检查缓存数据是否为当日。如果不是，必须更新。
    - 如果是当日数据，会进一步判断今天是否为交易日以及当前时间是否在收盘后（15:00之后）。
    - 最终目标是：在获取到当天最完整的收盘数据后，就不再进行API调用。

## 3. 核心场景分析
### 3.1 综合分析表格

| 场景描述（时间+操作） | 数据源（API/缓存） | 技术指标计算（是/否） | 响应时间预估 | 触发条件 | 实际代码位置 |
| :--- | :--- | :--- | :--- | :--- | :--- |
| **周一 10:00**<br/>首次打开网站 | 缓存/API | 是 | < 2s (缓存) / 5-10s (API) | `GET /api/sectors/all-realtime` | `data_service.get_enhanced_sector_realtime_data_optimized` |
| **周一 10:01**<br/>再次访问首页 | 缓存 | 否 | < 1s | `GET /api/sectors/all-realtime` | `data_service._should_update_sector_data` (返回False) |
| **周一 10:02**<br/>再次访问首页 | API | 是 | 5-10s | `GET /api/sectors/all-realtime` | `data_service._should_update_sector_data` (返回True, >1min) |
| **周一 14:00**<br/>点击“智能更新” | API | 是 | 5-15s | `POST /api/data/update` | `routes.update_data` -> `force_refresh=True` |
| **周一 16:00**<br/>点击“智能更新” | API/缓存 | 是 | < 2s (缓存) / 5-15s (API) | `POST /api/data/update` | `routes.update_data` -> `smart_update=True` |
| **周一 16:00**<br/>点击“完全重载” | API | 是 | 30-60s | `POST /api/sectors/full-reload` | `routes.full_reload_sectors_data` |
| **周六 10:00**<br/>浏览板块详情 | 缓存/API | 否 | < 1s (缓存) / 3-5s (API) | `GET /api/sectors/<code>/stocks` | `routes.get_sector_stocks` |
| **任何时间**<br/>点击“批量更新指标”| 缓存 | 是 | 10-25s |`POST /api/data/update-indicators`| `routes.update_technical_indicators`|

### 3.2 实际场景举例
- **场景1：周一上午10:00用户首次打开网站**
  系统调用`GET /api/sectors/all-realtime`。`data_service`检查缓存，由于是首次，缓存为空，因此通过`akshare`调用API获取86个板块的实时数据，计算技术指标，然后将结果返回给前端并存入MySQL缓存。响应较慢。
- **场景2：周一上午10:01用户再次访问或刷新页面**
  系统再次调用`GET /api/sectors/all-realtime`。`data_service`中的`_should_update_sector_data`逻辑被触发，检查发现距离上次更新（场景1）不足1分钟，因此直接从MySQL缓存中读取数据并返回。响应极快。
- **场景3：周一下午15:30用户点击“智能更新”按钮**
  前端调用`POST /api/data/update`，`force_user_update`为`true`。后端判断当前为非交易时间，因此`force_refresh`为`false`，执行`smart_update`。`_should_update_sector_data`逻辑会检查缓存数据是否在15:00之后生成。如果不是，则会触发一次API调用以获取最准确的收盘数据；如果是，则直接返回缓存。
- **场景4：周三上午11:00用户点击“完全重载”按钮**
  前端调用`POST /api/sectors/full-reload`。后端完全绕过所有缓存和判断逻辑，直接调用`akshare`重新获取所有86个板块的数据，并重新计算所有技术指标。这是最彻底但最耗时的数据同步方式。
- **场景5：周六用户浏览“煤炭行业”板块成分股**
  前端调用`GET /api/sectors/BK0437/stocks`。后端`get_sector_stocks`函数会检查`SectorStock`表中的缓存。它会找到周五的收盘数据，但因为`data_date`不是今天（周六），所以判定缓存“过期”，然后从API获取新数据（实际上和周五收盘数据一样）。
- **场景6：周六再次浏览“煤炭行业”板块**
  再次调用`GET /api/sectors/BK0437/stocks`。此时，后端会发现`SectorStock`表中已有周六的缓存数据（在场景5中存入），`data_date`与今天匹配，因此直接返回缓存数据，不再调用API。

## 4. 问题识别和改进方案
- **问题1：板块成分股缓存策略过于简单**
  - **识别**: `get_sector_stocks`接口仅通过日期判断缓存是否有效。这导致在非交易日（如周六）首次访问时，即使存在周五的最新数据，也会因日期不符而无效，并触发不必要的API调用。
  - **建议**: 优化此接口的缓存逻辑，使其能识别非交易日，并接受前一个交易日的收盘数据作为有效缓存，避免在周末和节假日发生不必要的API请求。
- **问题2：前端缺少加载状态的精细化管理**
  - **识别**: 不同的更新操作（智能更新、完全重载）耗时差异巨大，但前端可能只显示一个通用的“加载中”状态，用户体验不佳。
  - **建议**: 为不同的更新按钮提供更明确的状态反馈。例如，“智能更新”时显示“正在检查更新...”， “完全重载”时显示一个进度条或提示“正在全量同步，可能需要一分钟...”。
- **问题3：技术指标计算与数据获取耦合**
  - **识别**: `get_enhanced_sector_realtime_data_with_indicators`等函数将数据获取和指标计算耦合在一起，强制刷新时必须等待两者都完成。
  - **建议**: 将指标计算解耦为异步任务。当获取到新数据后，先将原始数据返回给前端以快速展示，然后触发一个后台Celery任务去计算技术指标，计算完成后再通过WebSocket或轮询更新前端的指标数据。这将极大缩短“完全重载”等操作的前端等待时间。
- **问题4：API超时设置可能不够灵活**
  - **识别**: `api.ts`中为不同类型的请求设置了固定的长短超时。但在弱网环境下，即便是常规请求也可能超时。
  - **建议**: 实现一个动态超时或重试机制。在`axios`的响应拦截器中捕获超时错误(`ECONNABORTED`)，并为用户提供一个手动重试的选项，或者实现一个自动的、带退避算法的重试逻辑。 