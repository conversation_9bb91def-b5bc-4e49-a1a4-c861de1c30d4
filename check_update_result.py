#!/usr/bin/env python3
"""
检查智能更新后的结果
"""

import requests
import json
from datetime import datetime

def check_after_update():
    """检查更新后的数据时间戳"""
    try:
        print("检查智能更新后的数据时间戳...")
        response = requests.get("http://localhost:5000/api/sectors/all-realtime", timeout=60)
        data = response.json()
        
        print(f"状态码: {response.status_code}")
        print(f"成功: {data.get('success', False)}")
        print(f"数据源: {data.get('data_source', '未知')}")
        print(f"板块数量: {len(data.get('data', []))}")
        
        if data.get('data'):
            # 收集所有时间戳
            update_times = [
                s.get('数据更新时间') for s in data['data'] 
                if s.get('数据更新时间')
            ]
            
            if update_times:
                latest_time = max(update_times)
                earliest_time = min(update_times)
                unique_times = list(set(update_times))
                
                print(f"\n时间戳分析:")
                print(f"最新时间: {latest_time}")
                print(f"最早时间: {earliest_time}")
                print(f"唯一时间戳数量: {len(unique_times)}")
                print(f"所有唯一时间戳: {unique_times}")
                
                # 检查前几个板块的详细信息
                print(f"\n前5个板块详情:")
                for i, sector in enumerate(data['data'][:5]):
                    print(f"  {i+1}. {sector.get('板块名称', '未知')} - 时间: {sector.get('数据更新时间', '无')}")
            else:
                print("❌ 没有找到任何时间戳数据")
        
        return data.get('success', False)
    except Exception as e:
        print(f"检查失败: {e}")
        return False

def main():
    print(f"🔍 检查智能更新结果 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    check_after_update()
    print(f"🏁 检查完成 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
