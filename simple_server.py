#!/usr/bin/env python3
"""
简单的Flask服务器用于测试修复后的API
"""
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from flask import Flask, jsonify, request
from flask_cors import CORS
import akshare as ak
import time
from datetime import datetime

app = Flask(__name__)
CORS(app)

def convert_stock_code_to_akshare_format(stock_code):
    """将股票代码转换为AkShare个股查询API所需的格式"""
    if len(stock_code) != 6:
        return None
    
    if stock_code.startswith('6'):
        return f"SH{stock_code}"  # 上海股票
    elif stock_code.startswith('0') or stock_code.startswith('3'):
        return f"SZ{stock_code}"  # 深圳股票
    elif stock_code.startswith('4') or stock_code.startswith('8'):
        return f"BJ{stock_code}"  # 北京股票
    else:
        return None

def get_stock_realtime_data_akshare(stock_code):
    """使用AkShare个股查询API获取单只股票的实时数据"""
    try:
        # 转换股票代码格式
        akshare_code = convert_stock_code_to_akshare_format(stock_code)
        if not akshare_code:
            print(f"无法转换股票代码格式: {stock_code}")
            return None
        
        # 调用AkShare API获取个股数据
        stock_data = ak.stock_individual_spot_xq(symbol=akshare_code)
        
        if stock_data is None or stock_data.empty:
            print(f"获取股票 {akshare_code} 数据为空")
            return None
        
        # 将DataFrame转换为字典格式
        stock_dict = dict(zip(stock_data['item'], stock_data['value']))
        
        # 提取需要的字段
        result = {
            '涨跌幅': float(stock_dict.get('涨幅', 0.0)),  # 涨幅字段 → 涨跌幅
            '最新价': float(stock_dict.get('现价', 0.0)),  # 现价字段 → 最新价
            '涨跌额': float(stock_dict.get('涨跌', 0.0)),  # 涨跌字段 → 涨跌额
            '更新时间': stock_dict.get('时间', ''),        # 时间字段 → 更新时间
            '股票名称': stock_dict.get('名称', ''),        # 名称字段
            '昨收价': float(stock_dict.get('昨收', 0.0)),  # 昨收字段
        }
        
        print(f"成功获取股票 {stock_code} 实时数据: 涨跌幅={result['涨跌幅']}%, 最新价={result['最新价']}")
        return result
        
    except Exception as e:
        print(f"获取股票 {stock_code} 实时数据失败: {e}")
        return None

@app.route('/')
def index():
    return {'message': '股票分析后端服务运行中', 'status': 'ok'}

@app.route('/health')
def health():
    return {'status': 'healthy', 'service': 'stock_analysis_backend'}

@app.route('/api/stocks/special-data', methods=['POST'])
def get_stocks_special_data():
    """批量获取股票的特殊数据表信息（N形待选和大笔买入）以及实时涨跌幅数据"""
    try:
        # 获取请求数据
        data = request.get_json()
        if not data or 'stock_codes' not in data:
            return jsonify({
                'success': False,
                'error': '请求参数错误，需要提供stock_codes数组',
                'timestamp': datetime.now().isoformat()
            }), 400

        stock_codes = data['stock_codes']
        if not isinstance(stock_codes, list) or not stock_codes:
            return jsonify({
                'success': False,
                'error': 'stock_codes必须是非空数组',
                'timestamp': datetime.now().isoformat()
            }), 400

        print(f"批量查询特殊数据表信息，股票数量: {len(stock_codes)}")

        # 批量获取实时涨跌幅数据
        print(f"开始获取 {len(stock_codes)} 只股票的实时涨跌幅数据")
        realtime_data = {}
        successful_count = 0
        
        for code in stock_codes:
            realtime_info = get_stock_realtime_data_akshare(code)
            if realtime_info:
                realtime_data[code] = realtime_info
                successful_count += 1
            else:
                # 如果获取失败，提供默认值
                realtime_data[code] = {
                    '涨跌幅': None,
                    '最新价': None,
                    '涨跌额': None,
                    '更新时间': '',
                    '股票名称': '',
                    '昨收价': None
                }
            
            # 添加小延迟避免API限制
            time.sleep(0.1)
        
        print(f"实时数据获取完成，成功: {successful_count}/{len(stock_codes)}")

        # 组装结果数据
        result_data = {}
        for code in stock_codes:
            result_data[code] = {
                'n_shape_status': {'exists': False},  # 简化测试，不查询数据库
                'large_buy_status': {'exists': False},  # 简化测试，不查询数据库
                'realtime_data': realtime_data.get(code, {
                    '涨跌幅': None,
                    '最新价': None,
                    '涨跌额': None,
                    '更新时间': '',
                    '股票名称': '',
                    '昨收价': None
                })
            }

        print(f"成功查询特殊数据表信息，实时数据成功: {successful_count}")

        return jsonify({
            'success': True,
            'data': result_data,
            'summary': {
                'total_stocks': len(stock_codes),
                'n_shape_matches': 0,  # 简化测试
                'large_buy_matches': 0,  # 简化测试
                'realtime_success': successful_count
            },
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        print(f"获取股票特殊数据失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

if __name__ == '__main__':
    print('🚀 简单测试服务器启动在 http://localhost:5000')
    app.run(host='0.0.0.0', port=5000, debug=False)
