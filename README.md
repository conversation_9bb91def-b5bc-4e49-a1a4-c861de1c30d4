# 股票市场复盘分析系统

基于 AKshare API 的股票市场复盘分析网站，按申万行业分类的31个大板块进行趋势、震荡、连续上涨以及创新高的智能判断。

## 🚀 功能特性

- **实时数据**：基于 AKshare API 获取最新股票市场数据
- **智能分析**：自动进行趋势、震荡、连续上涨、创新高判断
- **板块分类**：按申万行业分类的31个大板块进行分析
- **板块日历**：📅 日历视图追踪每日活跃板块动向，支持历史数据查询和详情分析
- **可视化展示**：现代化Web界面，图表展示分析结果
- **Docker部署**：一键启动，开箱即用

## 🛠️ 技术架构

### 前端
- **框架**：React 18 + TypeScript
- **UI库**：Ant Design
- **图表**：ECharts
- **构建工具**：Vite

### 后端
- **框架**：Flask + Python 3.9
- **数据源**：AKshare API
- **数据库**：SQLite（开发环境）
- **API**：RESTful API

### 部署
- **容器化**：Docker + Docker Compose
- **反向代理**：Nginx

## 📋 系统要求

- Docker Desktop
- Docker Compose
- 8GB+ 内存推荐

## 🔧 快速开始

### 1. 克隆项目

```bash
git clone <repository-url>
cd stock_analysis
```

### 2. 启动系统

**Windows用户：**
```bash
# 双击运行或在命令行执行
start.bat
```

**Linux/Mac用户：**
```bash
# 给脚本执行权限（首次运行）
chmod +x start.sh

# 启动系统
./start.sh
```

### 3. 访问系统

启动成功后，在浏览器中访问：

- **前端界面**：http://localhost:3000
- **后端API**：http://localhost:5000
- **API文档**：http://localhost:5000/api/docs

## 🎯 主要功能

### 数据分析模块

1. **趋势判断**
   - 基于移动平均线（5日、10日、20日）分析
   - 自动识别上升/下降趋势

2. **震荡判断** 
   - 根据波动率进行震荡区间识别
   - 标准差计算判断震荡强度

3. **连续上涨判断**
   - 对比连续交易日收盘价变化
   - 识别连续上涨天数

4. **创新高判断**
   - 5日、10日、20日新高检测
   - 历史最高点对比分析

### 前端展示

- **仪表板**：整体市场概览
- **板块详情**：单个板块深度分析
- **板块日历**：📅 日历视图展示每日活跃板块，支持：
  - 日历网格显示活跃板块数量和排名第一板块
  - 点击日期查看当日详细排名数据
  - 连续活跃板块统计和分析
  - 数据导出和高级筛选功能
  - 智能缓存和性能优化
- **筛选功能**：按条件筛选板块
- **图表展示**：K线图、均线图等

## 🔨 开发模式

### 本地开发

```bash
# 启动后端开发服务器
cd backend
pip install -r ../requirements.txt
python app.py

# 启动前端开发服务器
cd frontend
npm install
npm run dev
```

### 查看日志

```bash
# 查看所有容器日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f backend
docker-compose logs -f frontend
```

### 停止系统

**Windows：**
```bash
stop.bat
```

**Linux/Mac：**
```bash
./stop.sh
```

## 📊 API接口

### 主要端点

- `GET /api/sectors` - 获取所有板块列表
- `GET /api/sectors/{sector_id}` - 获取单个板块详情
- `GET /api/analysis/trend` - 获取趋势分析结果
- `GET /api/analysis/oscillation` - 获取震荡分析结果
- `GET /api/analysis/consecutive` - 获取连续上涨分析
- `GET /api/analysis/new-high` - 获取创新高分析

详细API文档请访问：http://localhost:5000/api/docs

## 🔧 配置说明

### 环境变量

在 `backend/config.py` 中可以配置：

- 数据库连接
- API密钥
- 更新频率
- 缓存设置

### Docker配置

在 `docker-compose.yml` 中可以调整：

- 端口映射
- 资源限制
- 环境变量

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

MIT License

## 🆘 常见问题

### Q: Docker启动失败怎么办？
A: 请确保Docker Desktop正在运行，并检查端口3000和5000是否被占用。

### Q: 数据更新频率如何调整？
A: 在 `backend/config.py` 中修改 `UPDATE_INTERVAL` 配置。

### Q: 如何添加新的分析指标？
A: 在 `backend/models/analysis.py` 中添加新的分析函数。

## 📞 支持

如有问题，请提交 Issue 或联系维护者。 #   s t o c k _ a n a l y s i s 
 
 