#!/usr/bin/env python3
"""
database_service.py的灵活技术分析优化补丁
为现有的generate_historical_analysis方法添加灵活生成策略
"""

def generate_flexible_historical_analysis(self, sector_code: str, days: int = 30) -> bool:
    """
    为指定板块生成历史技术分析数据（灵活版本）
    根据可用数据量动态调整生成策略，提升用户体验
    
    优化策略：
    - 3-4天数据：生成基础趋势分析
    - 5-9天数据：生成MA5 + 基础分析
    - 10-19天数据：生成MA5、MA10 + 中级分析  
    - 20-59天数据：生成MA5、MA10、MA20 + 高级分析
    - 60+天数据：生成完整的MA5、MA10、MA20、MA60 + 全面分析
    """
    try:
        # 获取板块信息
        sector = Sector.get_by_code(sector_code)
        if not sector:
            logger.warning(f"未找到板块: {sector_code}")
            return False

        # 获取历史行情数据
        quotes = DailyQuote.get_sector_quotes(sector.id, limit=days)
        if len(quotes) < 3:  # 降低最低要求从20天到3天
            logger.warning(f"{sector_code}行情数据不足，无法进行技术分析 (当前{len(quotes)}条，最少需要3条)")
            return False

        # 按时间正序排列
        quotes_sorted = sorted(quotes, key=lambda x: x.quote_date)

        # 灵活生成技术分析 - 从第3天开始
        generated_count = 0
        for i in range(2, len(quotes_sorted)):  # 从第3个数据开始（最少需要3天数据）
            target_date = quotes_sorted[i].quote_date
            available_data_count = i + 1  # 到当前日期为止的数据量

            # 检查是否已存在该日期的分析
            existing_analysis = TechnicalAnalysis.query.filter_by(
                sector_id=sector.id,
                analysis_date=target_date
            ).first()

            if existing_analysis:
                continue  # 跳过已存在的分析

            # 获取到该日期为止的所有数据
            historical_data = quotes_sorted[:i+1]

            # 转换为DataFrame
            quotes_data = []
            for quote in historical_data:
                quotes_data.append({
                    '日期': quote.quote_date,
                    '开盘': float(quote.open_price),
                    '最高': float(quote.high_price),
                    '最低': float(quote.low_price),
                    '收盘': float(quote.close_price),
                    '成交量': quote.volume,
                    '成交额': float(quote.turnover) if quote.turnover else None
                })

            df = pd.DataFrame(quotes_data)

            # 执行灵活的技术分析
            analysis_result = self._flexible_comprehensive_analysis(df, available_data_count)

            # 创建分析记录
            analysis = TechnicalAnalysis(
                sector_id=sector.id,
                analysis_date=target_date
            )
            self._update_flexible_analysis_record(analysis, analysis_result, available_data_count)
            db.session.add(analysis)
            generated_count += 1

        db.session.commit()
        logger.info(f"成功为{sector_code}灵活生成{generated_count}条历史技术分析数据")
        return True

    except Exception as e:
        logger.error(f"灵活生成{sector_code}历史技术分析失败: {e}")
        db.session.rollback()
        return False

def _flexible_comprehensive_analysis(self, df: pd.DataFrame, data_count: int, price_col: str = '收盘') -> Dict[str, Any]:
    """
    根据数据量灵活执行技术分析
    """
    try:
        result = {
            'data_count': data_count,
            'ma5': None,
            'ma10': None,
            'ma20': None,
            'ma60': None,
            'volatility': None,
            'atr': None,
            'trend': {'direction': 'unknown', 'strength': 0},
            'oscillation': {'is_oscillating': False, 'range': 0},
            'consecutive': {'up_days': 0, 'down_days': 0},
            'highs_lows': {}
        }
        
        # 1. 灵活计算移动平均线
        df_with_ma = df.copy()
        latest_idx = len(df) - 1
        
        # MA5 - 需要5天数据
        if data_count >= 5:
            df_with_ma['MA5'] = df_with_ma[price_col].rolling(window=5).mean()
            ma5_value = df_with_ma.iloc[latest_idx]['MA5']
            if not pd.isna(ma5_value):
                result['ma5'] = float(ma5_value)
        
        # MA10 - 需要10天数据
        if data_count >= 10:
            df_with_ma['MA10'] = df_with_ma[price_col].rolling(window=10).mean()
            ma10_value = df_with_ma.iloc[latest_idx]['MA10']
            if not pd.isna(ma10_value):
                result['ma10'] = float(ma10_value)
        
        # MA20 - 需要20天数据
        if data_count >= 20:
            df_with_ma['MA20'] = df_with_ma[price_col].rolling(window=20).mean()
            ma20_value = df_with_ma.iloc[latest_idx]['MA20']
            if not pd.isna(ma20_value):
                result['ma20'] = float(ma20_value)
        
        # MA60 - 需要60天数据
        if data_count >= 60:
            df_with_ma['MA60'] = df_with_ma[price_col].rolling(window=60).mean()
            ma60_value = df_with_ma.iloc[latest_idx]['MA60']
            if not pd.isna(ma60_value):
                result['ma60'] = float(ma60_value)
        
        # 2. 灵活趋势分析
        if data_count >= 3:
            result['trend'] = self._flexible_trend_analysis(df, data_count, price_col)
        
        # 3. 其他技术指标（需要足够数据）
        if data_count >= 10:
            try:
                # 震荡分析
                result['oscillation'] = analysis_service.analyze_oscillation(df, price_col)
                
                # 波动率和ATR
                df_with_indicators = analysis_service.calculate_volatility(df, price_col)
                df_with_indicators = analysis_service.calculate_atr(df_with_indicators)
                latest = df_with_indicators.iloc[-1]
                
                result['volatility'] = float(latest['volatility']) if not pd.isna(latest['volatility']) else None
                result['atr'] = float(latest['atr']) if not pd.isna(latest['atr']) else None
            except Exception as e:
                logger.warning(f"计算高级技术指标失败: {e}")
        
        # 4. 连续涨跌分析（需要至少3天数据）
        if data_count >= 3:
            try:
                result['consecutive'] = analysis_service.analyze_consecutive_moves(df, price_col)
            except Exception as e:
                logger.warning(f"连续涨跌分析失败: {e}")
        
        # 5. 新高新低分析（需要至少5天数据）
        if data_count >= 5:
            try:
                result['highs_lows'] = analysis_service.analyze_new_highs_lows(df, price_col)
            except Exception as e:
                logger.warning(f"新高新低分析失败: {e}")
        
        return result
        
    except Exception as e:
        logger.error(f"灵活技术分析失败: {e}")
        return result

def _flexible_trend_analysis(self, df: pd.DataFrame, data_count: int, price_col: str = '收盘') -> Dict[str, Any]:
    """
    根据数据量灵活计算趋势
    """
    try:
        if data_count < 3:
            return {"direction": "unknown", "strength": 0, "reason": "数据不足"}
        elif data_count < 5:
            # 最简单的趋势判断 - 比较首尾价格
            first_price = df[price_col].iloc[0]
            last_price = df[price_col].iloc[-1]
            change_pct = (last_price - first_price) / first_price * 100
            
            if change_pct > 1:
                return {"direction": "up", "strength": min(50, abs(change_pct) * 10), "reason": "短期上涨"}
            elif change_pct < -1:
                return {"direction": "down", "strength": min(50, abs(change_pct) * 10), "reason": "短期下跌"}
            else:
                return {"direction": "sideways", "strength": 20, "reason": "短期震荡"}
        elif data_count < 10:
            # 使用5日均线的简单趋势判断
            if data_count >= 5:
                df_ma = df.copy()
                df_ma['MA5'] = df_ma[price_col].rolling(window=5).mean()
                current_price = df[price_col].iloc[-1]
                ma5 = df_ma['MA5'].iloc[-1]
                
                if not pd.isna(ma5):
                    if current_price > ma5 * 1.01:
                        return {"direction": "up", "strength": 40, "reason": "价格高于5日均线"}
                    elif current_price < ma5 * 0.99:
                        return {"direction": "down", "strength": 40, "reason": "价格低于5日均线"}
                    else:
                        return {"direction": "sideways", "strength": 30, "reason": "价格接近5日均线"}
            
            # 回退到简单判断
            return self._flexible_trend_analysis(df, 4, price_col)
        elif data_count < 20:
            # 使用5日和10日均线的中等复杂度趋势判断
            try:
                return analysis_service._analyze_medium_trend(df, price_col)
            except:
                return self._flexible_trend_analysis(df, 9, price_col)
        else:
            # 完整的趋势分析
            try:
                return analysis_service.analyze_trend(df, price_col)
            except:
                return self._flexible_trend_analysis(df, 19, price_col)
                
    except Exception as e:
        logger.error(f"灵活趋势分析失败: {e}")
        return {"direction": "unknown", "strength": 0, "reason": f"计算失败: {e}"}

def _update_flexible_analysis_record(self, analysis: TechnicalAnalysis, result: Dict[str, Any], data_count: int):
    """
    根据可用数据灵活更新分析记录，并添加数据完整性标记
    """
    # 移动平均线 - 根据数据量设置
    analysis.ma5 = result.get('ma5')
    analysis.ma10 = result.get('ma10')
    analysis.ma20 = result.get('ma20')
    analysis.ma60 = result.get('ma60')
    
    # 技术指标
    analysis.volatility = result.get('volatility')
    analysis.atr = result.get('atr')
    
    # 趋势分析
    trend = result.get('trend', {})
    analysis.trend_direction = trend.get('direction')
    analysis.trend_strength = trend.get('strength')
    
    # 震荡分析
    oscillation = result.get('oscillation', {})
    analysis.is_oscillating = oscillation.get('is_oscillating', False)
    analysis.oscillation_range = oscillation.get('range')
    
    # 连续涨跌
    consecutive = result.get('consecutive', {})
    analysis.consecutive_up_days = consecutive.get('up_days', 0)
    analysis.consecutive_down_days = consecutive.get('down_days', 0)
    
    # 新高新低
    highs_lows = result.get('highs_lows', {})
    analysis.is_new_high_5d = highs_lows.get('is_new_high_5d', False)
    analysis.is_new_high_10d = highs_lows.get('is_new_high_10d', False)
    analysis.is_new_high_20d = highs_lows.get('is_new_high_20d', False)
    analysis.is_new_high_60d = highs_lows.get('is_new_high_60d', False)
    analysis.is_new_low_5d = highs_lows.get('is_new_low_5d', False)
    analysis.is_new_low_10d = highs_lows.get('is_new_low_10d', False)
    analysis.is_new_low_20d = highs_lows.get('is_new_low_20d', False)
    analysis.is_new_low_60d = highs_lows.get('is_new_low_60d', False)
    
    # 支撑阻力位
    analysis.support_level = highs_lows.get('support_level')
    analysis.resistance_level = highs_lows.get('resistance_level')
    
    analysis.updated_at = datetime.utcnow()

# 修改现有的update_sector_analysis方法，使其也支持灵活生成
def update_sector_analysis_flexible(self, sector_code: str, generate_historical: bool = True) -> bool:
    """
    更新指定板块的技术分析数据（灵活版本）
    """
    try:
        # 获取板块信息
        sector = Sector.get_by_code(sector_code)
        if not sector:
            logger.warning(f"未找到板块: {sector_code}")
            return False

        # 获取最近的行情数据
        quotes = DailyQuote.get_sector_quotes(sector.id, limit=100)
        if len(quotes) < 3:  # 降低最低要求
            logger.warning(f"{sector_code}行情数据不足，无法进行技术分析 (当前{len(quotes)}条，最少需要3条)")
            return False

        # 检查数据量并给出提示
        if len(quotes) < 5:
            logger.info(f"{sector_code}数据量较少({len(quotes)}条)，将生成基础技术分析")
        elif len(quotes) < 10:
            logger.info(f"{sector_code}数据量适中({len(quotes)}条)，将生成包含MA5的技术分析")
        elif len(quotes) < 20:
            logger.info(f"{sector_code}数据量良好({len(quotes)}条)，将生成包含MA5、MA10的技术分析")
        else:
            logger.info(f"{sector_code}数据量充足({len(quotes)}条)，将生成完整的技术分析")

        # 如果需要生成历史数据
        if generate_historical:
            return self.generate_flexible_historical_analysis(sector_code, len(quotes))

        # 否则只生成最新的分析数据（使用灵活策略）
        quotes_data = []
        for quote in reversed(quotes):  # 按时间正序排列
            quotes_data.append({
                '日期': quote.quote_date,
                '开盘': float(quote.open_price),
                '最高': float(quote.high_price),
                '最低': float(quote.low_price),
                '收盘': float(quote.close_price),
                '成交量': quote.volume,
                '成交额': float(quote.turnover) if quote.turnover else None
            })

        df = pd.DataFrame(quotes_data)

        # 执行灵活的技术分析
        analysis_result = self._flexible_comprehensive_analysis(df, len(quotes))

        # 保存分析结果 - 使用最新行情日期
        latest_quote_date = quotes[0].quote_date  # quotes按日期降序排列

        # 检查是否已存在该日期的分析
        existing_analysis = TechnicalAnalysis.query.filter_by(
            sector_id=sector.id,
            analysis_date=latest_quote_date
        ).first()

        if existing_analysis:
            # 更新现有分析
            self._update_flexible_analysis_record(existing_analysis, analysis_result, len(quotes))
        else:
            # 创建新分析记录
            analysis = TechnicalAnalysis(
                sector_id=sector.id,
                analysis_date=latest_quote_date
            )
            self._update_flexible_analysis_record(analysis, analysis_result, len(quotes))
            db.session.add(analysis)

        db.session.commit()
        logger.info(f"成功更新{sector_code}的灵活技术分析数据")
        return True

    except Exception as e:
        logger.error(f"更新{sector_code}灵活技术分析失败: {e}")
        db.session.rollback()
        return False
