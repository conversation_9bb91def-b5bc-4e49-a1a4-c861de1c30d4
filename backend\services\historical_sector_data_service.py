
import akshare as ak
import pandas as pd
from datetime import datetime, date, timedelta
import logging

logger = logging.getLogger(__name__)

class HistoricalSectorDataService:
    """历史板块数据收集服务"""
    
    def get_sector_historical_data(self, target_date: date) -> pd.DataFrame:
        """
        获取指定日期的板块历史数据
        
        Args:
            target_date: 目标日期
            
        Returns:
            pd.DataFrame: 板块历史数据
        """
        try:
            logger.info(f"获取 {target_date} 的板块历史数据...")
            
            # 获取板块列表
            sector_list = ak.stock_board_industry_name_em()
            
            historical_data = []
            date_str = target_date.strftime('%Y%m%d')
            
            for _, sector in sector_list.iterrows():
                sector_code = sector['板块代码']
                sector_name = sector['板块名称']
                
                try:
                    # 获取板块历史数据
                    hist_data = ak.stock_board_industry_hist_em(
                        symbol=sector_code,
                        period="日k",
                        start_date=date_str,
                        end_date=date_str
                    )
                    
                    if not hist_data.empty:
                        latest = hist_data.iloc[-1]
                        
                        # 计算涨跌幅
                        close_price = latest['收盘']
                        open_price = latest['开盘']
                        price_change_pct = ((close_price - open_price) / open_price) * 100
                        
                        historical_data.append({
                            '板块代码': sector_code,
                            '板块名称': sector_name,
                            '最新价': close_price,
                            '涨跌幅': price_change_pct,
                            '涨跌额': close_price - open_price,
                            '成交量': latest.get('成交量', 0),
                            '成交额': latest.get('成交额', 0),
                            '日期': target_date
                        })
                        
                except Exception as e:
                    logger.warning(f"获取板块 {sector_name} 历史数据失败: {e}")
                    continue
            
            if historical_data:
                df = pd.DataFrame(historical_data)
                logger.info(f"成功获取 {len(df)} 个板块的历史数据")
                return df
            else:
                logger.error("未获取到任何历史数据")
                return pd.DataFrame()
                
        except Exception as e:
            logger.error(f"获取板块历史数据失败: {e}")
            return pd.DataFrame()
