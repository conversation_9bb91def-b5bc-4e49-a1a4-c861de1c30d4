#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据新鲜度判断逻辑修复验证脚本

验证修复后的数据新鲜度判断逻辑：
1. 完善交易时间判断逻辑
2. 检查数据获取时间是否在当日交易结束后（15:00之后）
3. 只有在交易结束后获取的当日数据才能被认为是"最新的"
"""

import sys
import os
from datetime import datetime, time as dt_time
import pandas as pd

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

def test_trading_time_logic():
    """测试交易时间判断逻辑"""
    print("🕐 测试交易时间判断逻辑")
    print("=" * 60)
    
    try:
        from utils.trading_calendar import trading_calendar
        
        current_time = datetime.now()
        current_date = current_time.date()
        current_time_only = current_time.time()
        
        print(f"当前时间: {current_time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 测试交易时间判断
        is_trading_time = trading_calendar.is_trading_time()
        print(f"是否为交易时间: {is_trading_time}")
        
        # 测试交易日判断
        is_trading_day = trading_calendar.is_trading_day(current_date)
        print(f"今天是否为交易日: {is_trading_day}")
        
        # 测试交易结束时间判断
        trading_end_time = current_time.replace(hour=15, minute=0, second=0, microsecond=0)
        is_after_trading_end = current_time >= trading_end_time
        print(f"当前时间是否在交易结束后(15:00): {is_after_trading_end}")
        
        return {
            'current_time': current_time,
            'is_trading_time': is_trading_time,
            'is_trading_day': is_trading_day,
            'is_after_trading_end': is_after_trading_end,
            'trading_end_time': trading_end_time
        }
        
    except Exception as e:
        print(f"❌ 交易时间逻辑测试失败: {e}")
        return None

def simulate_data_freshness_scenarios():
    """模拟不同的数据新鲜度场景"""
    print(f"\n📊 模拟数据新鲜度判断场景")
    print("=" * 60)
    
    current_time = datetime.now()
    
    # 场景1：今天上午10:00获取的数据（交易时间内）
    scenario1_time = current_time.replace(hour=10, minute=0, second=0, microsecond=0)
    
    # 场景2：今天下午15:30获取的数据（交易结束后）
    scenario2_time = current_time.replace(hour=15, minute=30, second=0, microsecond=0)
    
    # 场景3：今天下午14:30获取的数据（交易时间内）
    scenario3_time = current_time.replace(hour=14, minute=30, second=0, microsecond=0)
    
    # 场景4：昨天获取的数据
    from datetime import timedelta
    scenario4_time = current_time - timedelta(days=1)
    
    scenarios = [
        {
            'name': '今天上午10:00获取的数据',
            'data_time': scenario1_time,
            'expected': '如果当前在交易结束后，应该需要更新'
        },
        {
            'name': '今天下午15:30获取的数据',
            'data_time': scenario2_time,
            'expected': '如果当前在交易结束后，应该使用缓存'
        },
        {
            'name': '今天下午14:30获取的数据',
            'data_time': scenario3_time,
            'expected': '如果当前在交易结束后，应该需要更新'
        },
        {
            'name': '昨天获取的数据',
            'data_time': scenario4_time,
            'expected': '应该需要更新'
        }
    ]
    
    for i, scenario in enumerate(scenarios, 1):
        print(f"\n场景{i}: {scenario['name']}")
        print(f"  数据时间: {scenario['data_time'].strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"  预期结果: {scenario['expected']}")
        
        # 模拟判断逻辑
        result = simulate_freshness_check(scenario['data_time'], current_time)
        print(f"  实际结果: {result}")

def simulate_freshness_check(data_time, current_time):
    """模拟数据新鲜度检查逻辑"""
    try:
        from utils.trading_calendar import trading_calendar
        
        # 模拟修复后的逻辑
        if not trading_calendar.is_trading_time():
            # 检查数据是否是今天的
            if data_time.date() == current_time.date():
                # 检查今天是否为交易日
                is_trading_day = trading_calendar.is_trading_day(current_time.date())
                
                if is_trading_day:
                    # 如果今天是交易日，检查当前时间是否在交易结束后
                    trading_end_time = current_time.replace(hour=15, minute=0, second=0, microsecond=0)
                    
                    if current_time >= trading_end_time:
                        # 当前时间在交易结束后，检查数据获取时间是否也在交易结束后
                        if data_time >= trading_end_time:
                            return "使用缓存（数据是交易结束后获取的）"
                        else:
                            return "需要更新（数据是交易结束前获取的）"
                    else:
                        # 当前时间在交易结束前
                        return "使用缓存（交易尚未结束）"
                else:
                    # 今天不是交易日
                    return "使用缓存（今天不是交易日）"
            else:
                return "需要更新（数据不是今天的）"
        else:
            # 交易时间内，使用1分钟阈值
            time_diff = current_time - data_time
            if time_diff.total_seconds() > 60:
                return f"需要更新（交易时间内数据过时 {time_diff.total_seconds():.1f}秒）"
            else:
                return f"使用缓存（交易时间内数据较新 {time_diff.total_seconds():.1f}秒）"
                
    except Exception as e:
        return f"判断失败: {e}"

def test_actual_data_service():
    """测试实际的数据服务新鲜度判断"""
    print(f"\n🔧 测试实际数据服务新鲜度判断")
    print("=" * 60)
    
    try:
        from services.data_service import DataService
        from config import LocalConfig
        
        # 创建配置
        config = LocalConfig()
        mysql_config = {
            'MYSQL_CACHE_CONFIG': config.MYSQL_CACHE_CONFIG,
            'CACHE_STRATEGY': config.CACHE_STRATEGY
        }
        
        # 创建数据服务
        data_service = DataService(mysql_config=mysql_config)
        
        print("✅ 数据服务创建成功")
        
        # 测试数据新鲜度检查
        should_update = data_service._should_update_sector_data()
        print(f"📊 数据新鲜度检查结果: {'需要更新' if should_update else '使用缓存'}")
        
        # 获取缓存数据检查详情
        cached_data = data_service.get_cached_sector_data()
        if not cached_data.empty and '数据更新时间' in cached_data.columns:
            update_times = cached_data['数据更新时间'].dropna()
            if not update_times.empty:
                latest_update_str = str(update_times.iloc[0])
                print(f"📅 缓存数据最新更新时间: {latest_update_str}")
                
                try:
                    latest_update = datetime.strptime(latest_update_str, "%Y-%m-%d %H:%M:%S")
                    current_time = datetime.now()
                    time_diff = current_time - latest_update
                    
                    print(f"⏰ 当前时间: {current_time.strftime('%Y-%m-%d %H:%M:%S')}")
                    print(f"⏱️  时间差: {time_diff.total_seconds():.1f} 秒 ({time_diff.total_seconds()/60:.1f} 分钟)")
                    
                    # 检查是否在交易结束后
                    trading_end_time = current_time.replace(hour=15, minute=0, second=0, microsecond=0)
                    if latest_update >= trading_end_time:
                        print(f"✅ 数据是在交易结束后获取的 ({latest_update.strftime('%H:%M:%S')} >= 15:00)")
                    else:
                        print(f"⚠️  数据是在交易结束前获取的 ({latest_update.strftime('%H:%M:%S')} < 15:00)")
                        
                except ValueError as e:
                    print(f"❌ 时间格式解析失败: {e}")
            else:
                print("⚠️  缓存数据中无有效的更新时间")
        else:
            print("⚠️  缓存数据为空或缺少更新时间字段")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据服务测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def verify_code_changes():
    """验证代码修改"""
    print(f"\n📋 验证代码修改")
    print("=" * 60)
    
    try:
        with open('backend/services/data_service.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键修复点
        fixes_verified = []
        
        # 1. 检查是否添加了交易日判断
        if 'is_trading_day' in content and 'trading_calendar.is_trading_day' in content:
            fixes_verified.append("✅ 已添加交易日判断逻辑")
        else:
            fixes_verified.append("❌ 未添加交易日判断逻辑")
        
        # 2. 检查是否添加了交易结束时间判断
        if 'trading_end_time' in content and 'hour=15, minute=0' in content:
            fixes_verified.append("✅ 已添加交易结束时间判断")
        else:
            fixes_verified.append("❌ 未添加交易结束时间判断")
        
        # 3. 检查是否添加了数据获取时间与交易结束时间的比较
        if '交易结束后获取的' in content and '交易结束前获取的' in content:
            fixes_verified.append("✅ 已添加数据获取时间与交易结束时间的比较")
        else:
            fixes_verified.append("❌ 未添加数据获取时间与交易结束时间的比较")
        
        # 4. 检查是否保持了原有的交易时间内逻辑
        if '交易时间内数据已过时' in content and 'freshness_threshold = 60' in content:
            fixes_verified.append("✅ 保持了原有的交易时间内逻辑")
        else:
            fixes_verified.append("❌ 交易时间内逻辑可能被修改")
        
        for fix in fixes_verified:
            print(fix)
        
        return all('✅' in fix for fix in fixes_verified)
        
    except FileNotFoundError:
        print("❌ 无法找到数据服务文件")
        return False
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🔧 数据新鲜度判断逻辑修复验证")
    print("=" * 60)
    print("验证时间:", datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
    print("修复目标: 完善交易时间判断，确保只有交易结束后的当日数据才被认为是最新的")
    
    # 1. 验证代码修改
    code_ok = verify_code_changes()
    
    # 2. 测试交易时间逻辑
    trading_info = test_trading_time_logic()
    
    # 3. 模拟数据新鲜度场景
    simulate_data_freshness_scenarios()
    
    # 4. 测试实际数据服务
    if code_ok:
        service_ok = test_actual_data_service()
    else:
        service_ok = False
    
    # 5. 总结验证结果
    print(f"\n📊 验证总结")
    print("=" * 60)
    
    if code_ok:
        print("✅ 代码修改验证通过")
    else:
        print("❌ 代码修改验证失败")
    
    if trading_info:
        print(f"✅ 交易时间逻辑测试通过")
        print(f"   - 当前是否交易时间: {trading_info['is_trading_time']}")
        print(f"   - 今天是否交易日: {trading_info['is_trading_day']}")
        print(f"   - 是否在交易结束后: {trading_info['is_after_trading_end']}")
    else:
        print("❌ 交易时间逻辑测试失败")
    
    if service_ok:
        print("✅ 数据服务测试通过")
    else:
        print("⚠️  数据服务测试未完成")
    
    print(f"\n💡 修复要点回顾:")
    print("1. 添加了交易日判断逻辑")
    print("2. 添加了交易结束时间（15:00）判断")
    print("3. 只有在交易结束后获取的当日数据才被认为是最新的")
    print("4. 保持了原有的交易时间内1分钟阈值逻辑")
    print("5. 提高了数据时效性和准确性")

if __name__ == "__main__":
    main()
