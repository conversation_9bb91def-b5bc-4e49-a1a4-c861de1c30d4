#!/usr/bin/env python3
"""
简单的API测试脚本
"""

import requests
import json
from datetime import datetime

def test_health():
    """测试健康检查API"""
    try:
        response = requests.get("http://localhost:5000/api/health", timeout=10)
        print(f"Health API: {response.status_code} - {response.text}")
        return response.status_code == 200
    except Exception as e:
        print(f"Health API 失败: {e}")
        return False

def test_sectors_api():
    """测试板块数据API"""
    try:
        print("测试板块数据API...")
        response = requests.get("http://localhost:5000/api/sectors/all-realtime", timeout=60)
        data = response.json()
        
        print(f"状态码: {response.status_code}")
        print(f"成功: {data.get('success', False)}")
        print(f"数据源: {data.get('data_source', '未知')}")
        print(f"板块数量: {len(data.get('data', []))}")
        
        if data.get('data'):
            # 检查前几个板块的时间戳
            for i, sector in enumerate(data['data'][:3]):
                print(f"板块{i+1}: {sector.get('板块名称', '未知')} - 时间: {sector.get('数据更新时间', '无')}")
        
        return data.get('success', False)
    except Exception as e:
        print(f"板块数据API 失败: {e}")
        return False

def test_smart_update():
    """测试智能更新API"""
    try:
        print("测试智能更新API...")
        response = requests.post(
            "http://localhost:5000/api/data/update",
            json={"update_mode": "incremental", "force_user_update": True},
            timeout=300
        )
        data = response.json()
        
        print(f"状态码: {response.status_code}")
        print(f"成功: {data.get('success', False)}")
        print(f"消息: {data.get('message', '无消息')}")
        
        return data.get('success', False)
    except Exception as e:
        print(f"智能更新API 失败: {e}")
        return False

def main():
    print(f"🚀 API测试开始 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 测试健康检查
    if test_health():
        print("✅ 健康检查通过")
    else:
        print("❌ 健康检查失败")
        return
    
    # 测试板块数据API
    if test_sectors_api():
        print("✅ 板块数据API正常")
    else:
        print("❌ 板块数据API失败")
        return
    
    # 测试智能更新API
    if test_smart_update():
        print("✅ 智能更新API正常")
    else:
        print("❌ 智能更新API失败")
    
    print(f"🏁 测试完成 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
