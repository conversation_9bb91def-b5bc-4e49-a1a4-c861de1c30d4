import React, { useState, useEffect } from 'react'
import {
  Card,
  Table,
  Typography,
  Spin,
  Alert,
  Space,
  Tag,
  Button,
  message,
  Row,
  Col,
  Statistic
} from 'antd'
import {
  ReloadOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
  MinusOutlined,
  Bar<PERSON><PERSON>Outlined
} from '@ant-design/icons'
import { apiService } from '../../services/api'
import { useTheme } from '../../theme/ThemeProvider'
import type { ColumnsType } from 'antd/es/table'

const { Title, Text } = Typography

// 概念分析数据接口
interface ConceptAnalysisData {
  concept: string
  count_current: number
  count_previous: number
  change_from_previous: number
  count_initial: number
  change_from_initial: number
  n_shape_count: number
  created_at: string
  updated_at: string
}

const ConceptAnalysis: React.FC = () => {
  const { colorScheme } = useTheme()
  const [loading, setLoading] = useState(true)
  const [data, setData] = useState<ConceptAnalysisData[]>([])
  const [error, setError] = useState<string | null>(null)

  // 加载概念分析数据
  const loadConceptData = async () => {
    try {
      setLoading(true)
      setError(null)
      
      const response = await apiService.getConceptAnalysis()
      
      if (response.data.success) {
        setData(response.data.data)
        message.success(`成功加载${response.data.total}条概念分析数据`)
      } else {
        setError(response.data.error || '获取概念分析数据失败')
      }
    } catch (err: any) {
      console.error('获取概念分析数据失败:', err)
      setError(err.response?.data?.error || err.message || '获取概念分析数据失败')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    // 设置页面标题
    document.title = '概念分析 - 股票分析系统'
    loadConceptData()

    // 组件卸载时恢复默认标题
    return () => {
      document.title = '股票分析系统'
    }
  }, [])

  // 渲染变化标签
  const renderChangeTag = (change: number) => {
    if (change > 0) {
      return (
        <Tag color="red" icon={<ArrowUpOutlined />}>
          +{change}
        </Tag>
      )
    } else if (change < 0) {
      return (
        <Tag color="green" icon={<ArrowDownOutlined />}>
          {change}
        </Tag>
      )
    } else {
      return (
        <Tag color="default" icon={<MinusOutlined />}>
          0
        </Tag>
      )
    }
  }

  // 表格列定义
  const columns: ColumnsType<ConceptAnalysisData> = [
    {
      title: '排名',
      key: 'rank',
      width: 80,
      render: (_, __, index) => (
        <Text strong style={{ color: colorScheme.primary }}>
          {index + 1}
        </Text>
      ),
    },
    {
      title: '概念名称',
      dataIndex: 'concept',
      key: 'concept',
      width: 150,
      sorter: (a, b) => a.concept.localeCompare(b.concept),
      render: (concept: string) => (
        <Text strong style={{ color: colorScheme.onSurface }}>
          {concept}
        </Text>
      ),
    },
    {
      title: '当前数量',
      dataIndex: 'count_current',
      key: 'count_current',
      width: 120,
      sorter: (a, b) => a.count_current - b.count_current,
      render: (count: number, record: ConceptAnalysisData) => {
        if (count > 0) {
          return (
            <Text
              style={{
                color: colorScheme.primary,
                fontWeight: 'bold',
                cursor: 'pointer',
                textDecoration: 'underline'
              }}
              onClick={() => {
                // 在新标签页中打开概念涨停股票详情页面
                const url = `/concept-stocks/${encodeURIComponent(record.concept)}`
                window.open(url, '_blank')
              }}
            >
              {count}
            </Text>
          )
        } else {
          return (
            <Text style={{
              color: colorScheme.onSurface,
              fontWeight: 'normal'
            }}>
              {count}
            </Text>
          )
        }
      },
    },
    {
      title: 'N型待选',
      dataIndex: 'n_shape_count',
      key: 'n_shape_count',
      width: 120,
      sorter: (a, b) => a.n_shape_count - b.n_shape_count,
      render: (count: number, record: ConceptAnalysisData) => {
        if (count > 0) {
          return (
            <Text
              style={{
                color: colorScheme.primary,
                fontWeight: 'bold',
                cursor: 'pointer',
                textDecoration: 'underline'
              }}
              onClick={() => {
                // 在新标签页中打开概念N型待选股票详情页面
                const url = `/concept-n-stocks/${encodeURIComponent(record.concept)}`
                window.open(url, '_blank')
              }}
            >
              {count}
            </Text>
          )
        } else {
          return (
            <Text style={{
              color: colorScheme.onSurface,
              fontWeight: 'normal'
            }}>
              {count}
            </Text>
          )
        }
      },
    },
    {
      title: '前期数量',
      dataIndex: 'count_previous',
      key: 'count_previous',
      width: 120,
      sorter: (a, b) => a.count_previous - b.count_previous,
      render: (count: number) => (
        <Text style={{ color: colorScheme.onSurface }}>
          {count}
        </Text>
      ),
    },
    {
      title: '较前期变化',
      dataIndex: 'change_from_previous',
      key: 'change_from_previous',
      width: 130,
      sorter: (a, b) => a.change_from_previous - b.change_from_previous,
      render: (change: number) => renderChangeTag(change),
    },
    {
      title: '初始数量',
      dataIndex: 'count_initial',
      key: 'count_initial',
      width: 120,
      sorter: (a, b) => a.count_initial - b.count_initial,
      render: (count: number) => (
        <Text style={{ color: colorScheme.onSurface }}>
          {count}
        </Text>
      ),
    },
    {
      title: '较初始变化',
      dataIndex: 'change_from_initial',
      key: 'change_from_initial',
      width: 130,
      sorter: (a, b) => a.change_from_initial - b.change_from_initial,
      render: (change: number) => renderChangeTag(change),
    },
    {
      title: '更新时间',
      dataIndex: 'updated_at',
      key: 'updated_at',
      width: 180,
      sorter: (a, b) => new Date(a.updated_at).getTime() - new Date(b.updated_at).getTime(),
      render: (time: string) => (
        <Text style={{ color: colorScheme.onSurface, fontSize: '12px' }}>
          {new Date(time).toLocaleString('zh-CN')}
        </Text>
      ),
    },
  ]

  // 计算统计数据
  const totalConcepts = data.length
  const activeConcepts = data.filter(item => item.count_current > 0).length
  const increasingConcepts = data.filter(item => item.change_from_previous > 0).length
  const decreasingConcepts = data.filter(item => item.change_from_previous < 0).length
  const nShapeConcepts = data.filter(item => item.n_shape_count > 0).length
  const totalNShapeStocks = data.reduce((sum, item) => sum + item.n_shape_count, 0)

  if (loading) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        minHeight: '400px',
        backgroundColor: colorScheme.background
      }}>
        <Spin size="large" />
      </div>
    )
  }

  if (error) {
    return (
      <div style={{ padding: '24px', backgroundColor: colorScheme.background }}>
        <Alert
          message="加载失败"
          description={error}
          type="error"
          showIcon
          action={
            <Button size="small" onClick={loadConceptData}>
              重试
            </Button>
          }
        />
      </div>
    )
  }

  return (
    <div style={{ backgroundColor: colorScheme.background, minHeight: '100vh' }}>
      {/* 页面标题 */}
      <div style={{ marginBottom: '24px' }}>
        <Title 
          level={2} 
          style={{ 
            color: colorScheme.onSurface, 
            margin: 0,
            display: 'flex',
            alignItems: 'center',
            gap: '12px'
          }}
        >
          <BarChartOutlined style={{ color: colorScheme.primary }} />
          概念分析
        </Title>
        <Text type="secondary" style={{ color: colorScheme.onSurfaceVariant }}>
          涨停概念统计分析，展示各概念板块的涨停股票数量变化
        </Text>
      </div>

      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={4}>
          <Card
            size="small"
            style={{
              backgroundColor: colorScheme.surfaceContainerLow,
              borderColor: colorScheme.outlineVariant
            }}
          >
            <Statistic
              title="概念总数"
              value={totalConcepts}
              valueStyle={{ color: colorScheme.primary }}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card
            size="small"
            style={{
              backgroundColor: colorScheme.surfaceContainerLow,
              borderColor: colorScheme.outlineVariant
            }}
          >
            <Statistic
              title="活跃概念"
              value={activeConcepts}
              valueStyle={{ color: colorScheme.primary }}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card
            size="small"
            style={{
              backgroundColor: colorScheme.surfaceContainerLow,
              borderColor: colorScheme.outlineVariant
            }}
          >
            <Statistic
              title="上升概念"
              value={increasingConcepts}
              valueStyle={{ color: '#f5222d' }}
              prefix={<ArrowUpOutlined />}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card
            size="small"
            style={{
              backgroundColor: colorScheme.surfaceContainerLow,
              borderColor: colorScheme.outlineVariant
            }}
          >
            <Statistic
              title="下降概念"
              value={decreasingConcepts}
              valueStyle={{ color: '#52c41a' }}
              prefix={<ArrowDownOutlined />}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card
            size="small"
            style={{
              backgroundColor: colorScheme.surfaceContainerLow,
              borderColor: colorScheme.outlineVariant
            }}
          >
            <Statistic
              title="N型概念"
              value={nShapeConcepts}
              valueStyle={{ color: '#fa8c16' }}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card
            size="small"
            style={{
              backgroundColor: colorScheme.surfaceContainerLow,
              borderColor: colorScheme.outlineVariant
            }}
          >
            <Statistic
              title="N型股票"
              value={totalNShapeStocks}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 数据表格 */}
      <Card
        title={
          <Space>
            <Text strong style={{ color: colorScheme.onSurface }}>
              概念分析数据
            </Text>
            <Button
              type="text"
              icon={<ReloadOutlined />}
              onClick={loadConceptData}
              loading={loading}
              style={{ color: colorScheme.primary }}
            >
              刷新
            </Button>
          </Space>
        }
        style={{
          backgroundColor: colorScheme.surface,
          borderColor: colorScheme.outlineVariant
        }}
      >
        <Table
          columns={columns}
          dataSource={data}
          rowKey="concept"
          pagination={false}
          scroll={{ x: 1000, y: 600 }}
          size="small"
          style={{
            backgroundColor: colorScheme.surface
          }}
        />
      </Card>
    </div>
  )
}

export default ConceptAnalysis
