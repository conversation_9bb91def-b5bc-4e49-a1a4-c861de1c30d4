#!/usr/bin/env python3
"""
测试修复后的智能更新功能
"""

import requests
import json
import time
from datetime import datetime

def test_smart_update_fix():
    """测试修复后的智能更新"""
    print("🔧 测试智能更新修复")
    print("=" * 50)
    
    base_url = "http://localhost:5000/api"
    
    try:
        # 1. 获取更新前的时间戳
        print("1. 获取更新前的数据...")
        before_response = requests.get(f"{base_url}/sectors/all-realtime", timeout=30)
        before_data = before_response.json()
        
        if before_data.get('success') and before_data.get('data'):
            before_times = [s.get('数据更新时间') for s in before_data['data'] if s.get('数据更新时间')]
            before_latest = max(before_times) if before_times else "无"
            print(f"   更新前时间: {before_latest}")
        else:
            print("   ❌ 获取更新前数据失败")
            return False
        
        # 2. 执行智能更新
        print("\n2. 执行智能更新...")
        current_time_before_update = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        print(f"   当前时间: {current_time_before_update}")
        
        update_response = requests.post(
            f"{base_url}/data/update",
            json={
                "update_mode": "incremental",
                "force_user_update": True
            },
            timeout=300
        )
        
        update_data = update_response.json()
        print(f"   更新状态: {update_data.get('success', False)}")
        print(f"   更新消息: {update_data.get('message', '无')}")
        
        if not update_data.get('success'):
            print("   ❌ 智能更新失败")
            return False
        
        # 3. 获取更新后的时间戳
        print("\n3. 获取更新后的数据...")
        after_response = requests.get(f"{base_url}/sectors/all-realtime", timeout=30)
        after_data = after_response.json()
        
        if after_data.get('success') and after_data.get('data'):
            after_times = [s.get('数据更新时间') for s in after_data['data'] if s.get('数据更新时间')]
            after_latest = max(after_times) if after_times else "无"
            print(f"   更新后时间: {after_latest}")
            
            # 4. 分析结果
            print("\n4. 结果分析...")
            if before_latest != after_latest:
                print("   ✅ 修复成功！时间戳已更新")
                print(f"   📈 从 {before_latest} 更新到 {after_latest}")
                
                # 检查时间戳是否接近当前时间
                try:
                    update_time = datetime.strptime(after_latest, '%Y-%m-%d %H:%M:%S')
                    current_time = datetime.now()
                    time_diff = abs((current_time - update_time).total_seconds())
                    
                    if time_diff < 300:  # 5分钟内
                        print(f"   ⏰ 时间戳很新鲜（{time_diff:.0f}秒前）")
                    else:
                        print(f"   ⚠️ 时间戳较旧（{time_diff:.0f}秒前）")
                except:
                    print("   ⚠️ 无法解析时间戳格式")
                
                return True
            else:
                print("   ❌ 修复失败：时间戳仍未更新")
                print(f"   🔍 时间戳保持: {before_latest}")
                return False
        else:
            print("   ❌ 获取更新后数据失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    print(f"🚀 开始测试修复 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 测试健康检查
    try:
        health_response = requests.get("http://localhost:5000/api/health", timeout=10)
        if health_response.status_code == 200:
            print("✅ 后端服务正常")
        else:
            print("❌ 后端服务异常")
            return
    except:
        print("❌ 无法连接后端服务")
        return
    
    # 测试修复
    success = test_smart_update_fix()
    
    if success:
        print("\n🎉 修复验证成功！")
        print("💡 前端现在应该能看到更新的时间戳了")
    else:
        print("\n😞 修复验证失败")
        print("💡 需要进一步调试")
    
    print(f"\n🏁 测试完成 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
