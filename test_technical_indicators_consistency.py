#!/usr/bin/env python3
"""
测试技术指标计算的一致性
验证前端和后端的计算结果是否一致
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

import pandas as pd
from datetime import datetime, date, timedelta
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_consecutive_up_logic():
    """测试连续上涨天数计算逻辑"""
    
    print("🧪 测试连续上涨天数计算逻辑的一致性")
    print("=" * 50)
    
    # 创建测试数据（模拟降序排列的quotes数据）
    test_quotes = [
        {'quote_date': '2025-01-07', 'close_price': 105.0, 'price_change': 2.0},   # 最新，上涨
        {'quote_date': '2025-01-06', 'close_price': 103.0, 'price_change': 1.0},   # 上涨
        {'quote_date': '2025-01-05', 'close_price': 102.0, 'price_change': 1.0},   # 上涨
        {'quote_date': '2025-01-04', 'close_price': 101.0, 'price_change': -1.0},  # 下跌（中断连续上涨）
        {'quote_date': '2025-01-03', 'close_price': 102.0, 'price_change': 2.0},   # 上涨
    ]
    
    print("📊 测试数据（降序排列，最新在前）：")
    for i, quote in enumerate(test_quotes):
        print(f"  {i}: {quote['quote_date']} - 收盘价: {quote['close_price']}, 涨跌额: {quote['price_change']}")
    
    # 1. 前端逻辑（当前，有问题）
    print("\n❌ 前端逻辑（当前，有问题）：")
    consecutive_up_days_frontend_wrong = 0
    recent_quotes = test_quotes[0:5]
    for i in range(len(recent_quotes) - 1):
        if recent_quotes[i]['price_change'] > 0:
            consecutive_up_days_frontend_wrong += 1
        else:
            break
    
    print(f"  前端错误计算结果: {consecutive_up_days_frontend_wrong}天")
    print(f"  问题：没有正确处理连续性，只是简单累加正涨跌额")
    
    # 2. 前端逻辑（修复后）
    print("\n🔧 前端逻辑（修复后）：")
    consecutive_up_days_frontend_fixed = 0
    # 从最新数据开始，向前检查连续上涨
    for i in range(len(recent_quotes)):
        if recent_quotes[i]['price_change'] > 0:
            consecutive_up_days_frontend_fixed += 1
        else:
            break  # 遇到非上涨就停止
    
    print(f"  前端修复后结果: {consecutive_up_days_frontend_fixed}天")
    
    # 3. 后端逻辑（模拟）
    print("\n⚙️ 后端逻辑（模拟）：")
    # 后端接收的是升序排列的DataFrame
    df_data = []
    for quote in reversed(test_quotes):  # 转换为升序排列
        df_data.append({
            'quote_date': quote['quote_date'],
            '收盘': quote['close_price']
        })
    
    df = pd.DataFrame(df_data)
    df['price_change'] = df['收盘'].diff()  # 计算涨跌额
    
    print(f"  DataFrame（升序排列）:")
    for i, row in df.iterrows():
        change = row['price_change'] if not pd.isna(row['price_change']) else 'NaN'
        print(f"    {i}: {row['quote_date']} - 收盘: {row['收盘']}, 涨跌额: {change}")
    
    # 模拟后端的连续上涨计算
    consecutive_up = 0
    consecutive_down = 0
    
    # 从最新数据开始向前计算连续天数
    for i in range(len(df) - 1, 0, -1):
        change = df['price_change'].iloc[i]
        if pd.isna(change):
            break
            
        if change > 0:
            if consecutive_down == 0:  # 还在上涨序列中
                consecutive_up += 1
            else:  # 已经开始下跌，停止计算上涨
                break
        elif change < 0:
            if consecutive_up == 0:  # 还在下跌序列中
                consecutive_down += 1
            else:  # 已经开始上涨，停止计算下跌
                break
        else:  # 平盘，停止计算
            break
    
    print(f"  后端计算结果: {consecutive_up}天")
    
    # 4. 结果对比
    print("\n📋 结果对比：")
    print(f"  前端错误结果: {consecutive_up_days_frontend_wrong}天")
    print(f"  前端修复结果: {consecutive_up_days_frontend_fixed}天")
    print(f"  后端正确结果: {consecutive_up}天")
    print(f"  修复后一致: {consecutive_up_days_frontend_fixed == consecutive_up}")
    
    return consecutive_up_days_frontend_fixed == consecutive_up

def test_trend_type_mapping():
    """测试趋势类型字段映射"""
    
    print("\n🧪 测试趋势类型字段映射")
    print("=" * 30)
    
    # 后端计算的字段
    backend_trend_judgments = ["上升趋势", "下降趋势", "数据不足"]
    
    # 前端期望的字段（需要确认）
    frontend_trend_types = ["上涨", "下跌", "震荡"]
    
    print("后端计算字段 (trend_judgment):")
    for judgment in backend_trend_judgments:
        print(f"  - {judgment}")
    
    print("\n前端期望字段 (trend_type):")
    for trend_type in frontend_trend_types:
        print(f"  - {trend_type}")
    
    print("\n❌ 发现字段不匹配问题！")
    print("  后端: trend_judgment -> '上升趋势'/'下降趋势'")
    print("  前端: trend_type -> '上涨'/'下跌'/'震荡'")

def test_price_change_calculation():
    """测试涨跌额计算"""
    
    print("\n🧪 测试涨跌额计算")
    print("=" * 20)
    
    # 测试数据
    test_data = [
        {'date': '2025-01-03', 'close': 100.0},
        {'date': '2025-01-04', 'close': 101.0},  # 应该是 +1.0
        {'date': '2025-01-05', 'close': 102.0},  # 应该是 +1.0
        {'date': '2025-01-06', 'close': 103.0},  # 应该是 +1.0
        {'date': '2025-01-07', 'close': 105.0},  # 应该是 +2.0
    ]
    
    print("原始数据:")
    for data in test_data:
        print(f"  {data['date']}: {data['close']}")
    
    # 计算涨跌额
    for i in range(1, len(test_data)):
        price_change = test_data[i]['close'] - test_data[i-1]['close']
        test_data[i]['price_change'] = price_change
    
    print("\n计算涨跌额后:")
    for i, data in enumerate(test_data):
        change = data.get('price_change', 'N/A')
        print(f"  {data['date']}: {data['close']} (涨跌额: {change})")

if __name__ == "__main__":
    try:
        print("🚀 开始测试技术指标计算一致性")
        
        # 连续上涨逻辑测试
        consecutive_test_passed = test_consecutive_up_logic()
        
        # 趋势类型字段映射测试
        test_trend_type_mapping()
        
        # 涨跌额计算测试
        test_price_change_calculation()
        
        print("\n" + "=" * 50)
        if consecutive_test_passed:
            print("✅ 连续上涨逻辑测试通过！")
        else:
            print("❌ 连续上涨逻辑测试失败！")
            
        print("⚠️ 发现趋势类型字段不匹配问题，需要修复！")
            
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
