import akshare as ak
import pandas as pd
from datetime import datetime
import numpy as np

def get_stock_info(symbol):
    """获取股票实时行情数据"""
    try:
        stock_data = ak.stock_individual_spot_xq(symbol=symbol)
        return stock_data
    except Exception as e:
        print(f"获取股票数据失败: {e}")
        return None

def format_stock_data(stock_data):
    """将股票数据转换为更易于使用的格式"""
    if stock_data is None:
        return None
    
    # 转换为字典格式，便于访问
    stock_dict = dict(zip(stock_data['item'], stock_data['value']))
    return stock_dict

def analyze_stock(stock_dict):
    """分析股票基本信息"""
    if stock_dict is None:
        return
    
    print("\n" + "="*50)
    print(f"股票分析报告: {stock_dict['名称']} ({stock_dict['代码']})")
    print("="*50)
    
    # 基本信息
    print(f"\n基本信息:")
    print(f"交易所: {stock_dict['交易所']}")
    print(f"当前价格: {stock_dict['现价']} 元")
    print(f"涨跌幅: {stock_dict['涨幅']}%")
    print(f"今日振幅: {stock_dict['振幅']}%")
    
    # 交易数据
    print(f"\n今日交易数据:")
    print(f"开盘价: {stock_dict['今开']} 元")
    print(f"最高价: {stock_dict['最高']} 元")
    print(f"最低价: {stock_dict['最低']} 元")
    print(f"昨收价: {stock_dict['昨收']} 元")
    print(f"成交量: {int(float(stock_dict['成交量'])/10000):.2f} 万手")
    print(f"成交额: {float(stock_dict['成交额'])/100000000:.2f} 亿元")
    
    # 估值指标
    print(f"\n估值指标:")
    print(f"市盈率(动): {stock_dict['市盈率(动)']}")
    print(f"市净率: {stock_dict['市净率']}")
    print(f"每股收益: {stock_dict['每股收益']} 元")
    print(f"每股净资产: {stock_dict['每股净资产']} 元")
    
    # 股息信息
    print(f"\n股息信息:")
    print(f"股息(TTM): {stock_dict['股息(TTM)']} 元")
    print(f"股息率(TTM): {stock_dict['股息率(TTM)']}%")
    
    # 技术指标分析
    current_price = float(stock_dict['现价'])
    week_52_high = float(stock_dict['52周最高'])
    week_52_low = float(stock_dict['52周最低'])
    
    # 计算当前价格在52周区间的位置(0-100%)
    price_position = (current_price - week_52_low) / (week_52_high - week_52_low) * 100
    
    print(f"\n技术指标:")
    print(f"52周最高: {week_52_high} 元")
    print(f"52周最低: {week_52_low} 元")
    print(f"当前价格在52周区间的位置: {price_position:.2f}%")
    
    # 市值信息
    print(f"\n市值信息:")
    print(f"流通股本: {float(stock_dict['流通股'])/100000000:.2f} 亿股")
    print(f"流通市值: {float(stock_dict['流通值'])/100000000:.2f} 亿元")

def main():
    # 股票代码列表
    stock_symbols = ["SH600000", "SH601318", "SZ000001"]
    
    for symbol in stock_symbols:
        try:
            # 获取股票数据
            stock_data = get_stock_info(symbol)
            if stock_data is not None:
                # 打印原始数据
                print(f"\n{symbol} 原始数据:")
                print(stock_data)
                
                # 格式化并分析股票数据
                stock_dict = format_stock_data(stock_data)
                analyze_stock(stock_dict)
                
                print("\n" + "-"*50)
        except Exception as e:
            print(f"处理 {symbol} 时出错: {e}")

if __name__ == "__main__":
    main() 