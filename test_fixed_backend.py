#!/usr/bin/env python3
"""
测试修复后的后端服务器
"""
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from flask import Flask, jsonify, request
from flask_cors import CORS
import akshare as ak
import time
from datetime import datetime
from sqlalchemy import create_engine, text
import urllib.parse

app = Flask(__name__)
CORS(app)

# 数据库连接
DATABASE_URL = "mysql+pymysql://root:Flameaway3.@localhost:3306/stock_analysis"
engine = create_engine(DATABASE_URL)

def convert_stock_code_to_akshare_format(stock_code):
    """将股票代码转换为AkShare个股查询API所需的格式"""
    if len(stock_code) != 6:
        return None
    
    if stock_code.startswith('6'):
        return f"SH{stock_code}"  # 上海股票
    elif stock_code.startswith('0') or stock_code.startswith('3'):
        return f"SZ{stock_code}"  # 深圳股票
    elif stock_code.startswith('4') or stock_code.startswith('8'):
        return f"BJ{stock_code}"  # 北京股票
    else:
        return None

def get_stock_realtime_data_akshare(stock_code):
    """使用AkShare个股查询API获取单只股票的实时数据"""
    try:
        # 转换股票代码格式
        akshare_code = convert_stock_code_to_akshare_format(stock_code)
        if not akshare_code:
            print(f"无法转换股票代码格式: {stock_code}")
            return None
        
        # 调用AkShare API获取个股数据
        stock_data = ak.stock_individual_spot_xq(symbol=akshare_code)
        
        if stock_data is None or stock_data.empty:
            print(f"获取股票 {akshare_code} 数据为空")
            return None
        
        # 将DataFrame转换为字典格式
        stock_dict = dict(zip(stock_data['item'], stock_data['value']))
        
        # 提取需要的字段
        result = {
            '涨跌幅': float(stock_dict.get('涨幅', 0.0)),  # 涨幅字段 → 涨跌幅
            '最新价': float(stock_dict.get('现价', 0.0)),  # 现价字段 → 最新价
            '涨跌额': float(stock_dict.get('涨跌', 0.0)),  # 涨跌字段 → 涨跌额
            '更新时间': stock_dict.get('时间', ''),        # 时间字段 → 更新时间
            '股票名称': stock_dict.get('名称', ''),        # 名称字段
            '昨收价': float(stock_dict.get('昨收', 0.0)),  # 昨收字段
        }
        
        print(f"成功获取股票 {stock_code} 实时数据: 涨跌幅={result['涨跌幅']}%, 最新价={result['最新价']}")
        return result
        
    except Exception as e:
        print(f"获取股票 {stock_code} 实时数据失败: {e}")
        return None

@app.route('/')
def index():
    return {'message': '修复后的股票分析后端服务', 'status': 'ok'}

@app.route('/api/concept-n-stocks/<concept_name>', methods=['GET'])
def get_concept_n_stocks(concept_name):
    """获取指定概念的N型待选股票列表（包含实时数据）"""
    start_time = time.time()

    try:
        print(f"开始获取概念 '{concept_name}' 的N型待选股票数据")

        # URL解码概念名称（处理中文字符）
        decoded_concept = urllib.parse.unquote(concept_name)

        # 查询包含指定概念的所有N型待选股票，关联大笔买入数据
        query = text("""
            SELECT
                nsc.secID as stock_code,
                nsc.secShortName as stock_name,
                nsc.涨停日期 as limit_up_date,
                nsc.缩量下跌天数 as decline_days,
                nsc.持续缩量 as continuous_decline,
                nsc.股票名称 as full_stock_name,
                nsc.题材 as theme,
                nsc.题材汇总 as theme_summary,
                nsc.题材_G_ as theme_g,
                nsc.tradeDate as trade_date,
                nsc.ths概念名称 as ths_concept_names,
                nsc.板块名称 as sector_name,
                nsc.em板块名称 as em_sector_names,
                nsc.created_at,
                nsc.updated_at,
                lbo.总买入金额 as large_buy_amount,
                lbo.数量 as large_buy_count,
                lbo.总卖出金额 as large_sell_amount,
                lbo.买卖比 as buy_sell_ratio,
                lbo.总买入占比 as buy_ratio_percent
            FROM n_shape_candidates nsc
            LEFT JOIN large_buy_orders lbo ON (
                nsc.secID = lbo.secID
                OR SUBSTRING_INDEX(nsc.secID, '.', 1) = lbo.代码
            )
            WHERE nsc.题材 = :concept_name
               OR nsc.题材汇总 LIKE :concept_pattern
            ORDER BY nsc.涨停日期 DESC, nsc.缩量下跌天数 DESC
        """)

        with engine.connect() as connection:
            result = connection.execute(query, {
                'concept_name': decoded_concept,
                'concept_pattern': f'%{decoded_concept}%'
            })
            rows = result.fetchall()

        # 转换数据格式（添加大笔买入字段）
        n_stocks_data = []
        stock_codes = []  # 收集股票代码用于获取实时数据
        
        for row in rows:
            # 从secID提取基础股票代码
            stock_code = row[0]
            if '.' in stock_code:
                base_code = stock_code.split('.')[0]
            else:
                base_code = stock_code
            
            stock_codes.append(base_code)
            
            stock_dict = {
                'stock_code': base_code,  # 使用基础代码
                'stock_name': row[1],
                'limit_up_date': row[2],
                'decline_days': row[3] if row[3] else 0,
                'continuous_decline': row[4],
                'full_stock_name': row[5],
                'theme': row[6],
                'theme_summary': row[7],
                'theme_g': row[8],
                'trade_date': row[9],
                'ths_concept_names': row[10],
                'sector_name': row[11],
                'em_sector_names': row[12],
                'created_at': row[13].isoformat() if row[13] else None,
                'updated_at': row[14].isoformat() if row[14] else None,
                # 大笔买入相关字段
                'large_buy_data': {
                    'exists': row[15] is not None,
                    '总买入金额': float(row[15]) if row[15] else 0.0,
                    '数量': int(row[16]) if row[16] else 0,
                    '总卖出金额': float(row[17]) if row[17] else 0.0,
                    '买卖比': float(row[18]) if row[18] else 0.0,
                    '总买入占比': float(row[19]) if row[19] else 0.0
                }
            }
            n_stocks_data.append(stock_dict)

        # 批量获取实时涨跌幅数据
        print(f"开始获取 {len(stock_codes)} 只股票的实时涨跌幅数据")
        realtime_success_count = 0
        
        # 只获取前3只股票的实时数据进行测试
        for i, stock_dict in enumerate(n_stocks_data[:3]):
            stock_code = stock_codes[i]
            realtime_info = get_stock_realtime_data_akshare(stock_code)
            
            if realtime_info:
                # 添加实时数据到股票字典中
                stock_dict.update({
                    '涨跌幅': realtime_info['涨跌幅'],
                    '最新价': realtime_info['最新价'],
                    '涨跌额': realtime_info['涨跌额'],
                    '更新时间': realtime_info['更新时间'],
                    '昨收价': realtime_info['昨收价']
                })
                realtime_success_count += 1
            else:
                # 如果获取失败，添加空值
                stock_dict.update({
                    '涨跌幅': None,
                    '最新价': None,
                    '涨跌额': None,
                    '更新时间': '',
                    '昨收价': None
                })
            
            # 添加小延迟避免API限制
            time.sleep(0.1)
        
        # 为其余股票添加空的实时数据字段
        for stock_dict in n_stocks_data[3:]:
            stock_dict.update({
                '涨跌幅': None,
                '最新价': None,
                '涨跌额': None,
                '更新时间': '',
                '昨收价': None
            })
        
        print(f"实时数据获取完成，成功: {realtime_success_count}/3（测试模式）")

        query_time = (time.time() - start_time) * 1000
        print(f"成功获取概念 '{decoded_concept}' 的 {len(n_stocks_data)} 只N型待选股票，查询耗时: {query_time:.2f}ms，实时数据成功: {realtime_success_count}")

        return jsonify({
            'success': True,
            'data': n_stocks_data,
            'total': len(n_stocks_data),
            'concept_name': decoded_concept,
            'query_time_ms': round(query_time, 2),
            'realtime_success': realtime_success_count,
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        query_time = (time.time() - start_time) * 1000
        print(f"获取概念 '{concept_name}' N型待选股票数据失败: {e}, 耗时: {query_time:.2f}ms")
        return jsonify({
            'success': False,
            'error': str(e),
            'concept_name': concept_name,
            'query_time_ms': round(query_time, 2),
            'timestamp': datetime.now().isoformat()
        }), 500

if __name__ == '__main__':
    print('🚀 修复后的后端服务启动在 http://localhost:5000')
    app.run(host='0.0.0.0', port=5000, debug=False)
