Metadata-Version: 2.4
Name: akshare
Version: 1.17.18
Summary: AKShare is an elegant and simple financial data interface library for Python, built for human beings!
Home-page: https://github.com/akfamily/akshare
Author: AKFamily
Author-email: <EMAIL>
License: MIT
Keywords: stock,option,futures,fund,bond,index,air,finance,spider,quant,quantitative,investment,trading,algotrading,data
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: License :: OSI Approved :: MIT License
Classifier: Operating System :: OS Independent
Requires-Python: >=3.8
Description-Content-Type: text/markdown
License-File: LICENSE
Requires-Dist: aiohttp>=3.11.13
Requires-Dist: beautifulsoup4>=4.9.1
Requires-Dist: lxml>=4.2.1
Requires-Dist: pandas>=0.25
Requires-Dist: requests>=2.22.0
Requires-Dist: html5lib>=1.0.1
Requires-Dist: xlrd>=1.2.0
Requires-Dist: urllib3>=1.25.8
Requires-Dist: tqdm>=4.43.0
Requires-Dist: openpyxl>=3.0.3
Requires-Dist: jsonpath>=0.82
Requires-Dist: tabulate>=0.8.6
Requires-Dist: decorator>=4.4.2
Requires-Dist: nest_asyncio>=1.6.0
Requires-Dist: mini-racer>=0.12.4; platform_system != "Linux"
Requires-Dist: py-mini-racer>=0.6.0; platform_system == "Linux"
Requires-Dist: akracer[py-mini-racer]>=0.0.13; platform_system == "Linux"
Provides-Extra: full
Requires-Dist: akqmt; extra == "full"
Provides-Extra: qmt
Requires-Dist: akqmt; extra == "qmt"
Dynamic: author
Dynamic: author-email
Dynamic: classifier
Dynamic: description
Dynamic: description-content-type
Dynamic: home-page
Dynamic: keywords
Dynamic: license
Dynamic: license-file
Dynamic: provides-extra
Dynamic: requires-dist
Dynamic: requires-python
Dynamic: summary

![AKShare Logo](https://github.com/akfamily/akshare/blob/main/assets/images/akshare_logo.jpg)

[![PyPI - Python Version](https://img.shields.io/pypi/pyversions/akshare.svg)](https://pypi.org/project/akshare/)
[![PyPI](https://img.shields.io/pypi/v/akshare.svg)](https://pypi.org/project/akshare/)
[![Downloads](https://pepy.tech/badge/akshare)](https://pepy.tech/project/akshare)
[![Documentation Status](https://readthedocs.org/projects/akshare/badge/?version=latest)](https://akshare.readthedocs.io/?badge=latest)
[![Ruff](https://img.shields.io/endpoint?url=https://raw.githubusercontent.com/astral-sh/ruff/main/assets/badge/v2.json)](https://github.com/astral-sh/ruff)
[![akshare](https://img.shields.io/badge/Data%20Science-AKShare-green)](https://github.com/akfamily/akshare)
[![Actions Status](https://github.com/akfamily/akshare/actions/workflows/release_and_deploy.yml/badge.svg)](https://github.com/akfamily/akshare/actions)
[![MIT Licence](https://img.shields.io/badge/license-MIT-blue)](https://github.com/akfamily/akshare/blob/main/LICENSE)
[![](https://img.shields.io/github/forks/jindaxiang/akshare)](https://github.com/akfamily/akshare)
[![](https://img.shields.io/github/stars/jindaxiang/akshare)](https://github.com/akfamily/akshare)
[![](https://img.shields.io/github/issues/jindaxiang/akshare)](https://github.com/akfamily/akshare)
[![code style: prettier](https://img.shields.io/badge/code_style-prettier-ff69b4.svg?style=flat-square)](https://github.com/prettier/prettier)

## Overview

[AKShare](https://github.com/akfamily/akshare) requires Python(64 bit) 3.9 or higher and
aims to simplify the process of fetching financial data.

**Write less, get more!**

- Documentation: [中文文档](https://akshare.akfamily.xyz/)

## Installation

### General

```shell
pip install akshare --upgrade
```

### China

```shell
pip install akshare -i http://mirrors.aliyun.com/pypi/simple/ --trusted-host=mirrors.aliyun.com  --upgrade
```

### PR

Please check out [Documentation](https://akshare.akfamily.xyz/contributing.html) if you
want to contribute to AKShare

### Docker

#### Pull images

```shell
docker pull registry.cn-shanghai.aliyuncs.com/akfamily/aktools:jupyter
```

#### Run Container

```shell
docker run -it registry.cn-shanghai.aliyuncs.com/akfamily/aktools:jupyter python
```

#### Test

```python
import akshare as ak

print(ak.__version__)
```

## Usage

### Data

Code:

```python
import akshare as ak

stock_zh_a_hist_df = ak.stock_zh_a_hist(symbol="000001", period="daily", start_date="20170301", end_date='20231022', adjust="")
print(stock_zh_a_hist_df)
```

Output:

```
      日期          开盘   收盘    最高  ...  振幅   涨跌幅  涨跌额  换手率
0     2017-03-01   9.49   9.49   9.55  ...  0.84  0.11  0.01  0.21
1     2017-03-02   9.51   9.43   9.54  ...  1.26 -0.63 -0.06  0.24
2     2017-03-03   9.41   9.40   9.43  ...  0.74 -0.32 -0.03  0.20
3     2017-03-06   9.40   9.45   9.46  ...  0.74  0.53  0.05  0.24
4     2017-03-07   9.44   9.45   9.46  ...  0.63  0.00  0.00  0.17
          ...    ...    ...    ...  ...   ...   ...   ...   ...
1610  2023-10-16  11.00  11.01  11.03  ...  0.73  0.09  0.01  0.26
1611  2023-10-17  11.01  11.02  11.05  ...  0.82  0.09  0.01  0.25
1612  2023-10-18  10.99  10.95  11.02  ...  1.00 -0.64 -0.07  0.34
1613  2023-10-19  10.91  10.60  10.92  ...  3.01 -3.20 -0.35  0.61
1614  2023-10-20  10.55  10.60  10.67  ...  1.51  0.00  0.00  0.27
[1615 rows x 11 columns]
```

### Plot

Code:

```python
import akshare as ak
import mplfinance as mpf  # Please install mplfinance as follows: pip install mplfinance

stock_us_daily_df = ak.stock_us_daily(symbol="AAPL", adjust="qfq")
stock_us_daily_df = stock_us_daily_df.set_index(["date"])
stock_us_daily_df = stock_us_daily_df["2020-04-01": "2020-04-29"]
mpf.plot(stock_us_daily_df, type="candle", mav=(3, 6, 9), volume=True, show_nontrading=False)
```

Output:

![KLine](https://jfds-1252952517.cos.ap-chengdu.myqcloud.com/akshare/readme/home/<USER>

## Features

- **Easy of use**: Just one line code to fetch the data;
- **Extensible**: Easy to customize your own code with other application;
- **Powerful**: Python ecosystem.

## Tutorials

1. [Overview](https://akshare.akfamily.xyz/introduction.html)
2. [Installation](https://akshare.akfamily.xyz/installation.html)
3. [Tutorial](https://akshare.akfamily.xyz/tutorial.html)
4. [Data Dict](https://akshare.akfamily.xyz/data/index.html)
5. [Subjects](https://akshare.akfamily.xyz/topic/index.html)

## Contribution

[AKShare](https://github.com/akfamily/akshare) is still under developing, feel free to open issues and pull requests:

- Report or fix bugs
- Require or publish interface
- Write or fix documentation
- Add test cases

> Notice: We use [Ruff](https://github.com/astral-sh/ruff) to format the code

## Statement

1. All data provided by [AKShare](https://github.com/akfamily/akshare) is just for academic research purpose;
2. The data provided by [AKShare](https://github.com/akfamily/akshare) is for reference only and does not constitute any investment proposal;
3. Any investor based on [AKShare](https://github.com/akfamily/akshare) research should pay more attention to data risk;
4. [AKShare](https://github.com/akfamily/akshare) will insist on providing open-source financial data;
5. Based on some uncontrollable factors, some data interfaces in [AKShare](https://github.com/akfamily/akshare) may be removed;
6. Please follow the relevant open-source protocol used by [AKShare](https://github.com/akfamily/akshare);
7. Provide HTTP API for the person who uses other program language: [AKTools](https://aktools.readthedocs.io/).

## Show your style

Use the badge in your project's README.md:

```markdown
[![Data: akshare](https://img.shields.io/badge/Data%20Science-AKShare-green)](https://github.com/akfamily/akshare)
```

Using the badge in README.rst:

```
.. image:: https://img.shields.io/badge/Data%20Science-AKShare-green
    :target: https://github.com/akfamily/akshare
```

Looks like this:

[![Data: akshare](https://img.shields.io/badge/Data%20Science-AKShare-green)](https://github.com/akfamily/akshare)

## Citation

Please use this **bibtex** if you want to cite this repository in your publications:

```markdown
@misc{akshare,
    author = {Albert King},
    title = {AKShare},
    year = {2019},
    publisher = {GitHub},
    journal = {GitHub repository},
    howpublished = {\url{https://github.com/akfamily/akshare}},
}
```

## Acknowledgement

Special thanks [FuShare](https://github.com/LowinLi/fushare) for the opportunity of learning from the project;

Special thanks [TuShare](https://github.com/waditu/tushare) for the opportunity of learning from the project;

Thanks for the data provided by [东方财富网站](http://data.eastmoney.com);

Thanks for the data provided by [新浪财经网站](https://finance.sina.com.cn);

Thanks for the data provided by [金十数据网站](https://www.jin10.com/);

Thanks for the data provided by [生意社网站](http://www.100ppi.com/);

Thanks for the data provided by [中国银行间市场交易商协会网站](http://www.nafmii.org.cn/);

Thanks for the data provided by [99期货网站](http://www.99qh.com/);

Thanks for the data provided by [中国外汇交易中心暨全国银行间同业拆借中心网站](http://www.chinamoney.com.cn/chinese/);

Thanks for the data provided by [和讯财经网站](http://www.hexun.com/);

Thanks for the data provided by [DACHENG-XIU 网站](https://dachxiu.chicagobooth.edu/);

Thanks for the data provided by [上海证券交易所网站](http://www.sse.com.cn/assortment/options/price/);

Thanks for the data provided by [深证证券交易所网站](http://www.szse.cn/);

Thanks for the data provided by [北京证券交易所网站](http://www.bse.cn/);

Thanks for the data provided by [中国金融期货交易所网站](http://www.cffex.com.cn/);

Thanks for the data provided by [上海期货交易所网站](http://www.shfe.com.cn/);

Thanks for the data provided by [大连商品交易所网站](http://www.dce.com.cn/);

Thanks for the data provided by [郑州商品交易所网站](http://www.czce.com.cn/);

Thanks for the data provided by [上海国际能源交易中心网站](http://www.ine.com.cn/);

Thanks for the data provided by [Timeanddate 网站](https://www.timeanddate.com/);

Thanks for the data provided by [河北省空气质量预报信息发布系统网站](http://110.249.223.67/publish/);

Thanks for the data provided by [Economic Policy Uncertainty 网站](http://www.nanhua.net/nhzc/varietytrend.html);

Thanks for the data provided by [申万指数网站](http://www.swsindex.com/idx0120.aspx?columnid=8832);

Thanks for the data provided by [真气网网站](https://www.zq12369.com/);

Thanks for the data provided by [财富网站](http://www.fortunechina.com/);

Thanks for the data provided by [中国证券投资基金业协会网站](http://gs.amac.org.cn/);

Thanks for the data provided by [Expatistan 网站](https://www.expatistan.com/cost-of-living);

Thanks for the data provided by [北京市碳排放权电子交易平台网站](https://www.bjets.com.cn/article/jyxx/);

Thanks for the data provided by [国家金融与发展实验室网站](http://www.nifd.cn/);

Thanks for the data provided by [义乌小商品指数网站](http://www.ywindex.com/Home/Product/index/);

Thanks for the data provided by [百度迁徙网站](https://qianxi.baidu.com/?from=shoubai#city=0);

Thanks for the data provided by [思知网站](https://www.ownthink.com/);

Thanks for the data provided by [Currencyscoop 网站](https://currencyscoop.com/);

Thanks for the data provided by [新加坡交易所网站](https://www.sgx.com/zh-hans/research-education/derivatives);

## Backer and Sponsor

<a href="https://www.jetbrains.com/?from=albertandking/akshare" target="_blank">
<img src="https://resources.jetbrains.com/storage/products/company/brand/logos/jetbrains.png" alt="JetBrains logo.">
</a>
