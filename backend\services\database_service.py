"""
数据库服务 - 处理数据的存储和检索
"""
from datetime import datetime, date, timedelta
from typing import List, Dict, Any, Optional
import logging
import time
from database import db
from models import Sector, DailyQuote, TechnicalAnalysis
from services.data_service import data_service
from services.analysis_service import analysis_service
import pandas as pd

logger = logging.getLogger(__name__)

class DatabaseService:
    """数据库服务类"""
    
    def __init__(self):
        pass
    
    def init_sectors(self) -> bool:
        """初始化申万行业板块数据"""
        try:
            # 检查是否已有数据
            existing_count = Sector.query.count()
            if existing_count > 0:
                logger.info(f"数据库中已有{existing_count}个板块，跳过初始化")
                return True
            
            # 获取申万行业数据
            sectors_data = data_service.get_all_sectors_data()
            
            # 批量插入数据库
            for sector_info in sectors_data:
                sector = Sector(
                    sector_code=sector_info['code'],
                    sector_name=sector_info['name'],
                    industry_level='一级',
                    description=f"申万一级行业 - {sector_info['name']}",
                    is_active=True
                )
                db.session.add(sector)
            
            db.session.commit()
            logger.info(f"成功初始化{len(sectors_data)}个申万行业板块")
            return True

        except Exception as e:
            logger.error(f"初始化板块数据失败: {e}")
            db.session.rollback()
            return False

    def update_missing_analysis(self) -> Dict[str, Any]:
        """只更新缺少技术分析数据的板块"""
        try:
            # 查找缺少技术分析数据的板块
            sectors_without_analysis = []
            sectors = Sector.get_active_sectors()

            for sector in sectors:
                latest_analysis = TechnicalAnalysis.query.filter_by(sector_id=sector.id)\
                                                        .order_by(TechnicalAnalysis.analysis_date.desc())\
                                                        .first()
                if not latest_analysis:
                    sectors_without_analysis.append(sector)

            if not sectors_without_analysis:
                return {
                    "total_sectors": 0,
                    "success_count": 0,
                    "failed_count": 0,
                    "failed_sectors": [],
                    "message": "所有板块都有技术分析数据",
                    "update_time": datetime.now().isoformat()
                }

            logger.info(f"发现 {len(sectors_without_analysis)} 个板块缺少技术分析数据")

            success_count = 0
            failed_sectors = []
            start_time = datetime.now()

            for sector in sectors_without_analysis:
                try:
                    logger.info(f"为板块 {sector.sector_code} 生成技术分析数据")
                    if self.update_sector_analysis(sector.sector_code):
                        success_count += 1
                        logger.info(f"✅ {sector.sector_code} 分析数据生成成功")
                    else:
                        failed_sectors.append(f"{sector.sector_code}(分析失败)")
                        logger.warning(f"⚠️ {sector.sector_code} 分析数据生成失败")
                except Exception as e:
                    logger.error(f"为{sector.sector_code}生成分析数据失败: {e}")
                    failed_sectors.append(f"{sector.sector_code}({str(e)})")

            total_elapsed = (datetime.now() - start_time).total_seconds()

            result = {
                "total_sectors": len(sectors_without_analysis),
                "success_count": success_count,
                "failed_count": len(failed_sectors),
                "failed_sectors": failed_sectors,
                "elapsed_seconds": round(total_elapsed, 2),
                "update_time": datetime.now().isoformat()
            }

            logger.info(f"缺失分析数据更新完成: 成功 {success_count}/{len(sectors_without_analysis)} 个板块，耗时 {total_elapsed:.2f} 秒")
            return result

        except Exception as e:
            logger.error(f"更新缺失分析数据失败: {e}")
            return {
                "total_sectors": 0,
                "success_count": 0,
                "failed_count": 1,
                "failed_sectors": [f"系统错误: {str(e)}"],
                "update_time": datetime.now().isoformat()
            }
    
    def update_sector_quotes(self, sector_code: str, days: int = 90) -> bool:
        """更新指定板块的行情数据"""
        try:
            # 获取板块信息
            sector = Sector.get_by_code(sector_code)
            if not sector:
                logger.warning(f"未找到板块: {sector_code}")
                return False

            # 计算日期范围 - 确保获取足够的数据进行技术分析
            # 技术分析需要至少60个交易日，考虑周末和节假日，至少需要90天
            end_date = datetime.now().strftime("%Y%m%d")
            start_date = (datetime.now() - timedelta(days=days)).strftime("%Y%m%d")

            # 获取历史数据
            logger.debug(f"开始获取{sector_code}历史数据，日期范围: {start_date} - {end_date}")
            df = data_service.get_sector_historical_data(sector_code, start_date, end_date)

            if df.empty:
                logger.warning(f"未获取到{sector_code}的历史数据")
                logger.debug(f"可能原因：1.API限流 2.板块代码无效 3.数据源暂时不可用")
                return False

            logger.debug(f"成功获取{sector_code}历史数据，共{len(df)}条记录")
            
            # 处理数据并存储
            for _, row in df.iterrows():
                quote_date = pd.to_datetime(row['日期']).date()
                
                # 检查是否已存在
                existing_quote = DailyQuote.query.filter_by(
                    sector_id=sector.id,
                    quote_date=quote_date
                ).first()
                
                if existing_quote:
                    # 更新现有记录
                    existing_quote.open_price = float(row['开盘'])
                    existing_quote.high_price = float(row['最高'])
                    existing_quote.low_price = float(row['最低'])
                    existing_quote.close_price = float(row['收盘'])
                    existing_quote.volume = int(row['成交量']) if '成交量' in row else None
                    existing_quote.turnover = float(row['成交额']) if '成交额' in row else None
                    existing_quote.price_change = float(row['涨跌额']) if '涨跌额' in row else None
                    existing_quote.price_change_pct = float(row['涨跌幅']) if '涨跌幅' in row else None
                    existing_quote.updated_at = datetime.utcnow()
                else:
                    # 创建新记录
                    quote = DailyQuote(
                        sector_id=sector.id,
                        quote_date=quote_date,
                        open_price=float(row['开盘']),
                        high_price=float(row['最高']),
                        low_price=float(row['最低']),
                        close_price=float(row['收盘']),
                        volume=int(row['成交量']) if '成交量' in row else None,
                        turnover=float(row['成交额']) if '成交额' in row else None,
                        price_change=float(row['涨跌额']) if '涨跌额' in row else None,
                        price_change_pct=float(row['涨跌幅']) if '涨跌幅' in row else None
                    )
                    db.session.add(quote)
            
            db.session.commit()
            logger.info(f"成功更新{sector_code}的行情数据，共{len(df)}条记录")
            return True
            
        except Exception as e:
            logger.error(f"更新{sector_code}行情数据失败: {e}")
            db.session.rollback()
            return False
    
    def update_sector_analysis(self, sector_code: str, generate_historical: bool = True, flexible: bool = True) -> bool:
        """
        更新指定板块的技术分析数据

        Args:
            sector_code: 板块代码
            generate_historical: 是否生成历史数据
            flexible: 是否使用灵活策略（默认True）
        """
        try:
            # 获取板块信息
            sector = Sector.get_by_code(sector_code)
            if not sector:
                logger.warning(f"未找到板块: {sector_code}")
                return False

            # 获取最近的行情数据
            quotes = DailyQuote.get_sector_quotes(sector.id, limit=100)

            # 根据策略检查数据量要求
            if flexible:
                min_required = 3  # 灵活策略最少需要3天数据
                if len(quotes) < min_required:
                    logger.warning(f"{sector_code}行情数据不足，无法进行技术分析 (当前{len(quotes)}条，灵活策略最少需要{min_required}条)")
                    return False

                # 给出数据量提示
                if len(quotes) < 5:
                    logger.info(f"{sector_code}数据量较少({len(quotes)}条)，将生成基础技术分析")
                elif len(quotes) < 10:
                    logger.info(f"{sector_code}数据量适中({len(quotes)}条)，将生成包含MA5的技术分析")
                elif len(quotes) < 20:
                    logger.info(f"{sector_code}数据量良好({len(quotes)}条)，将生成包含MA5、MA10的技术分析")
                else:
                    logger.info(f"{sector_code}数据量充足({len(quotes)}条)，将生成完整的技术分析")
            else:
                min_required = 5  # 原始策略最少需要5天数据
                if len(quotes) < min_required:
                    logger.warning(f"{sector_code}行情数据不足，无法进行技术分析 (当前{len(quotes)}条，原始策略最少需要{min_required}条)")
                    return False

                if len(quotes) < 20:
                    logger.warning(f"{sector_code}数据量不足以进行完整技术分析 (当前{len(quotes)}条，建议至少20条)")

            # 如果需要生成历史数据
            if generate_historical:
                if flexible or len(quotes) >= 20:
                    return self.generate_historical_analysis(sector_code, len(quotes), flexible)

            # 否则只生成最新的分析数据
            # 转换为DataFrame
            quotes_data = []
            for quote in reversed(quotes):  # 按时间正序排列
                quotes_data.append({
                    '日期': quote.quote_date,
                    '开盘': float(quote.open_price),
                    '最高': float(quote.high_price),
                    '最低': float(quote.low_price),
                    '收盘': float(quote.close_price),
                    '成交量': quote.volume,
                    '成交额': float(quote.turnover) if quote.turnover else None
                })

            df = pd.DataFrame(quotes_data)

            # 根据策略执行技术分析
            if flexible:
                # 灵活策略：根据数据量动态调整分析
                analysis_result = self._flexible_comprehensive_analysis(df, len(quotes))
            else:
                # 原始策略：使用完整的技术分析
                analysis_result = analysis_service.comprehensive_analysis(df)

            # 保存分析结果 - 使用最新行情日期而不是当前日期
            latest_quote_date = quotes[0].quote_date  # quotes按日期降序排列，第一个是最新的

            # 检查是否已存在该日期的分析
            existing_analysis = TechnicalAnalysis.query.filter_by(
                sector_id=sector.id,
                analysis_date=latest_quote_date
            ).first()

            if existing_analysis:
                # 更新现有分析
                self._update_analysis_record(existing_analysis, analysis_result)
            else:
                # 创建新分析记录
                analysis = TechnicalAnalysis(
                    sector_id=sector.id,
                    analysis_date=latest_quote_date
                )
                self._update_analysis_record(analysis, analysis_result)
                db.session.add(analysis)

            db.session.commit()
            logger.info(f"成功更新{sector_code}的技术分析数据")
            return True

        except Exception as e:
            logger.error(f"更新{sector_code}技术分析失败: {e}")
            db.session.rollback()
            return False
    
    def _update_analysis_record(self, analysis: TechnicalAnalysis, result: Dict[str, Any]):
        """更新分析记录的字段"""
        # 移动平均线
        analysis.ma5 = result.get('ma5')
        analysis.ma10 = result.get('ma10')
        analysis.ma20 = result.get('ma20')
        analysis.ma60 = result.get('ma60')
        
        # 技术指标
        analysis.volatility = result.get('volatility')
        analysis.atr = result.get('atr')
        
        # 趋势分析
        trend = result.get('trend', {})
        # 将英文趋势方向转换为中文
        direction_mapping = {
            'up': '上升趋势',
            'down': '下降趋势',
            'sideways': '震荡',
            'unknown': '数据不足'
        }
        raw_direction = trend.get('direction')
        analysis.trend_direction = direction_mapping.get(raw_direction, raw_direction)
        analysis.trend_strength = trend.get('strength')
        
        # 震荡分析
        oscillation = result.get('oscillation', {})
        analysis.is_oscillating = oscillation.get('is_oscillating', False)
        analysis.oscillation_range = oscillation.get('range')
        
        # 连续涨跌
        consecutive = result.get('consecutive', {})
        analysis.consecutive_up_days = consecutive.get('consecutive_up', 0)
        analysis.consecutive_down_days = consecutive.get('consecutive_down', 0)
        
        # 新高新低
        highs_lows = result.get('highs_lows', {})
        analysis.is_new_high_5d = highs_lows.get('new_high_5d', False)
        analysis.is_new_high_10d = highs_lows.get('new_high_10d', False)
        analysis.is_new_high_20d = highs_lows.get('new_high_20d', False)
        analysis.is_new_high_60d = highs_lows.get('new_high_60d', False)
        analysis.is_new_low_5d = highs_lows.get('new_low_5d', False)
        analysis.is_new_low_10d = highs_lows.get('new_low_10d', False)
        analysis.is_new_low_20d = highs_lows.get('new_low_20d', False)
        analysis.is_new_low_60d = highs_lows.get('new_low_60d', False)
        
        analysis.updated_at = datetime.utcnow()

    def generate_historical_analysis(self, sector_code: str, days: int = 30, flexible: bool = True) -> bool:
        """
        为指定板块生成历史技术分析数据

        Args:
            sector_code: 板块代码
            days: 历史数据天数
            flexible: 是否使用灵活生成策略（默认True）
                     True: 根据数据量灵活生成（3天起）
                     False: 使用原始策略（20天起）
        """
        try:
            # 获取板块信息
            sector = Sector.get_by_code(sector_code)
            if not sector:
                logger.warning(f"未找到板块: {sector_code}")
                return False

            # 获取历史行情数据
            quotes = DailyQuote.get_sector_quotes(sector.id, limit=days)

            # 根据策略检查数据量要求
            if flexible:
                min_required = 3  # 灵活策略最少需要3天数据
                if len(quotes) < min_required:
                    logger.warning(f"{sector_code}行情数据不足，无法进行技术分析 (当前{len(quotes)}条，灵活策略最少需要{min_required}条)")
                    return False
                logger.info(f"{sector_code}使用灵活策略生成技术分析，数据量: {len(quotes)}条")
            else:
                min_required = 20  # 原始策略需要20天数据
                if len(quotes) < min_required:
                    logger.warning(f"{sector_code}行情数据不足，无法进行技术分析 (当前{len(quotes)}条，原始策略需要{min_required}条)")
                    return False
                logger.info(f"{sector_code}使用原始策略生成技术分析，数据量: {len(quotes)}条")

            # 按时间正序排列
            quotes_sorted = sorted(quotes, key=lambda x: x.quote_date)

            # 为每个有足够数据的日期生成技术分析
            generated_count = 0

            # 根据策略确定起始点
            if flexible:
                start_index = 2  # 灵活策略从第3个数据开始（需要3天数据）
                logger.info(f"{sector_code}灵活策略: 从第{start_index + 1}天开始生成技术分析")
            else:
                start_index = 19  # 原始策略从第20个数据开始（需要20天数据计算MA20）
                logger.info(f"{sector_code}原始策略: 从第{start_index + 1}天开始生成技术分析")

            for i in range(start_index, len(quotes_sorted)):
                target_date = quotes_sorted[i].quote_date

                # 检查是否已存在该日期的分析
                existing_analysis = TechnicalAnalysis.query.filter_by(
                    sector_id=sector.id,
                    analysis_date=target_date
                ).first()

                if existing_analysis:
                    continue  # 跳过已存在的分析

                # 获取到该日期为止的所有数据
                historical_data = quotes_sorted[:i+1]

                # 转换为DataFrame
                quotes_data = []
                for quote in historical_data:
                    quotes_data.append({
                        '日期': quote.quote_date,
                        '开盘': float(quote.open_price),
                        '最高': float(quote.high_price),
                        '最低': float(quote.low_price),
                        '收盘': float(quote.close_price),
                        '成交量': quote.volume,
                        '成交额': float(quote.turnover) if quote.turnover else None
                    })

                df = pd.DataFrame(quotes_data)

                # 根据策略执行技术分析
                if flexible:
                    # 灵活策略：根据数据量动态调整分析
                    available_data_count = i + 1
                    analysis_result = self._flexible_comprehensive_analysis(df, available_data_count)
                else:
                    # 原始策略：使用完整的技术分析
                    analysis_result = analysis_service.comprehensive_analysis(df)

                # 创建分析记录
                analysis = TechnicalAnalysis(
                    sector_id=sector.id,
                    analysis_date=target_date
                )
                self._update_analysis_record(analysis, analysis_result)
                db.session.add(analysis)
                generated_count += 1

            db.session.commit()
            logger.info(f"成功为{sector_code}生成{generated_count}条历史技术分析数据")
            return True

        except Exception as e:
            logger.error(f"生成{sector_code}历史技术分析失败: {e}")
            db.session.rollback()
            return False

    def update_all_sectors(self, max_time_seconds: int = 90, chunk_size: int = 2) -> Dict[str, Any]:
        """更新所有板块的数据（增强版本：分块处理+智能超时控制）"""
        try:
            sectors = Sector.get_active_sectors()
            success_count = 0
            failed_sectors = []
            timeout_sectors = []
            start_time = datetime.now()

            logger.info(f"开始批量更新 {len(sectors)} 个板块，最大耗时 {max_time_seconds} 秒，分块大小 {chunk_size}")

            # 分块处理板块
            for chunk_start in range(0, len(sectors), chunk_size):
                chunk_end = min(chunk_start + chunk_size, len(sectors))
                chunk_sectors = sectors[chunk_start:chunk_end]

                logger.info(f"处理第 {chunk_start//chunk_size + 1} 块，板块 {chunk_start+1}-{chunk_end}")

                # 检查整体超时
                elapsed = (datetime.now() - start_time).total_seconds()
                if elapsed > max_time_seconds:
                    remaining_sectors = [s.sector_code for s in sectors[chunk_start:]]
                    timeout_sectors.extend(remaining_sectors)
                    logger.warning(f"批量更新超时，剩余 {len(remaining_sectors)} 个板块未处理")
                    break

                # 处理当前块中的每个板块
                chunk_success = 0
                for i, sector in enumerate(chunk_sectors):
                    global_index = chunk_start + i

                    # 检查单个板块的超时（每个板块最多60秒）
                    sector_start_time = datetime.now()
                    sector_timeout = 60

                    try:
                        logger.info(f"更新板块 {global_index+1}/{len(sectors)}: {sector.sector_code}")

                        # 使用更短的数据范围以提高成功率
                        sector_success = False

                        # 先尝试更新行情数据
                        try:
                            if self.update_sector_quotes(sector.sector_code, days=90):  # 确保获取足够数据进行技术分析
                                # 再尝试更新技术分析
                                if self.update_sector_analysis(sector.sector_code, generate_historical=False):
                                    sector_success = True
                                    success_count += 1
                                    chunk_success += 1
                                    logger.info(f"✅ {sector.sector_code} 更新成功")
                                else:
                                    failed_sectors.append(f"{sector.sector_code}(分析失败)")
                                    logger.warning(f"⚠️ {sector.sector_code} 分析失败")
                            else:
                                failed_sectors.append(f"{sector.sector_code}(行情失败)")
                                logger.warning(f"⚠️ {sector.sector_code} 行情失败")
                        except Exception as sector_e:
                            # 检查是否是超时错误
                            sector_elapsed = (datetime.now() - sector_start_time).total_seconds()
                            if sector_elapsed > sector_timeout:
                                failed_sectors.append(f"{sector.sector_code}(单板块超时)")
                                logger.warning(f"⚠️ {sector.sector_code} 单板块超时 ({sector_elapsed:.1f}s)")
                            else:
                                failed_sectors.append(f"{sector.sector_code}({str(sector_e)[:50]})")
                                logger.error(f"更新{sector.sector_code}失败: {sector_e}")

                        # 动态调整间隔时间
                        if i < len(chunk_sectors) - 1:  # 不是块中最后一个板块
                            # 根据成功率调整间隔
                            current_count = i + 1
                            if current_count > 0:
                                success_rate = chunk_success / current_count
                                if success_rate > 0.8:  # 成功率高，减少间隔
                                    time.sleep(0.5)
                                elif success_rate > 0.5:  # 成功率中等，正常间隔
                                    time.sleep(1)
                                else:  # 成功率低，增加间隔
                                    time.sleep(2)
                            else:
                                time.sleep(1)  # 默认间隔

                    except Exception as e:
                        logger.error(f"更新{sector.sector_code}异常: {e}")
                        failed_sectors.append(f"{sector.sector_code}({str(e)[:50]})")

                # 块间休息，避免API限流
                if chunk_end < len(sectors):
                    logger.info(f"第 {chunk_start//chunk_size + 1} 块完成，成功 {chunk_success}/{len(chunk_sectors)}，休息3秒...")
                    time.sleep(3)

            total_elapsed = (datetime.now() - start_time).total_seconds()

            result = {
                "total_sectors": len(sectors),
                "success_count": success_count,
                "failed_count": len(failed_sectors),
                "timeout_count": len(timeout_sectors),
                "failed_sectors": failed_sectors,
                "timeout_sectors": timeout_sectors,
                "elapsed_seconds": round(total_elapsed, 2),
                "success_rate": round(success_count / len(sectors) * 100, 1) if len(sectors) > 0 else 0,
                "update_time": datetime.now().isoformat()
            }

            # 详细的更新结果日志
            if success_count > 0:
                logger.info(f"✅ 批量更新完成: 成功{success_count}/{len(sectors)}个板块 ({result['success_rate']}%)")
                logger.info(f"📊 更新统计: 成功{success_count}个, 失败{len(failed_sectors)}个, 超时{len(timeout_sectors)}个")
            else:
                logger.warning(f"⚠️ 批量更新完成: 成功{success_count}/{len(sectors)}个板块 ({result['success_rate']}%)")
                logger.warning(f"📊 更新统计: 成功{success_count}个, 失败{len(failed_sectors)}个, 超时{len(timeout_sectors)}个")
                if failed_sectors:
                    logger.warning(f"❌ 失败板块: {failed_sectors[:5]}{'...' if len(failed_sectors) > 5 else ''}")

            return result

        except Exception as e:
            logger.error(f"批量更新失败: {e}")
            raise

    def _flexible_comprehensive_analysis(self, df: pd.DataFrame, data_count: int, price_col: str = '收盘') -> Dict[str, Any]:
        """
        根据数据量灵活执行技术分析
        """
        try:
            result = {
                'data_count': data_count,
                'ma5': None,
                'ma10': None,
                'ma20': None,
                'ma60': None,
                'volatility': None,
                'atr': None,
                'trend': {'direction': 'unknown', 'strength': 0},
                'oscillation': {'is_oscillating': False, 'range': 0},
                'consecutive': {'up_days': 0, 'down_days': 0},
                'highs_lows': {}
            }

            # 1. 灵活计算移动平均线
            df_with_ma = df.copy()
            latest_idx = len(df) - 1

            # MA5 - 需要5天数据
            if data_count >= 5:
                df_with_ma['MA5'] = df_with_ma[price_col].rolling(window=5).mean()
                ma5_value = df_with_ma.iloc[latest_idx]['MA5']
                if not pd.isna(ma5_value):
                    result['ma5'] = float(ma5_value)

            # MA10 - 需要10天数据
            if data_count >= 10:
                df_with_ma['MA10'] = df_with_ma[price_col].rolling(window=10).mean()
                ma10_value = df_with_ma.iloc[latest_idx]['MA10']
                if not pd.isna(ma10_value):
                    result['ma10'] = float(ma10_value)

            # MA20 - 需要20天数据
            if data_count >= 20:
                df_with_ma['MA20'] = df_with_ma[price_col].rolling(window=20).mean()
                ma20_value = df_with_ma.iloc[latest_idx]['MA20']
                if not pd.isna(ma20_value):
                    result['ma20'] = float(ma20_value)

            # MA60 - 需要60天数据
            if data_count >= 60:
                df_with_ma['MA60'] = df_with_ma[price_col].rolling(window=60).mean()
                ma60_value = df_with_ma.iloc[latest_idx]['MA60']
                if not pd.isna(ma60_value):
                    result['ma60'] = float(ma60_value)

            # 2. 灵活趋势分析
            if data_count >= 3:
                result['trend'] = self._flexible_trend_analysis(df, data_count, price_col)

            # 3. 其他技术指标（需要足够数据）
            if data_count >= 10:
                try:
                    # 震荡分析
                    result['oscillation'] = analysis_service.analyze_oscillation(df, price_col)

                    # 波动率和ATR
                    df_with_indicators = analysis_service.calculate_volatility(df, price_col)
                    df_with_indicators = analysis_service.calculate_atr(df_with_indicators)
                    latest = df_with_indicators.iloc[-1]

                    result['volatility'] = float(latest['volatility']) if not pd.isna(latest['volatility']) else None
                    result['atr'] = float(latest['atr']) if not pd.isna(latest['atr']) else None
                except Exception as e:
                    logger.warning(f"计算高级技术指标失败: {e}")

            # 4. 连续涨跌分析（需要至少3天数据）
            if data_count >= 3:
                try:
                    result['consecutive'] = analysis_service.analyze_consecutive_moves(df, price_col)
                except Exception as e:
                    logger.warning(f"连续涨跌分析失败: {e}")

            # 5. 新高新低分析（需要至少5天数据）
            if data_count >= 5:
                try:
                    result['highs_lows'] = analysis_service.analyze_new_highs_lows(df, price_col)
                except Exception as e:
                    logger.warning(f"新高新低分析失败: {e}")

            return result

        except Exception as e:
            logger.error(f"灵活技术分析失败: {e}")
            return result

    def _flexible_trend_analysis(self, df: pd.DataFrame, data_count: int, price_col: str = '收盘') -> Dict[str, Any]:
        """
        根据数据量灵活计算趋势
        """
        try:
            if data_count < 3:
                return {"direction": "unknown", "strength": 0, "reason": "数据不足"}
            elif data_count < 5:
                # 最简单的趋势判断 - 比较首尾价格
                first_price = df[price_col].iloc[0]
                last_price = df[price_col].iloc[-1]
                change_pct = (last_price - first_price) / first_price * 100

                if change_pct > 1:
                    return {"direction": "up", "strength": min(50, abs(change_pct) * 10), "reason": "短期上涨"}
                elif change_pct < -1:
                    return {"direction": "down", "strength": min(50, abs(change_pct) * 10), "reason": "短期下跌"}
                else:
                    return {"direction": "sideways", "strength": 20, "reason": "短期震荡"}
            elif data_count < 10:
                # 使用5日均线的简单趋势判断
                if data_count >= 5:
                    df_ma = df.copy()
                    df_ma['MA5'] = df_ma[price_col].rolling(window=5).mean()
                    current_price = df[price_col].iloc[-1]
                    ma5 = df_ma['MA5'].iloc[-1]

                    if not pd.isna(ma5):
                        if current_price > ma5 * 1.01:
                            return {"direction": "up", "strength": 40, "reason": "价格高于5日均线"}
                        elif current_price < ma5 * 0.99:
                            return {"direction": "down", "strength": 40, "reason": "价格低于5日均线"}
                        else:
                            return {"direction": "sideways", "strength": 30, "reason": "价格接近5日均线"}

                # 回退到简单判断
                return self._flexible_trend_analysis(df, 4, price_col)
            elif data_count < 20:
                # 使用5日和10日均线的中等复杂度趋势判断
                try:
                    return analysis_service._analyze_medium_trend(df, price_col)
                except:
                    return self._flexible_trend_analysis(df, 9, price_col)
            else:
                # 完整的趋势分析
                try:
                    return analysis_service.analyze_trend(df, price_col)
                except:
                    return self._flexible_trend_analysis(df, 19, price_col)

        except Exception as e:
            logger.error(f"灵活趋势分析失败: {e}")
            return {"direction": "unknown", "strength": 0, "reason": f"计算失败: {e}"}

# 全局数据库服务实例
database_service = DatabaseService()
