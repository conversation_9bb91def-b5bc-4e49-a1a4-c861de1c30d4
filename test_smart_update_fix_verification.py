#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能更新修复效果验证脚本

验证修复后的智能更新功能：
1. 用户主动更新时在交易时间内强制获取最新数据
2. 数据更新时间戳正确更新
3. 前端显示的时间与实际处理时间一致
"""

import requests
import json
import time
from datetime import datetime

def test_smart_update_fix():
    """测试智能更新修复效果"""
    print("🔧 测试智能更新修复效果")
    print("=" * 60)
    
    try:
        # 1. 获取修复前的数据状态
        print("1. 获取修复前的数据状态...")
        before_response = requests.get("http://localhost:5000/api/sectors/all-realtime", timeout=30)
        
        if before_response.status_code == 200:
            before_data = before_response.json()
            before_update_times = []
            
            if before_data.get('success') and before_data.get('data'):
                before_update_times = [s.get('数据更新时间') for s in before_data['data'] if s.get('数据更新时间')]
            
            before_latest = max(before_update_times) if before_update_times else "无"
            print(f"修复前最新时间: {before_latest}")
            
            # 2. 执行修复后的智能更新
            print("\n2. 执行修复后的智能更新...")
            update_start_time = datetime.now()
            print(f"更新开始时间: {update_start_time.strftime('%Y-%m-%d %H:%M:%S')}")
            
            start_time = time.time()
            
            # 使用修复后的API调用（包含force_user_update=true）
            update_response = requests.post(
                "http://localhost:5000/api/data/update",
                json={
                    "update_mode": "incremental",
                    "force_user_update": True
                },
                timeout=350
            )
            
            end_time = time.time()
            elapsed_time = end_time - start_time
            update_end_time = datetime.now()
            
            print(f"更新结束时间: {update_end_time.strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"更新耗时: {elapsed_time:.2f} 秒")
            
            if update_response.status_code == 200:
                update_data = update_response.json()
                print(f"更新结果: {update_data.get('success', False)}")
                print(f"更新消息: {update_data.get('message', '无消息')}")
                
                # 3. 获取修复后的数据状态
                print("\n3. 获取修复后的数据状态...")
                after_response = requests.get("http://localhost:5000/api/sectors/all-realtime", timeout=30)
                
                if after_response.status_code == 200:
                    after_data = after_response.json()
                    after_update_times = []
                    
                    if after_data.get('success') and after_data.get('data'):
                        after_update_times = [s.get('数据更新时间') for s in after_data['data'] if s.get('数据更新时间')]
                    
                    after_latest = max(after_update_times) if after_update_times else "无"
                    print(f"修复后最新时间: {after_latest}")
                    
                    # 4. 验证修复效果
                    print(f"\n🎯 修复效果验证:")
                    print(f"- 修复前时间: {before_latest}")
                    print(f"- 修复后时间: {after_latest}")
                    print(f"- 更新开始时间: {update_start_time.strftime('%Y-%m-%d %H:%M:%S')}")
                    print(f"- 更新结束时间: {update_end_time.strftime('%Y-%m-%d %H:%M:%S')}")
                    
                    # 检查时间是否更新
                    time_changed = before_latest != after_latest
                    print(f"- 数据时间是否更新: {'✅ 是' if time_changed else '❌ 否'}")
                    
                    if time_changed:
                        # 解析时间并检查是否在合理范围内
                        try:
                            after_time = datetime.strptime(after_latest, '%Y-%m-%d %H:%M:%S')
                            time_diff = abs((update_end_time - after_time).total_seconds())
                            
                            print(f"- 时间戳准确性: {time_diff:.1f} 秒差异")
                            
                            if time_diff < 300:  # 5分钟内认为准确
                                print("✅ 时间戳准确，在合理范围内")
                            else:
                                print("⚠️  时间戳可能不够准确")
                                
                        except ValueError:
                            print("⚠️  时间格式解析失败")
                    
                    # 检查是否强制获取了最新数据
                    if time_changed:
                        print("✅ 修复成功：用户主动更新时强制获取了最新数据")
                        print("✅ 修复成功：数据更新时间戳正确更新")
                    else:
                        print("❌ 修复失败：数据时间戳仍未更新")
                    
                    return {
                        'success': time_changed,
                        'before_time': before_latest,
                        'after_time': after_latest,
                        'elapsed_time': elapsed_time,
                        'update_start': update_start_time.strftime('%Y-%m-%d %H:%M:%S'),
                        'update_end': update_end_time.strftime('%Y-%m-%d %H:%M:%S')
                    }
                else:
                    print(f"❌ 获取修复后数据失败: {after_response.status_code}")
                    return None
            else:
                print(f"❌ 智能更新失败: {update_response.status_code}")
                return None
        else:
            print(f"❌ 获取修复前数据失败: {before_response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return None

def test_trading_time_logic():
    """测试交易时间判断逻辑"""
    print(f"\n🕐 测试交易时间判断逻辑")
    print("=" * 60)
    
    current_time = datetime.now()
    current_time_only = current_time.time()
    
    # 模拟交易时间判断
    from datetime import time as dt_time
    
    morning_start = dt_time(9, 30)
    morning_end = dt_time(11, 30)
    afternoon_start = dt_time(13, 0)
    afternoon_end = dt_time(15, 0)
    
    is_trading_time = (morning_start <= current_time_only <= morning_end) or \
                     (afternoon_start <= current_time_only <= afternoon_end)
    
    print(f"当前时间: {current_time.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"是否交易时间: {is_trading_time}")
    
    if is_trading_time:
        print("✅ 当前为交易时间，修复后应该强制获取最新数据")
    else:
        print("ℹ️  当前为非交易时间，可以使用缓存数据")
    
    return is_trading_time

def verify_fix_summary(test_result, is_trading_time):
    """总结修复验证结果"""
    print(f"\n📊 修复验证总结")
    print("=" * 60)
    
    if test_result and test_result['success']:
        print("🎉 修复验证成功！")
        print(f"✅ 问题1已修复: 交易时间内用户主动更新强制获取最新数据")
        print(f"✅ 问题2已修复: 数据更新时间戳正确更新")
        print(f"✅ 问题3已修复: 前端显示时间与实际处理时间一致")
        
        print(f"\n📈 修复效果:")
        print(f"- 更新前时间: {test_result['before_time']}")
        print(f"- 更新后时间: {test_result['after_time']}")
        print(f"- 处理耗时: {test_result['elapsed_time']:.2f} 秒")
        print(f"- 交易时间状态: {'是' if is_trading_time else '否'}")
        
    elif test_result and not test_result['success']:
        print("❌ 修复验证失败")
        print("数据时间戳仍未更新，可能需要进一步检查：")
        print("1. 后端服务是否重启以加载修复代码")
        print("2. 数据服务的强制刷新逻辑是否正确执行")
        print("3. 缓存更新机制是否正常工作")
        
    else:
        print("⚠️  修复验证未完成")
        print("可能的原因：")
        print("1. 后端服务未启动或连接失败")
        print("2. API调用超时或异常")
        print("3. 数据格式或逻辑错误")
    
    print(f"\n💡 修复要点回顾:")
    print("1. 添加force_user_update参数区分用户主动更新")
    print("2. 交易时间内用户主动更新时强制获取最新API数据")
    print("3. 强制刷新时更新所有板块的数据更新时间戳")
    print("4. 缩短交易时间内新鲜度阈值（30分钟→15分钟）")

def main():
    """主测试函数"""
    print("🔧 智能更新功能修复验证")
    print("=" * 60)
    print("验证时间:", datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
    
    # 1. 测试交易时间判断
    is_trading_time = test_trading_time_logic()
    
    # 2. 测试智能更新修复效果
    test_result = test_smart_update_fix()
    
    # 3. 总结验证结果
    verify_fix_summary(test_result, is_trading_time)

if __name__ == "__main__":
    main()
