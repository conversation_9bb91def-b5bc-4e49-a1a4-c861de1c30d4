#!/usr/bin/env python3
"""
调试交易时间判断和强制刷新逻辑
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from datetime import datetime
from utils.trading_calendar import trading_calendar

def test_trading_time():
    """测试交易时间判断"""
    current_time = datetime.now()
    is_trading = trading_calendar.is_trading_time()
    
    print(f"当前时间: {current_time.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"是否交易时间: {is_trading}")
    print(f"星期几: {current_time.strftime('%A')}")
    
    # 检查具体的交易时间段
    current_time_only = current_time.time()
    print(f"当前时间（仅时分）: {current_time_only}")
    
    # 模拟智能更新的逻辑
    force_user_update = True
    should_force_refresh = is_trading and force_user_update
    print(f"用户主动更新: {force_user_update}")
    print(f"应该强制刷新: {should_force_refresh}")
    
    return should_force_refresh

def main():
    print("🕐 交易时间和强制刷新逻辑调试")
    print("=" * 50)
    
    should_force = test_trading_time()
    
    if should_force:
        print("✅ 应该触发强制刷新")
    else:
        print("❌ 不会触发强制刷新")
        print("💡 这可能是时间戳不更新的原因")

if __name__ == "__main__":
    main()
