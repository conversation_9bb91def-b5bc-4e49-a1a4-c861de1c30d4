#!/usr/bin/env python3
"""
板块日历功能修复脚本
解决数据收集和定时任务问题
"""

import sys
import os
import requests
import json
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def trigger_data_collection(days_back=7, top_n=10):
    """触发历史数据收集"""
    print(f"🚀 开始收集最近{days_back}天的板块排名数据...")
    
    base_url = "http://localhost:5000"
    success_count = 0
    
    for i in range(days_back):
        target_date = (datetime.now() - timedelta(days=i)).strftime('%Y-%m-%d')
        
        try:
            print(f"📅 收集 {target_date} 的数据...")
            
            response = requests.post(
                f"{base_url}/api/sector-calendar/collect",
                json={
                    "target_date": target_date,
                    "top_n": top_n
                },
                timeout=60
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    result = data.get('data', {})
                    collected = result.get('collected_count', 0)
                    print(f"   ✅ 成功收集 {collected} 条数据")
                    success_count += 1
                else:
                    print(f"   ❌ 收集失败: {data.get('error', '未知错误')}")
            else:
                print(f"   ❌ HTTP错误: {response.status_code}")
                
        except requests.exceptions.Timeout:
            print(f"   ⏰ 请求超时，跳过 {target_date}")
        except Exception as e:
            print(f"   ❌ 异常: {e}")
    
    print(f"\n📊 数据收集完成: {success_count}/{days_back} 天成功")
    return success_count

def verify_data_availability():
    """验证数据可用性"""
    print("\n🔍 验证数据可用性...")
    
    try:
        # 检查排名数据
        response = requests.get(
            "http://localhost:5000/api/sector-calendar/rankings?limit=5",
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                count = len(data.get('data', []))
                print(f"✅ 排名数据可用: {count} 条记录")
                
                if count > 0:
                    latest_date = data['data'][0].get('ranking_date')
                    print(f"📅 最新数据日期: {latest_date}")
            else:
                print("❌ 排名数据获取失败")
        
        # 检查活跃板块数据
        response = requests.get(
            "http://localhost:5000/api/sector-calendar/active-sectors",
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                count = len(data.get('data', []))
                print(f"✅ 活跃板块数据可用: {count} 个板块")
            else:
                print("❌ 活跃板块数据获取失败")
                
        return True
        
    except Exception as e:
        print(f"❌ 数据验证失败: {e}")
        return False

def check_frontend_access():
    """检查前端访问"""
    print("\n🌐 检查前端访问...")
    
    try:
        # 检查前端是否可访问
        response = requests.get("http://localhost:3000", timeout=5)
        if response.status_code == 200:
            print("✅ 前端服务正常运行")
            print("📱 板块日历访问地址: http://localhost:3000/sector-calendar")
        else:
            print(f"⚠️  前端服务状态异常: {response.status_code}")
    except Exception as e:
        print(f"⚠️  前端服务检查失败: {e}")
        print("💡 请确保前端服务已启动: npm run dev")

def provide_usage_instructions():
    """提供使用说明"""
    print("\n📖 使用说明...")
    print("=" * 50)
    
    instructions = [
        "1. 访问板块日历功能:",
        "   - 打开浏览器访问: http://localhost:3000",
        "   - 点击顶部导航栏的'板块日历'菜单项",
        "",
        "2. 如果仍然看不到数据:",
        "   - 刷新浏览器页面",
        "   - 清除浏览器缓存",
        "   - 检查浏览器控制台是否有错误",
        "",
        "3. 手动触发数据收集:",
        "   - 在板块日历页面点击'刷新数据'按钮",
        "   - 或使用API: POST /api/sector-calendar/collect",
        "",
        "4. 定时任务设置:",
        "   - 确保后端服务持续运行",
        "   - 定时任务会自动收集数据",
        "",
        "5. 故障排除:",
        "   - 检查后端服务日志",
        "   - 确认网络连接正常",
        "   - 验证AKShare数据源可访问"
    ]
    
    for instruction in instructions:
        print(instruction)

def main():
    """主修复流程"""
    print("🔧 板块日历功能修复工具")
    print("=" * 50)
    print(f"修复时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 步骤1: 触发数据收集
    success_count = trigger_data_collection(days_back=3, top_n=10)
    
    # 步骤2: 验证数据可用性
    data_available = verify_data_availability()
    
    # 步骤3: 检查前端访问
    check_frontend_access()
    
    # 步骤4: 提供使用说明
    provide_usage_instructions()
    
    # 总结
    print("\n🎯 修复结果总结")
    print("=" * 50)
    
    if success_count > 0 and data_available:
        print("✅ 板块日历功能修复成功！")
        print("📅 数据已收集并可通过API访问")
        print("🌐 前端应该能够正常显示数据")
        print("\n💡 如果前端仍然显示空白，请:")
        print("   1. 刷新浏览器页面")
        print("   2. 清除浏览器缓存")
        print("   3. 在板块日历页面点击'刷新数据'按钮")
    else:
        print("⚠️  修复过程中遇到问题")
        print("📞 请检查:")
        print("   1. 后端服务是否正常运行")
        print("   2. 网络连接是否正常")
        print("   3. AKShare数据源是否可访问")

if __name__ == '__main__':
    main()
