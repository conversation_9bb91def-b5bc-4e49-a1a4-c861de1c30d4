import React, { useState, useMemo } from 'react'
import {
  Drawer,
  Table,
  Typography,
  Space,
  Tag,
  Button,
  Input,
  Select,
  Row,
  Col,
  Tooltip,
  Statistic,
  Card,
  Divider,
  App
} from 'antd'
import {
  CloseOutlined,
  SearchOutlined,
  FilterOutlined,
  DownloadOutlined,
  TrophyOutlined,
  RiseOutlined,
  FallOutlined,
  MinusOutlined,
  InfoCircleOutlined,
  CalendarOutlined
} from '@ant-design/icons'
import type { ColumnsType, TableProps } from 'antd/es/table'
import type { SorterResult } from 'antd/es/table/interface'
import { useTheme } from '../../theme/ThemeProvider'
import type { SectorRanking } from '../../types/SectorCalendar'

const { Title, Text } = Typography
const { Search } = Input
const { Option } = Select

interface RankingPanelProps {
  visible: boolean
  onClose: () => void
  date: string
  rankings: SectorRanking[]
  title?: string
}

const RankingPanel: React.FC<RankingPanelProps> = ({
  visible,
  onClose,
  date,
  rankings,
  title
}) => {
  // 状态管理
  const [searchText, setSearchText] = useState('')
  const [trendFilter, setTrendFilter] = useState<string>('all')
  const [rankingFilter, setRankingFilter] = useState<string>('all')
  const [sortedInfo, setSortedInfo] = useState<SorterResult<SectorRanking>>({})

  // 主题和消息
  const { colorScheme } = useTheme()
  const { message } = App.useApp()

  // 数据筛选和排序
  const filteredData = useMemo(() => {
    let filtered = rankings.filter(item => {
      // 搜索筛选
      const matchSearch = !searchText || 
        item.sector_name.toLowerCase().includes(searchText.toLowerCase()) ||
        item.sector_code.toLowerCase().includes(searchText.toLowerCase())

      // 趋势筛选
      const matchTrend = trendFilter === 'all' || 
        (trendFilter === 'up' && (item.price_change_pct || 0) > 0) ||
        (trendFilter === 'down' && (item.price_change_pct || 0) < 0) ||
        (trendFilter === 'flat' && (item.price_change_pct || 0) === 0)

      // 排名筛选
      const matchRanking = rankingFilter === 'all' ||
        (rankingFilter === 'top3' && item.ranking <= 3) ||
        (rankingFilter === 'top5' && item.ranking <= 5) ||
        (rankingFilter === 'top10' && item.ranking <= 10)

      return matchSearch && matchTrend && matchRanking
    })

    return filtered
  }, [rankings, searchText, trendFilter, rankingFilter])

  // 获取价格变化颜色
  const getPriceChangeColor = (change?: number) => {
    if (!change) return colorScheme.onSurface
    return change > 0 ? '#ff4d4f' : change < 0 ? '#52c41a' : colorScheme.onSurface
  }

  // 获取价格变化图标
  const getPriceChangeIcon = (change?: number) => {
    if (!change) return <MinusOutlined />
    return change > 0 ? <RiseOutlined /> : <FallOutlined />
  }

  // 渲染排名标签
  const renderRankingTag = (ranking: number) => {
    let color = 'blue'
    if (ranking === 1) color = 'gold'
    else if (ranking <= 3) color = 'orange'
    else if (ranking <= 5) color = 'green'

    return (
      <Tag color={color} style={{ fontWeight: 'bold' }}>
        #{ranking}
      </Tag>
    )
  }

  // 渲染价格变化
  const renderPriceChange = (change?: number) => {
    if (change === undefined || change === null) return '-'
    
    return (
      <Space>
        {getPriceChangeIcon(change)}
        <Text style={{ color: getPriceChangeColor(change), fontWeight: 'bold' }}>
          {change > 0 ? '+' : ''}{change.toFixed(2)}%
        </Text>
      </Space>
    )
  }

  // 渲染连续上涨天数
  const renderConsecutiveDays = (days?: number) => {
    if (!days || days <= 0) return '-'
    
    return (
      <Tag color="red" size="small">
        连涨{days}天
      </Tag>
    )
  }

  // 渲染新高标识
  const renderNewHighBadges = (record: SectorRanking) => {
    const badges = []

    if (record.is_new_high_5d) {
      badges.push(
        <Tag key="5d" color="volcano" size="small">
          5日新高
        </Tag>
      )
    }

    if (record.is_new_high_20d) {
      badges.push(
        <Tag key="20d" color="red" size="small">
          20日新高
        </Tag>
      )
    }

    return badges.length > 0 ? <Space size={4}>{badges}</Space> : '-'
  }

  // 渲染领涨股票信息
  const renderLeadingStock = (record: SectorRanking) => {
    const { leading_stock_name, leading_stock_code, leading_stock_price, leading_stock_change_pct } = record

    if (!leading_stock_name) {
      return (
        <Text type="secondary" style={{ fontSize: '12px' }}>
          暂无数据
        </Text>
      )
    }

    return (
      <div>
        <div>
          <Text strong style={{ fontSize: '13px', color: colorScheme.onSurface }}>
            {leading_stock_name}
          </Text>
          {leading_stock_code && (
            <Text type="secondary" style={{ fontSize: '11px', marginLeft: '4px' }}>
              ({leading_stock_code})
            </Text>
          )}
        </div>
        <div style={{ marginTop: '2px' }}>
          {leading_stock_price && (
            <Text style={{ fontSize: '11px', color: colorScheme.onSurface }}>
              ¥{leading_stock_price.toFixed(2)}
            </Text>
          )}
          {leading_stock_change_pct !== undefined && leading_stock_change_pct !== null && (
            <Text
              style={{
                fontSize: '11px',
                color: getPriceChangeColor(leading_stock_change_pct),
                marginLeft: leading_stock_price ? '6px' : '0px',
                fontWeight: 'bold'
              }}
            >
              {leading_stock_change_pct > 0 ? '+' : ''}{leading_stock_change_pct.toFixed(2)}%
            </Text>
          )}
        </div>
      </div>
    )
  }

  // 表格列定义
  const columns: ColumnsType<SectorRanking> = [
    {
      title: '排名',
      dataIndex: 'ranking',
      key: 'ranking',
      width: 80,
      sorter: (a, b) => a.ranking - b.ranking,
      sortOrder: sortedInfo.columnKey === 'ranking' ? sortedInfo.order : null,
      render: renderRankingTag,
      fixed: 'left'
    },
    {
      title: '板块名称',
      dataIndex: 'sector_name',
      key: 'sector_name',
      width: 150,
      sorter: (a, b) => a.sector_name.localeCompare(b.sector_name),
      sortOrder: sortedInfo.columnKey === 'sector_name' ? sortedInfo.order : null,
      render: (name: string, record: SectorRanking) => (
        <div>
          <Text strong style={{ color: colorScheme.onSurface }}>
            {name}
          </Text>
          <br />
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {record.sector_code}
          </Text>
        </div>
      ),
      fixed: 'left'
    },
    {
      title: '涨跌幅',
      dataIndex: 'price_change_pct',
      key: 'price_change_pct',
      width: 120,
      sorter: (a, b) => (a.price_change_pct || 0) - (b.price_change_pct || 0),
      sortOrder: sortedInfo.columnKey === 'price_change_pct' ? sortedInfo.order : null,
      render: renderPriceChange
    },
    {
      title: '收盘价',
      dataIndex: 'close_price',
      key: 'close_price',
      width: 100,
      sorter: (a, b) => (a.close_price || 0) - (b.close_price || 0),
      sortOrder: sortedInfo.columnKey === 'close_price' ? sortedInfo.order : null,
      render: (price?: number) => price ? `¥${price.toFixed(2)}` : '-'
    },
    {
      title: '涨跌额',
      dataIndex: 'price_change',
      key: 'price_change',
      width: 100,
      sorter: (a, b) => (a.price_change || 0) - (b.price_change || 0),
      sortOrder: sortedInfo.columnKey === 'price_change' ? sortedInfo.order : null,
      render: (change?: number) => {
        if (!change) return '-'
        return (
          <Text style={{ color: getPriceChangeColor(change) }}>
            {change > 0 ? '+' : ''}¥{change.toFixed(2)}
          </Text>
        )
      }
    },
    {
      title: '成交量',
      dataIndex: 'volume',
      key: 'volume',
      width: 120,
      sorter: (a, b) => (a.volume || 0) - (b.volume || 0),
      sortOrder: sortedInfo.columnKey === 'volume' ? sortedInfo.order : null,
      render: (volume?: number) => {
        if (!volume) return '-'
        if (volume >= 100000000) {
          return `${(volume / 100000000).toFixed(2)}亿`
        } else if (volume >= 10000) {
          return `${(volume / 10000).toFixed(0)}万`
        }
        return volume.toString()
      }
    },
    {
      title: '成交额',
      dataIndex: 'turnover',
      key: 'turnover',
      width: 120,
      sorter: (a, b) => (a.turnover || 0) - (b.turnover || 0),
      sortOrder: sortedInfo.columnKey === 'turnover' ? sortedInfo.order : null,
      render: (turnover?: number) => {
        if (!turnover) return '-'
        if (turnover >= 100000000) {
          return `¥${(turnover / 100000000).toFixed(2)}亿`
        } else if (turnover >= 10000) {
          return `¥${(turnover / 10000).toFixed(0)}万`
        }
        return `¥${turnover.toFixed(2)}`
      }
    },
    {
      title: '连续上涨',
      dataIndex: 'consecutive_up_days',
      key: 'consecutive_up_days',
      width: 100,
      sorter: (a, b) => (a.consecutive_up_days || 0) - (b.consecutive_up_days || 0),
      sortOrder: sortedInfo.columnKey === 'consecutive_up_days' ? sortedInfo.order : null,
      render: renderConsecutiveDays
    },
    {
      title: '新高标识',
      key: 'new_high',
      width: 120,
      render: (_, record) => renderNewHighBadges(record)
    },
    {
      title: '领涨股票',
      key: 'leading_stock',
      width: 180,
      render: (_, record) => renderLeadingStock(record)
    }
  ]

  // 表格变化处理
  const handleTableChange: TableProps<SectorRanking>['onChange'] = (pagination, filters, sorter) => {
    setSortedInfo(sorter as SorterResult<SectorRanking>)
  }

  // 导出数据
  const handleExport = () => {
    try {
      const csvContent = [
        // CSV头部
        ['排名', '板块代码', '板块名称', '涨跌幅(%)', '收盘价', '涨跌额', '成交量', '成交额', '连续上涨天数', '5日新高', '20日新高', '领涨股票', '领涨股票代码', '领涨股票价格', '领涨股票涨跌幅(%)'].join(','),
        // 数据行
        ...filteredData.map(item => [
          item.ranking,
          item.sector_code,
          `"${item.sector_name}"`,
          item.price_change_pct?.toFixed(2) || '',
          item.close_price?.toFixed(2) || '',
          item.price_change?.toFixed(2) || '',
          item.volume || '',
          item.turnover?.toFixed(2) || '',
          item.consecutive_up_days || '',
          item.is_new_high_5d ? '是' : '否',
          item.is_new_high_20d ? '是' : '否',
          `"${item.leading_stock_name || ''}"`,
          item.leading_stock_code || '',
          item.leading_stock_price?.toFixed(2) || '',
          item.leading_stock_change_pct?.toFixed(2) || ''
        ].join(','))
      ].join('\n')

      const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' })
      const link = document.createElement('a')
      const url = URL.createObjectURL(blob)
      link.setAttribute('href', url)
      link.setAttribute('download', `板块排名_${date}.csv`)
      link.style.visibility = 'hidden'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      
      message.success('数据导出成功')
    } catch (error) {
      console.error('导出失败:', error)
      message.error('数据导出失败')
    }
  }

  // 重置筛选
  const handleReset = () => {
    setSearchText('')
    setTrendFilter('all')
    setRankingFilter('all')
    setSortedInfo({})
  }

  // 统计数据
  const stats = useMemo(() => {
    const upCount = rankings.filter(item => (item.price_change_pct || 0) > 0).length
    const downCount = rankings.filter(item => (item.price_change_pct || 0) < 0).length
    const flatCount = rankings.filter(item => (item.price_change_pct || 0) === 0).length
    const newHighCount = rankings.filter(item => item.is_new_high_5d || item.is_new_high_20d).length

    return { upCount, downCount, flatCount, newHighCount }
  }, [rankings])

  return (
    <Drawer
      title={
        <Space>
          <CalendarOutlined style={{ color: colorScheme.primary }} />
          <span style={{ color: colorScheme.onSurface }}>
            {title || `${date} 板块排名详情`}
          </span>
          <Tag color="blue">{rankings.length}个板块</Tag>
        </Space>
      }
      placement="right"
      width="90%"
      open={visible}
      onClose={onClose}
      closeIcon={<CloseOutlined style={{ color: colorScheme.onSurface }} />}
      style={{
        background: colorScheme.surface
      }}
      headerStyle={{
        background: colorScheme.surfaceVariant,
        borderBottomColor: colorScheme.outline
      }}
      bodyStyle={{
        background: colorScheme.surface,
        padding: '16px'
      }}
      extra={
        <Space>
          <Button
            icon={<DownloadOutlined />}
            onClick={handleExport}
            style={{
              borderColor: colorScheme.outline,
              color: colorScheme.primary
            }}
          >
            导出数据
          </Button>
        </Space>
      }
    >
      {/* 统计信息 */}
      <Card
        size="small"
        style={{
          background: colorScheme.surfaceVariant,
          borderColor: colorScheme.outline,
          marginBottom: '16px'
        }}
      >
        <Row gutter={16}>
          <Col span={6}>
            <Statistic
              title="上涨板块"
              value={stats.upCount}
              suffix={`/ ${rankings.length}`}
              valueStyle={{ color: '#ff4d4f', fontSize: '16px' }}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="下跌板块"
              value={stats.downCount}
              suffix={`/ ${rankings.length}`}
              valueStyle={{ color: '#52c41a', fontSize: '16px' }}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="平盘板块"
              value={stats.flatCount}
              suffix={`/ ${rankings.length}`}
              valueStyle={{ color: colorScheme.onSurface, fontSize: '16px' }}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="创新高板块"
              value={stats.newHighCount}
              suffix={`/ ${rankings.length}`}
              valueStyle={{ color: colorScheme.primary, fontSize: '16px' }}
            />
          </Col>
        </Row>
      </Card>

      {/* 筛选控件 */}
      <Card
        size="small"
        title={
          <Space>
            <FilterOutlined />
            <span>数据筛选</span>
          </Space>
        }
        style={{
          background: colorScheme.surface,
          borderColor: colorScheme.outline,
          marginBottom: '16px'
        }}
        extra={
          <Button size="small" onClick={handleReset}>
            重置
          </Button>
        }
      >
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={12} md={8}>
            <Search
              placeholder="搜索板块名称或代码"
              allowClear
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              prefix={<SearchOutlined />}
            />
          </Col>
          <Col xs={12} sm={6} md={4}>
            <Select
              style={{ width: '100%' }}
              placeholder="趋势筛选"
              value={trendFilter}
              onChange={setTrendFilter}
            >
              <Option value="all">全部趋势</Option>
              <Option value="up">上涨</Option>
              <Option value="down">下跌</Option>
              <Option value="flat">平盘</Option>
            </Select>
          </Col>
          <Col xs={12} sm={6} md={4}>
            <Select
              style={{ width: '100%' }}
              placeholder="排名筛选"
              value={rankingFilter}
              onChange={setRankingFilter}
            >
              <Option value="all">全部排名</Option>
              <Option value="top3">前3名</Option>
              <Option value="top5">前5名</Option>
              <Option value="top10">前10名</Option>
            </Select>
          </Col>
        </Row>
      </Card>

      {/* 数据表格 */}
      <Card
        title={
          <Space>
            <TrophyOutlined />
            <span>排名详情</span>
            <Text type="secondary">
              (显示 {filteredData.length} / {rankings.length} 条数据)
            </Text>
          </Space>
        }
        style={{
          background: colorScheme.surface,
          borderColor: colorScheme.outline
        }}
      >
        <Table
          columns={columns}
          dataSource={filteredData}
          rowKey="id"
          size="small"
          scroll={{ x: 1200, y: 400 }}
          pagination={{
            pageSize: 20,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
            pageSizeOptions: ['10', '20', '50', '100']
          }}
          onChange={handleTableChange}
          style={{
            background: colorScheme.surface
          }}
          rowClassName={(record, index) => {
            // 为连续活跃板块添加高亮
            if (record.consecutive_up_days && record.consecutive_up_days >= 3) {
              return 'active-sector-row'
            }
            return ''
          }}
        />
      </Card>

      {/* 自定义样式 */}
      <style jsx>{`
        .active-sector-row {
          background: ${colorScheme.primaryContainer}40 !important;
        }
        
        .active-sector-row:hover {
          background: ${colorScheme.primaryContainer}60 !important;
        }
        
        .ant-table-thead > tr > th {
          background: ${colorScheme.surfaceVariant} !important;
          color: ${colorScheme.onSurface} !important;
          border-bottom: 1px solid ${colorScheme.outline} !important;
        }
        
        .ant-table-tbody > tr > td {
          border-bottom: 1px solid ${colorScheme.outlineVariant} !important;
        }
        
        .ant-table-tbody > tr:hover > td {
          background: ${colorScheme.surfaceVariant}40 !important;
        }
      `}</style>
    </Drawer>
  )
}

export default RankingPanel
