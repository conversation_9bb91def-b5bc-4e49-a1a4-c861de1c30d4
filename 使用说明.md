# 股票分析系统使用说明

## 系统概述

这是一个基于申万行业分类的股票市场复盘分析系统，提供31个申万一级行业板块的实时数据分析和可视化展示。

## 功能特性

### 1. 数据展示
- **31个申万一级行业板块**：包括农林牧渔、采掘、化工、钢铁等完整的行业分类
- **实时行情数据**：显示最新价格、涨跌幅、涨跌额等关键指标
- **技术分析结果**：趋势类型、连涨天数、是否创新高、波动水平等分析指标

### 2. 可视化图表
- **价格分布图**：柱状图展示各板块价格水平
- **涨跌幅分布图**：柱状图展示各板块涨跌幅情况
- **趋势类型分布**：饼图展示不同趋势类型的板块分布
- **连涨天数趋势**：折线图展示各板块连续上涨天数

### 3. 数据筛选和搜索
- **搜索功能**：支持按板块名称或代码搜索
- **趋势筛选**：可按上涨、下跌、震荡、盘整等趋势类型筛选
- **创新高筛选**：可筛选创新高或未创新高的板块

### 4. 实时更新
- **手动刷新**：点击刷新按钮获取最新数据
- **自动刷新**：开启自动刷新功能，每30秒自动更新数据
- **更新时间显示**：显示最后更新时间和自动刷新状态

### 5. 统计信息
- **总板块数**：显示系统监控的板块总数
- **上涨/下跌板块数**：统计当前上涨和下跌的板块数量
- **创新高板块数**：统计创新高的板块数量
- **平均涨跌幅**：计算所有板块的平均涨跌幅
- **最大连涨天数**：显示连续上涨天数最多的记录

### 6. 板块日历 📅
- **日历视图**：以日历形式展示每日活跃板块动向
- **数据可视化**：每个日期显示活跃板块数量和排名第一板块信息
- **历史数据查询**：支持查看最近30天的板块排名历史
- **详情面板**：点击日期可查看当日完整的板块排名详情
- **连续活跃分析**：统计和展示连续活跃的板块
- **高级筛选**：支持按板块名称、趋势、排名等条件筛选
- **数据导出**：支持将排名数据导出为CSV格式
- **智能缓存**：本地缓存机制，提升加载速度和用户体验

## 使用方法

### 启动系统

#### 方法一：使用统一启动脚本（推荐）
```bash
# 首次使用或环境有问题时
start_unified.bat

# 后续快速启动
start_local.bat
```

#### 方法二：手动启动
1. **激活虚拟环境**：
   ```bash
   venv\Scripts\activate.bat
   ```

2. **启动后端服务**：
   ```bash
   cd backend
   python app.py
   ```
   后端服务将在 http://127.0.0.1:5000 启动

3. **启动前端服务**：
   ```bash
   cd frontend
   npm run dev
   ```
   前端服务将在 http://localhost:3000 启动

4. **访问系统**：
   在浏览器中打开 http://localhost:3000

### 界面操作

#### 仪表板页面
- **统计卡片区域**：查看关键统计指标
- **操作控制区域**：
  - 点击"刷新数据"手动更新
  - 点击"更新数据"触发数据更新
  - 开启"自动刷新"实现定时更新
- **筛选控制区域**：
  - 使用搜索框查找特定板块
  - 使用趋势筛选下拉框过滤数据
  - 使用创新高筛选下拉框过滤数据

#### 图表分析标签页
- **价格分布图**：查看各板块价格水平对比
- **涨跌幅分布图**：查看各板块涨跌幅对比
- **趋势类型分布图**：查看不同趋势类型的板块占比
- **连涨天数趋势图**：查看各板块连续上涨天数趋势

#### 数据表格标签页
- **完整数据表格**：查看所有板块的详细数据
- **分页功能**：支持分页浏览和每页显示数量调整
- **排序功能**：点击表头可对数据进行排序

## 技术架构

### 前端技术栈
- **React 18 + TypeScript**：现代化前端框架
- **Ant Design**：企业级UI组件库
- **ECharts**：专业的数据可视化图表库
- **Axios**：HTTP客户端库
- **Vite**：快速的前端构建工具

### 后端技术栈
- **Flask**：轻量级Python Web框架
- **Flask-CORS**：跨域资源共享支持
- **模拟数据服务**：提供31个申万行业板块的模拟数据

### 数据结构
```json
{
  "id": 1,
  "code": "801010",
  "name": "农林牧渔",
  "latest_quote": {
    "date": "2025-06-29",
    "close_price": 2345.67,
    "price_change": 12.34,
    "price_change_pct": 0.0053
  },
  "latest_analysis": {
    "trend_type": "上涨",
    "consecutive_up_days": 3,
    "is_new_high": false,
    "volatility_level": "中"
  }
}
```

## 注意事项

1. **数据说明**：当前使用模拟数据，实际部署时需要集成真实的AKShare数据源
2. **浏览器兼容性**：建议使用Chrome、Firefox、Edge等现代浏览器
3. **网络要求**：前后端需要在同一网络环境下运行
4. **性能优化**：大量数据时建议关闭自动刷新功能

## 故障排除

### 常见问题

1. **前端无法访问后端API**
   - 检查后端服务是否正常启动
   - 确认端口5000没有被其他程序占用
   - 检查防火墙设置

2. **图表显示异常**
   - 刷新页面重新加载
   - 检查浏览器控制台是否有错误信息
   - 确认ECharts库正常加载

3. **数据更新失败**
   - 检查网络连接
   - 查看后端服务日志
   - 尝试手动刷新页面

### 开发调试

- **前端开发服务器**：http://localhost:3000
- **后端API服务器**：http://127.0.0.1:5000
- **API健康检查**：http://127.0.0.1:5000/health
- **API测试接口**：http://127.0.0.1:5000/api/test

## 板块日历使用指南 📅

### 访问板块日历
1. 在顶部导航栏点击"板块日历"菜单项
2. 系统将在新标签页中打开板块日历功能
3. 页面标题显示为"板块日历 - 股票分析系统"

### 日历视图操作
1. **查看日历数据**
   - 日历网格显示最近30天的数据
   - 有数据的日期会显示活跃板块数量徽章
   - 排名第一的板块会以标签形式显示

2. **选择日期**
   - 点击任意有数据的日期
   - 系统会自动展示该日期的板块排名详情
   - 选中日期会有特殊的高亮显示

3. **查看统计信息**
   - 右侧显示数据统计：有数据天数、活跃板块数
   - 连续活跃板块列表显示近7天出现≥2次的板块
   - 包含平均排名、最佳排名、出现次数等信息

### 详情面板操作
1. **打开详情面板**
   - 点击有数据的日期自动打开
   - 或点击日期详情卡片中的"查看详情"按钮

2. **数据筛选**
   - **搜索**：在搜索框中输入板块名称或代码
   - **趋势筛选**：选择上涨/下跌/平盘板块
   - **排名筛选**：选择前3/5/10名板块
   - **重置**：点击重置按钮清除所有筛选条件

3. **数据排序**
   - 点击表格列头进行排序
   - 支持排名、涨跌幅、收盘价、成交量等所有列排序
   - 再次点击可切换升序/降序

4. **数据导出**
   - 点击"导出数据"按钮
   - 系统将当前筛选的数据导出为CSV格式
   - 文件名格式：板块排名_日期.csv

### 性能优化功能
1. **智能缓存**
   - 系统自动缓存已加载的数据
   - 重复访问时从缓存快速加载
   - 缓存命中率显示在页面右上角

2. **手动刷新**
   - 点击"刷新数据"按钮获取最新数据
   - 刷新时会清理缓存确保数据最新
   - 显示最后更新时间

3. **骨架屏加载**
   - 首次加载时显示骨架屏而非转圈
   - 提升用户感知性能
   - 与真实界面布局完全一致

### 数据说明
- **排名数据**：包含排名、板块名称、涨跌幅、收盘价、成交量等9列详细信息
- **活跃板块**：基于近期排名表现统计的连续活跃板块
- **数据更新**：数据通过定时任务自动收集，支持手动触发更新
- **缓存机制**：本地缓存30分钟，提升访问速度

## 扩展功能

系统支持以下扩展：
1. 集成真实AKShare数据源
2. 添加更多技术指标分析
3. 支持历史数据查询
4. 添加预警和通知功能
5. 支持用户自定义看板
