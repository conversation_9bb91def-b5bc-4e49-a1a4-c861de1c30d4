### **股票分析系统：持久化存储改造方案 (基于现有模型)**

#### **引言**

本方案旨在响应用户核心诉d求——**在现有数据模型和代码架构的基础上，以最小化变更实现关键业务数据的长期持久化**。方案将放弃引入新表，转而对现有模型进行增强和职责调整，确保系统的稳定性和向后兼容性，同时实现从“数据库缓存”到“真正持久化”的战略升级。

---

#### **1. 核心思想：从“缓存”到“持久化”的就地升级**

*   **最小变更原则**: 不新增、不删除、不重命名任何现有的数据库表。所有改造都基于现存的 `Sector`, `DailyQuote`, `TechnicalAnalysis`, `SectorStock` 模型。
*   **“去TTL化”**: 核心改造是将部分被用作带TTL（Time-To-Live）的数据库缓存表，转变为可无限期存储历史数据的永久表。
*   **职责扩展**: 接受并利用现有模型（如 `SectorStock`）的设计现状，让其承担“维度-关系-事实”的混合职责，以避免重构带来的大量代码修改。

---

#### **2. 持久化方案：现有模型的职责划分与改造**

我们将现有核心业务模型划分为两类：**长期持久化表**和**短期缓存数据**。

##### **2.1. 长期持久化表 (Long-Term Persistence)**

这些表的记录一经写入，除非业务需要，否则不会被系统自动删除。

| **模型 / 表** | **定位与职责** | **改造要点** |
| :--- | :--- | :--- |
| **`Sector` / `sectors`** | **板块维度表**<br/>存储板块代码、名称等基础信息。 | 维持现状，无需改造。 |
| **`DailyQuote` / `daily_quotes`** | **板块日线事实表**<br/>存储板块每日的历史行情数据（OHLCV）。 | 维持现状，无需改造。 |
| **`TechnicalAnalysis` / `technical_analysis`** | **板块分析事实表**<br/>存储基于板块日线计算出的历史技术指标。 | 维持现状，无需改造。 |
| **`SectorStock` / `sector_stocks`** | **个股-板块关系及日行情混合表**<br/>1. 存储股票基本信息（代码、名称）。<br/>2. 记录板块与股票的从属关系。<br/>3. **长期存储**个股每日的详细行情。 | **核心改造对象**<br/>1. **必须移除/注释 `cleanup_old_data` 方法**，停止定期删旧数据。<br/>2. 审视并确认 `bulk_upsert` 方法不会误删历史数据。 |

##### **2.2. 短期/实时缓存数据 (Short-Term / Real-Time Cache)**

这些数据时效性极强，不适合直接高频写入MySQL，应优先使用**Redis**作为缓存介质。

| **数据类型** | **描述** | **推荐缓存策略** |
| :--- | :--- | :--- |
| **实时行情** | 所有板块和个股的最新价格、涨跌幅、成交量等。 | **Redis缓存**。交易时段设置超短TTL（如60秒），非交易时段可适当延长。 |
| **实时技术指标** | 基于最新行情计算的MA, KDJ等指标。 | **Redis缓存**。在获取到原始行情后，可异步计算并写入Redis。 |
| **板块成分股（当日）** | 当天某一板块包含的股票列表及其关键行情。 | **Redis缓存**。这是对`sector_stocks`表当日数据的快照，用于高频访问。 |

---

#### **3. 核心改造任务：`SectorStock` 的持久化升级**

为实现方案，唯一的代码级核心任务是改造 `SectorStock` 模型。

**实施步骤:**

1.  **定位模型文件**: 打开 `backend/models/sector_stock.py` 文件。

2.  **禁用数据清理逻辑**:
    找到文件末尾的 `cleanup_old_data` 类方法。此方法是导致 `sector_stocks` 表数据被定期删除的直接原因。
    ```python
    # backend/models/sector_stock.py

    # ... existing code ...

    @classmethod
    def bulk_upsert(cls, sector_code, data_date, stocks_data):
        # ... existing code ...
        pass
    
    # 核心改造点：将此方法整个注释掉或直接删除
    # @classmethod
    # def cleanup_old_data(cls, days_to_keep=7):
    #     """清理旧数据，只保留最近几天的数据"""
    #     cutoff_date = datetime.now().date() - timedelta(days=days_to_keep)
    #     deleted_count = cls.query.filter(cls.data_date < cutoff_date).delete()
    #     db.session.commit()
    #     return deleted_count
    ```

3.  **移除调用点 (可选但推荐)**:
    在整个项目中搜索 `cleanup_old_data` 的调用点（例如可能存在于某个定时任务脚本中），并将其一并移除，确保该逻辑被彻底禁。

---

#### **4. 方案优势与权衡**

*   **优势**:
    *   **极低的改造成本**: 仅需修改一处核心代码，几乎不触及业务逻辑层和API接口层。
    *   **完全的向后兼容**: 现有所有依赖 `SectorStock` 模型的功能将无缝衔接。
    *   **快速实现持久化**: 能够迅速解决核心痛点，实现个股历史行情的长期存储。

*   **权衡 (Trade-off)**:
    *   **数据库范式**: `sector_stocks` 表在设计上存在数据冗余（如每日重复存储股票名称）。本方案为了“最小化改造”而接受了这一现状。在未来的重构中，可以考虑将其拆分为独立的股票维度表和关系表。
    *   **存储空间**: 由于保留了全部历史数据，`sector_stocks` 表的体积会随时间线性增长，需关注数据库的存储容量规划。

---

#### **5. 总结**

本方案精准地回应了在现有架构上实现持久化存储的需求，通过对 `SectorStock` 模型进行一个“小手术”，即可达成目标。它是一个高度务实、低风险、高性价比的改造路径，为系统未来的数据分析和功能扩展奠定了可靠的数据基础。 