import React, { useState, useEffect, useCallback } from 'react'
import {
  Card,
  Table,
  Input,
  Select,
  Row,
  Col,
  Statistic,
  Button,
  Switch,

  Tag,
  Space,
  Typography,
  Tooltip,
  App,
  Dropdown,
  Modal,
  Progress,
} from 'antd'
import {
  SearchOutlined,
  SyncOutlined,
  <PERSON><PERSON><PERSON>Outlined,
  RiseOutlined,
  FallOutlined,
  MinusOutlined,
  <PERSON>Outlined,
  <PERSON><PERSON>ir<PERSON>Outlined,
  <PERSON><PERSON>Outlined,
  <PERSON>culatorOutlined,
  MoreOutlined,
  BulbOutlined,
  LoadingOutlined,
  <PERSON>Outlined,
} from '@ant-design/icons'
import { useNavigate } from 'react-router-dom'
import { useTheme } from '../../theme/ThemeProvider'
import SectorChart from '../Charts/SectorChart'
import { apiService } from '../../services/api'
import useAsyncTask from '../../hooks/useAsyncTask'
import type { ColumnsType } from 'antd/es/table'

const { Option } = Select
const { Title, Text } = Typography

// 添加CSS动画样式
const pulseKeyframes = `
  @keyframes pulse {
    0% {
      transform: scale(1);
      opacity: 1;
    }
    50% {
      transform: scale(1.2);
      opacity: 0.7;
    }
    100% {
      transform: scale(1);
      opacity: 1;
    }
  }

  .sector-name-cell {
    transition: all 0.2s ease-in-out;
  }

  .sector-name-cell:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  }
`

// 将样式注入到页面
if (typeof document !== 'undefined') {
  const styleElement = document.createElement('style')
  styleElement.textContent = pulseKeyframes
  if (!document.head.querySelector('style[data-pulse-animation]')) {
    styleElement.setAttribute('data-pulse-animation', 'true')
    document.head.appendChild(styleElement)
  }
}

// 86个板块数据接口
interface AllSectorData {
  排名: number
  板块名称: string
  板块代码: string
  最新价: number
  涨跌额: number
  涨跌幅: number
  总市值: number
  换手率: number
  上涨家数: number
  下跌家数: number
  领涨股票: string
  '领涨股票-涨跌幅': number
  领涨股票代码?: string
  领涨股票价格?: number
  数据更新时间?: string
  // 新增技术指标列
  趋势?: string
  震荡?: string
  连续上涨?: string
  新高?: string
}

// 统计数据接口
interface AllSectorStats {
  totalSectors: number
  upSectors: number
  downSectors: number
  flatSectors: number
  avgChangePercent: number
  maxChangePercent: number
  minChangePercent: number
  totalMarketValue: number
}

const AllSectors: React.FC = () => {
  const { colorScheme } = useTheme()
  const { message } = App.useApp() // 使用App组件提供的message实例
  const navigate = useNavigate()
  const [sectors, setSectors] = useState<AllSectorData[]>([])
  const [loading, setLoading] = useState(false)
  const [searchText, setSearchText] = useState('')
  const [trendFilter, setTrendFilter] = useState<string>('all')
  const [autoRefresh, setAutoRefresh] = useState(false)
  const [lastUpdateTime, setLastUpdateTime] = useState<string>('')
  const [dataUpdateTime, setDataUpdateTime] = useState<string>('') // 数据实际更新时间
  const [isLoadingData, setIsLoadingData] = useState(false) // 防止重复调用
  const [isForceRefreshing, setIsForceRefreshing] = useState(false) // 强制刷新状态
  const [isBatchUpdatingIndicators, setIsBatchUpdatingIndicators] = useState(false) // 批量更新技术指标状态

  const [hasInitialLoad, setHasInitialLoad] = useState(false) // 防止重复初始加载

  // 请求去重机制
  const [activeRequests, setActiveRequests] = useState<Set<string>>(new Set())

  // 异步任务管理 - 板块数据刷新
  const sectorsRefreshTask = useAsyncTask({
    pollInterval: 30000, // 30秒轮询一次
    showNotifications: false, // 禁用内部通知，使用自定义进度条
    onSuccess: (result) => {
      console.log('板块数据刷新任务完成:', result)
      // 强制刷新页面数据，确保获取最新数据
      forceRefreshPageData()
      // 显示成功通知
      message.success({
        content: '板块数据刷新完成，页面数据已更新',
        duration: 3
      })
    },
    onError: (error) => {
      console.error('板块数据刷新任务失败:', error)
      message.error({
        content: `板块数据刷新失败: ${error}`,
        duration: 5
      })
    },
    onProgress: (status) => {
      console.log('板块数据刷新进度:', status.progress + '%', status.current_step)
    }
  })

  // 同步指标重算状态管理
  const [isIndicatorsUpdating, setIsIndicatorsUpdating] = useState(false)

  // 请求去重辅助函数
  const withRequestDeduplication = async (requestKey: string, requestFn: () => Promise<void>) => {
    if (activeRequests.has(requestKey)) {
      console.log(`请求去重: ${requestKey} 正在进行中，跳过重复请求`)
      message.warning('操作正在进行中，请勿重复点击')
      return
    }

    setActiveRequests(prev => new Set(prev).add(requestKey))

    try {
      await requestFn()
    } finally {
      setActiveRequests(prev => {
        const newSet = new Set(prev)
        newSet.delete(requestKey)
        return newSet
      })
    }
  }

  const [stats, setStats] = useState<AllSectorStats>({
    totalSectors: 0,
    upSectors: 0,
    downSectors: 0,
    flatSectors: 0,
    avgChangePercent: 0,
    maxChangePercent: 0,
    minChangePercent: 0,
    totalMarketValue: 0,
  })

  // 带重试机制的API调用函数
  const fetchWithRetry = async (apiCall: () => Promise<any>, maxRetries: number = 3, delay: number = 2000) => {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await apiCall()
      } catch (error: any) {
        console.log(`API调用失败，第${attempt}次尝试:`, error.message)

        // 如果是连接被拒绝的错误且不是最后一次尝试，则等待后重试
        if (error.code === 'ECONNREFUSED' || error.message.includes('ECONNREFUSED')) {
          if (attempt < maxRetries) {
            console.log(`后端服务可能还在启动中，${delay}ms后重试...`)
            await new Promise(resolve => setTimeout(resolve, delay))
            continue
          }
        }

        // 其他错误或最后一次尝试失败，直接抛出
        throw error
      }
    }
  }

  // 强制刷新页面数据（用于异步任务完成后）
  const forceRefreshPageData = async () => {
    try {
      setIsLoadingData(true)
      console.log('异步任务完成：强制刷新页面数据，获取最新板块信息')

      // 直接调用刷新API，确保获取最新数据
      const response = await fetchWithRetry(() => apiService.refreshSectorsData('all', false), 3, 2000)
      const data = response.data

      if (data.success && Array.isArray(data.data)) {
        setSectors(data.data)
        calculateStats(data.data)
        setLastUpdateTime(new Date().toLocaleString())
        console.log(`强制刷新成功，更新了${data.data.length}个板块数据`)
      } else {
        throw new Error(data.message || '数据格式错误')
      }
    } catch (error: any) {
      console.error('强制刷新页面数据失败:', error)
      // 如果强制刷新失败，回退到缓存数据
      console.log('回退到缓存数据')
      await fetchAllSectorsData()
    } finally {
      setIsLoadingData(false)
    }
  }

  // 页面加载时获取86个板块缓存数据
  const fetchAllSectorsData = async () => {
    // 防止重复调用
    if (isLoadingData) {
      console.log('86板块数据正在加载中，跳过重复请求')
      return
    }

    try {
      setIsLoadingData(true)
      setLoading(true)
      console.log('页面加载：获取86个板块缓存数据')

      // 使用重试机制调用API
      const response = await fetchWithRetry(() => apiService.getAllSectorsRealtime(), 3, 2000)
      const data = response.data

      if (data.success && Array.isArray(data.data)) {
        setSectors(data.data)
        calculateStats(data.data)
        setLastUpdateTime(new Date().toLocaleString())

        // 根据数据源显示不同的消息
        const sourceMessage = data.data_source === 'cache' ? '从缓存加载' :
                             data.data_source === 'api_fallback' ? '缓存为空，API获取' : '加载'

        if (!loading) {
          message.success(`86板块数据${sourceMessage}成功，共${data.data.length}个板块`)
        }
      } else {
        throw new Error(data.message || '数据格式错误')
      }
    } catch (error: any) {
      console.error('获取86个板块数据失败:', error)

      // 如果是连接错误，提供更友好的错误信息
      if (error.code === 'ECONNREFUSED' || error.message.includes('ECONNREFUSED')) {
        message.error('无法连接到后端服务，请确保后端服务已启动并稍后重试')
      } else {
        message.error('获取数据失败: ' + (error.response?.data?.message || error.message || '请稍后重试'))
      }
    } finally {
      setLoading(false)
      setIsLoadingData(false)
    }
  }

  // 智能更新86个板块数据
  const handleManualRefresh = async () => {
    await withRequestDeduplication('manual-refresh', async () => {

    try {
      setIsLoadingData(true)
      setLoading(true)
      console.log('智能更新：根据数据新鲜度和交易时间智能判断更新策略')

      // 调用真正的智能更新API
      const response = await apiService.smartDataUpdate()
      const data = response.data

      if (data.success) {
        // 智能更新完成后，重新获取最新数据
        console.log('智能更新完成，重新获取最新板块数据')
        const refreshResponse = await apiService.getAllSectorsRealtime()
        const refreshData = refreshResponse.data

        if (refreshData.success && Array.isArray(refreshData.data)) {
          setSectors(refreshData.data)
          calculateStats(refreshData.data)
          setLastUpdateTime(new Date().toLocaleString())

          // 显示智能更新结果
          const updateMessage = data.message || '智能更新完成'
          message.success(`${updateMessage}，共${refreshData.data.length}个板块`)
        } else {
          throw new Error(refreshData.message || '获取更新后数据失败')
        }
      } else {
        throw new Error(data.message || '智能更新失败')
      }
    } catch (error: any) {
      console.error('手动刷新86个板块数据失败:', error)
      message.error('刷新数据失败: ' + (error.response?.data?.message || error.message || '请稍后重试'))
    } finally {
      setLoading(false)
      setIsLoadingData(false)
    }
    })
  }

  // 强制刷新数据（忽略缓存，直接从API获取）
  const handleForceRefresh = async () => {
    await withRequestDeduplication('force-refresh', async () => {

    // 显示确认对话框
    Modal.confirm({
      title: '确认完全重载？',
      content: (
        <div>
          <p>完全重载将：</p>
          <ul>
            <li>忽略所有缓存数据</li>
            <li>直接从API获取最新数据</li>
            <li>预计耗时 3-5 分钟</li>
            <li>重载期间将禁用所有界面操作</li>
          </ul>
          <p style={{ color: '#ff6b35', fontWeight: 'bold' }}>
            ⚠️ 重载过程中请勿关闭浏览器或刷新页面
          </p>
          <p>建议在数据异常时使用。</p>
        </div>
      ),
      okText: '确认重载',
      cancelText: '取消',
      okType: 'primary',
      onOk: () => performForceRefresh(),
      onCancel: () => {},
    });
    })
  }

  // 执行强制刷新的实际逻辑（使用同步SSE处理）
  const performForceRefresh = async () => {
    try {
      setIsForceRefreshing(true)
      console.log('启动板块数据完全重载（同步处理）')

      // 创建EventSource连接来接收进度更新
      const eventSource = new EventSource('/api/sectors/full-reload', {
        withCredentials: true
      })

      // 显示Material Design 3风格的进度对话框
      let modalInstance: any = null
      modalInstance = Modal.info({
        title: (
          <div style={{
            display: 'flex',
            alignItems: 'center',
            fontSize: '18px',
            fontWeight: 600,
            color: '#1976d2'
          }}>
            <div style={{
              width: 24,
              height: 24,
              borderRadius: '50%',
              background: 'linear-gradient(45deg, #1976d2, #42a5f5)',
              marginRight: 12,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              <div style={{
                width: 8,
                height: 8,
                borderRadius: '50%',
                backgroundColor: 'white',
                animation: 'pulse 1.5s ease-in-out infinite'
              }} />
            </div>
            完全重载进行中
          </div>
        ),
        content: (
          <div style={{ padding: '16px 0' }}>
            <div style={{ marginBottom: 20 }}>
              <Progress
                percent={0}
                status="active"
                strokeColor={{
                  '0%': '#1976d2',
                  '100%': '#42a5f5',
                }}
                trailColor="#e3f2fd"
                strokeWidth={8}
                format={(percent) => (
                  <span style={{ color: '#1976d2', fontWeight: 600 }}>
                    {percent}%
                  </span>
                )}
              />
            </div>
            <div style={{
              fontSize: '14px',
              color: '#666',
              textAlign: 'center',
              padding: '8px 16px',
              backgroundColor: '#f5f5f5',
              borderRadius: '8px',
              border: '1px solid #e0e0e0'
            }}>
              正在初始化...
            </div>
          </div>
        ),
        okButtonProps: { style: { display: 'none' } },
        closable: false,
        maskClosable: false,
        width: 520,
        centered: true,
        maskStyle: { backgroundColor: 'rgba(0, 0, 0, 0.6)' },
        bodyStyle: { padding: '24px' }
      })

      // 处理进度更新
      eventSource.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data)
          console.log('收到进度更新:', data)

          switch (data.type) {
            case 'start':
              modalInstance.update({
                content: (
                  <div style={{ padding: '16px 0' }}>
                    <div style={{ marginBottom: 20 }}>
                      <Progress
                        percent={0}
                        status="active"
                        strokeColor={{
                          '0%': '#1976d2',
                          '100%': '#42a5f5',
                        }}
                        trailColor="#e3f2fd"
                        strokeWidth={8}
                        format={(percent) => (
                          <span style={{ color: '#1976d2', fontWeight: 600 }}>
                            {percent}%
                          </span>
                        )}
                      />
                    </div>
                    <div style={{
                      fontSize: '14px',
                      color: '#666',
                      textAlign: 'center',
                      padding: '8px 16px',
                      backgroundColor: '#f5f5f5',
                      borderRadius: '8px',
                      border: '1px solid #e0e0e0'
                    }}>
                      {data.message}
                    </div>
                  </div>
                )
              })
              break

            case 'progress':
              const progress = data.progress || 0
              const stepInfo = `步骤 ${data.step || 1}/${data.total_steps || 3}`
              modalInstance.update({
                content: (
                  <div style={{ padding: '16px 0' }}>
                    <div style={{ marginBottom: 20 }}>
                      <Progress
                        percent={progress}
                        status="active"
                        strokeColor={{
                          '0%': '#1976d2',
                          '100%': '#42a5f5',
                        }}
                        trailColor="#e3f2fd"
                        strokeWidth={8}
                        format={(percent) => (
                          <span style={{ color: '#1976d2', fontWeight: 600 }}>
                            {percent}%
                          </span>
                        )}
                      />
                    </div>
                    <div style={{
                      fontSize: '14px',
                      color: '#666',
                      textAlign: 'center',
                      padding: '8px 16px',
                      backgroundColor: '#f5f5f5',
                      borderRadius: '8px',
                      border: '1px solid #e0e0e0',
                      marginBottom: 12
                    }}>
                      {data.message}
                    </div>
                    {data.completed && data.total && (
                      <div style={{
                        fontSize: '12px',
                        color: '#999',
                        textAlign: 'center',
                        padding: '4px 8px',
                        backgroundColor: '#fafafa',
                        borderRadius: '4px'
                      }}>
                        {stepInfo} • 进度: {data.completed}/{data.total}
                      </div>
                    )}
                  </div>
                )
              })
              break

            case 'complete':
              eventSource.close()
              modalInstance.destroy()
              message.success({
                content: data.message,
                duration: 5
              })
              // 刷新页面数据
              forceRefreshPageData()
              break

            case 'error':
              eventSource.close()
              modalInstance.destroy()
              message.error({
                content: data.message,
                duration: 8
              })
              break

            case 'warning':
              // 显示警告但继续处理
              message.warning({
                content: data.message,
                duration: 5
              })
              break
          }
        } catch (e) {
          console.error('解析进度数据失败:', e)
        }
      }

      // 处理连接错误
      eventSource.onerror = (error) => {
        console.error('SSE连接错误:', error)
        eventSource.close()
        if (modalInstance) {
          modalInstance.destroy()
        }
        message.error({
          content: '完全重载过程中发生网络错误，请检查网络连接后重试',
          duration: 8
        })
        setIsForceRefreshing(false)
      }

      // 设置超时处理（5分钟）
      setTimeout(() => {
        if (eventSource.readyState !== EventSource.CLOSED) {
          eventSource.close()
          if (modalInstance) {
            modalInstance.destroy()
          }
          message.error({
            content: '完全重载超时（5分钟），请稍后重试',
            duration: 8
          })
          setIsForceRefreshing(false)
        }
      }, 300000) // 5分钟超时
    } catch (error: any) {
      console.error('启动完全重载失败:', error)

      // 详细的错误信息提取
      let errorMessage = '完全重载失败，请稍后重试'
      if (error.response?.data?.error) {
        errorMessage = error.response.data.error
      } else if (error.response?.data?.message) {
        errorMessage = error.response.data.message
      } else if (error.message) {
        errorMessage = error.message
      }

      // 根据错误类型提供不同的提示
      if (error.response?.status === 503) {
        errorMessage = '数据库连接超时，请稍后重试'
      } else if (error.response?.status === 500) {
        errorMessage = '服务器内部错误，请检查网络连接或稍后重试'
      }

      message.error({
        content: `完全重载失败: ${errorMessage}`,
        duration: 8
      })
    } finally {
      setIsForceRefreshing(false)
    }
  }

  // 批量更新所有板块技术指标
  const handleBatchUpdateIndicators = async () => {
    await withRequestDeduplication('batch-update-indicators', async () => {
      try {
        setIsBatchUpdatingIndicators(true)

        // 显示确认对话框
        Modal.confirm({
          title: '批量更新技术指标',
          content: '这将重新计算所有86个板块的技术指标，可能需要几分钟时间。是否继续？',
          okText: '确认更新',
          cancelText: '取消',
          onOk: async () => {
            try {
              message.loading('正在批量更新技术指标...', 0)

              const response = await apiService.updateAllSectorsIndicators()

              if (response.data.success) {
                message.destroy()
                message.success({
                  content: `技术指标更新完成！成功更新 ${response.data.data.success_count}/${response.data.data.total_sectors} 个板块`,
                  duration: 5
                })

                // 刷新页面数据
                await forceRefreshPageData()
              } else {
                throw new Error(response.data.error || '批量更新失败')
              }
            } catch (error: any) {
              message.destroy()
              message.error({
                content: `批量更新技术指标失败: ${error.response?.data?.error || error.message || '请稍后重试'}`,
                duration: 8
              })
            }
          }
        })
      } catch (error: any) {
        console.error('批量更新技术指标失败:', error)
        message.error({
          content: `批量更新技术指标失败: ${error.message || '请稍后重试'}`,
          duration: 8
        })
      } finally {
        setIsBatchUpdatingIndicators(false)
      }
    })
  }

  // 重试机制的辅助函数
  const retryWithDelay = async (fn: () => Promise<any>, maxRetries: number = 3, delay: number = 5000): Promise<any> => {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await fn()
      } catch (error: any) {
        console.log(`第${attempt}次尝试失败:`, error.message)

        if (attempt === maxRetries) {
          throw error // 最后一次尝试失败，抛出错误
        }

        // 显示重试提示
        message.warning(`第${attempt}次尝试失败，${delay/1000}秒后进行第${attempt + 1}次尝试...`)

        // 等待指定时间后重试
        await new Promise(resolve => setTimeout(resolve, delay))

        // 递增延迟时间（指数退避）
        delay = Math.min(delay * 1.5, 30000) // 最大30秒
      }
    }
  }

  // 更新技术指标数据（同步处理版本）
  const handleUpdateIndicators = async () => {
    await withRequestDeduplication('update-indicators', async () => {

    // 显示确认对话框
    Modal.confirm({
      title: '确认指标重算？',
      content: (
        <div>
          <p>指标重算将：</p>
          <ul>
            <li>检查缺失的交易日数据</li>
            <li>进行智能增量更新</li>
            <li>重新计算技术指标</li>
            <li>预计耗时 30-60 秒</li>
          </ul>
          <p style={{ color: '#ff6b35', fontWeight: 'bold' }}>
            ⚠️ 任务执行期间页面将被锁定，请耐心等待完成
          </p>
          <p>系统会自动检测需要更新的数据，仅更新必要的部分。</p>
        </div>
      ),
      okText: '确认重算',
      cancelText: '取消',
      onOk: () => performSyncIndicatorUpdate(),
      onCancel: () => {},
    });
    })
  }

  // 执行技术指标更新的实际逻辑（同步处理版本）
  const performSyncIndicatorUpdate = async () => {
    // 显示MD3风格的加载提示框
    const loadingModal = Modal.info({
      title: (
        <div style={{
          display: 'flex',
          alignItems: 'center',
          color: colorScheme.primary
        }}>
          <LoadingOutlined style={{ marginRight: 8, fontSize: '18px' }} />
          任务正在进行中
        </div>
      ),
      content: (
        <div style={{
          padding: '16px 0',
          color: colorScheme.onSurface
        }}>
          <p>正在执行技术指标重算，请耐心等待...</p>
          <p style={{
            fontSize: '14px',
            color: colorScheme.onSurfaceVariant,
            marginBottom: 0
          }}>
            • 系统正在检查缺失的交易日数据<br/>
            • 执行智能增量更新策略<br/>
            • 重新计算技术指标<br/>
            • 预计耗时 30-60 秒
          </p>
        </div>
      ),
      icon: null,
      okButtonProps: { style: { display: 'none' } }, // 隐藏确定按钮
      closable: false,
      maskClosable: false,
      keyboard: false,
      style: {
        borderRadius: '12px',
      }
    })

    try {
      // 设置页面阻塞状态
      setIsIndicatorsUpdating(true)
      setLoading(true)

      console.log('开始同步技术指标更新')

      // 调用同步API（300秒超时）
      const response = await apiService.updateTechnicalIndicators()
      const data = response.data

      // 关闭加载提示框
      loadingModal.destroy()

      if (data.success) {
        // 显示成功结果
        const { total_sectors, updated_sectors, failed_sectors, processing_time_seconds } = data.data

        Modal.success({
          title: '指标重算完成',
          content: (
            <div>
              <p><strong>处理结果：</strong></p>
              <ul>
                <li>总板块数：{total_sectors} 个</li>
                <li>更新成功：{updated_sectors} 个</li>
                <li>更新失败：{failed_sectors} 个</li>
                <li>处理耗时：{processing_time_seconds} 秒</li>
              </ul>
              <p style={{ color: colorScheme.primary, marginTop: '12px' }}>
                页面数据将自动刷新以显示最新结果
              </p>
            </div>
          ),
          onOk: () => {
            // 强制刷新页面数据
            forceRefreshPageData()
          }
        })
      } else {
        throw new Error(data.error || '指标重算失败')
      }
    } catch (error: any) {
      // 关闭加载提示框
      loadingModal.destroy()

      console.error('同步技术指标更新失败:', error)

      // 增强的错误处理
      let errorMessage = '指标重算失败'
      if (error.code === 'ECONNABORTED') {
        errorMessage += ': 请求超时，请检查网络连接或稍后重试'
      } else if (error.response?.status === 500) {
        errorMessage += ': 服务器内部错误，请稍后重试'
      } else if (error.response?.status === 429) {
        errorMessage += ': 请求过于频繁，请稍后重试'
      } else if (error.response?.status === 408) {
        errorMessage += ': 处理超时，请稍后重试'
      } else {
        errorMessage += ': ' + (error.response?.data?.message || error.message || '未知错误')
      }

      Modal.error({
        title: '指标重算失败',
        content: (
          <div>
            <p>{errorMessage}</p>
            <p style={{ color: colorScheme.onSurfaceVariant, fontSize: '14px', marginTop: '12px' }}>
              如果问题持续存在，请联系系统管理员
            </p>
          </div>
        )
      })
    } finally {
      setIsIndicatorsUpdating(false)
      setLoading(false)
    }
  }

  // 显示功能说明对话框
  const showFunctionHelp = () => {
    Modal.info({
      title: '功能说明',
      width: 600,
      content: (
        <div style={{ lineHeight: '1.6' }}>
          <h4>🔄 智能更新</h4>
          <p>根据当前时间智能判断数据获取策略：</p>
          <ul>
            <li><strong>交易时间内</strong>：从API获取最新数据</li>
            <li><strong>非交易时间</strong>：使用缓存数据</li>
            <li><strong>API失败时</strong>：自动降级到缓存数据</li>
          </ul>

          <h4>⚡ 完全重载</h4>
          <p>忽略所有缓存，强制从API获取最新数据：</p>
          <ul>
            <li>适用于数据异常或缓存损坏时</li>
            <li>耗时较长（30-60秒）</li>
            <li>获取最新的API数据并更新缓存</li>
          </ul>

          <h4>📊 指标重算</h4>
          <p>重新计算技术指标和补全缺失数据：</p>
          <ul>
            <li>检查缺失的交易日数据</li>
            <li>增量更新策略，仅更新必要部分</li>
            <li>重新计算趋势、震荡、连续上涨、新高判断</li>
          </ul>

          <p style={{ marginTop: '16px', color: '#666' }}>
            💡 建议：日常使用"智能更新"即可，仅在数据异常时使用其他功能。
          </p>
        </div>
      ),
      okText: '我知道了',
    });
  }

  // 从板块数据中提取最新的数据更新时间
  const extractDataUpdateTime = (data: AllSectorData[]) => {
    if (!data || data.length === 0) return ''

    // 找到所有有效的数据更新时间
    const updateTimes = data
      .map(sector => sector.数据更新时间)
      .filter(time => time && time.trim() !== '')
      .sort()

    if (updateTimes.length === 0) return ''

    // 返回最新的时间（排序后的最后一个）
    return updateTimes[updateTimes.length - 1]
  }

  // 计算统计数据
  const calculateStats = (data: AllSectorData[]) => {
    const upSectors = data.filter(s => s.涨跌幅 > 0).length
    const downSectors = data.filter(s => s.涨跌幅 < 0).length
    const flatSectors = data.filter(s => s.涨跌幅 === 0).length

    const avgChangePercent = data.reduce((sum, s) => sum + s.涨跌幅, 0) / data.length
    const maxChangePercent = Math.max(...data.map(s => s.涨跌幅))
    const minChangePercent = Math.min(...data.map(s => s.涨跌幅))
    const totalMarketValue = data.reduce((sum, s) => sum + s.总市值, 0)

    setStats({
      totalSectors: data.length,
      upSectors,
      downSectors,
      flatSectors,
      avgChangePercent,
      maxChangePercent,
      minChangePercent,
      totalMarketValue,
    })

    // 提取并设置数据更新时间
    const latestDataTime = extractDataUpdateTime(data)
    if (latestDataTime) {
      setDataUpdateTime(latestDataTime)
    }
  }

  // 过滤数据
  const filteredSectors = sectors.filter(sector => {
    const matchesSearch = 
      sector.板块名称.toLowerCase().includes(searchText.toLowerCase()) ||
      sector.板块代码.toLowerCase().includes(searchText.toLowerCase())
    
    const matchesTrend = 
      trendFilter === 'all' ||
      (trendFilter === 'up' && sector.涨跌幅 > 0) ||
      (trendFilter === 'down' && sector.涨跌幅 < 0) ||
      (trendFilter === 'flat' && sector.涨跌幅 === 0)
    
    return matchesSearch && matchesTrend
  })

  // 获取涨跌幅颜色（中国股市约定：红涨绿跌）
  const getChangeColor = (change: number) => {
    if (change > 0) return colorScheme.bullish // 红色表示上涨
    if (change < 0) return colorScheme.bearish // 绿色表示下跌
    return colorScheme.neutral // 灰色表示平盘
  }

  // 获取涨跌幅图标
  const getChangeIcon = (change: number) => {
    if (change > 0) return <RiseOutlined />
    if (change < 0) return <FallOutlined />
    return <MinusOutlined />
  }

  // 表格列配置
  const columns: ColumnsType<AllSectorData> = [
    {
      title: '排名',
      dataIndex: '排名',
      key: '排名',
      width: 60,
      align: 'center',
      render: (rank: number) => (
        <Tag color={rank <= 3 ? 'gold' : rank <= 10 ? 'blue' : 'default'} size="small">
          {rank}
        </Tag>
      ),
    },
    {
      title: '板块名称',
      dataIndex: '板块名称',
      key: '板块名称',
      width: 110,
      render: (name: string, record: AllSectorData) => (
        <div
          style={{
            cursor: 'pointer',
            padding: '4px',
            borderRadius: '4px',
            transition: 'all 0.2s ease',
          }}
          className="sector-name-cell"
          onClick={(e) => {
            // 检查是否按住Ctrl键或中键点击，如果是则在新标签页打开
            if (e.ctrlKey || e.metaKey || e.button === 1) {
              window.open(`/sectors/${record.板块代码}`, '_blank')
            } else {
              // 默认在新窗口打开板块详情页面
              window.open(`/sectors/${record.板块代码}`, '_blank')
            }
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.backgroundColor = colorScheme.surfaceContainerHighest
            e.currentTarget.style.transform = 'translateY(-1px)'
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.backgroundColor = 'transparent'
            e.currentTarget.style.transform = 'translateY(0)'
          }}
        >
          <div style={{ display: 'flex', alignItems: 'center', gap: '6px' }}>
            <Text
              strong
              style={{
                color: colorScheme.primary,
                textDecoration: 'none',
                fontSize: '14px'
              }}
            >
              {name}
            </Text>
            <LinkOutlined
              style={{
                color: colorScheme.primary,
                fontSize: '12px',
                opacity: 0.7,
                marginRight: '2px'
              }}
            />
            <span
              style={{
                color: colorScheme.primary,
                fontSize: '10px',
                opacity: 0.6,
                fontWeight: 'normal'
              }}
              title="点击在新窗口打开"
            >
              ↗
            </span>
          </div>
          <Text
            type="secondary"
            style={{
              fontSize: '12px',
              display: 'block',
              marginTop: '2px'
            }}
          >
            {record.板块代码}
          </Text>
        </div>
      ),
    },
    {
      title: '涨跌幅',
      dataIndex: '涨跌幅',
      key: '涨跌幅',
      width: 85,
      align: 'right',
      sorter: (a, b) => a.涨跌幅 - b.涨跌幅,
      render: (change: number, record: AllSectorData) => (
        <Space size="small">
          {getChangeIcon(change)}
          <Text style={{ color: getChangeColor(change), fontWeight: 500, fontSize: '13px' }}>
            {change > 0 ? '+' : ''}{change.toFixed(2)}%
          </Text>
        </Space>
      ),
    },
    {
      title: '趋势',
      dataIndex: '趋势',
      key: '趋势',
      width: 80,
      align: 'center',
      responsive: ['md'],
      render: (trend: string) => {
        const isUpTrend = trend === '上升趋势';
        const isDownTrend = trend === '下降趋势';
        return (
          <Tag
            size="small"
            color={isUpTrend ? 'red' : isDownTrend ? 'green' : 'default'}
            style={{
              color: isUpTrend ? colorScheme.error : isDownTrend ? colorScheme.success : colorScheme.onSurface,
              borderColor: isUpTrend ? colorScheme.error : isDownTrend ? colorScheme.success : colorScheme.outline,
              backgroundColor: isUpTrend ? `${colorScheme.error}10` : isDownTrend ? `${colorScheme.success}10` : colorScheme.surfaceVariant,
              fontSize: '11px'
            }}
          >
            {trend === '上升趋势' ? '上升' : trend === '下降趋势' ? '下降' : trend || '不足'}
          </Tag>
        );
      },
    },
    {
      title: '震荡',
      dataIndex: '震荡',
      key: '震荡',
      width: 70,
      align: 'center',
      responsive: ['lg'],
      render: (oscillation: string) => {
        const isOscillating = oscillation === '震荡';
        return (
          <Tag
            size="small"
            color={isOscillating ? 'orange' : 'blue'}
            style={{
              color: isOscillating ? '#fa8c16' : colorScheme.primary,
              borderColor: isOscillating ? '#fa8c16' : colorScheme.primary,
              backgroundColor: isOscillating ? '#fa8c1610' : `${colorScheme.primary}10`,
              fontSize: '11px'
            }}
          >
            {oscillation || '不足'}
          </Tag>
        );
      },
    },
    {
      title: '连涨',
      dataIndex: '连续上涨',
      key: '连续上涨',
      width: 70,
      align: 'center',
      responsive: ['sm'],
      render: (consecutive: string) => {
        const isConsecutiveRise = consecutive === '连续上涨';
        return (
          <Tag
            size="small"
            color={isConsecutiveRise ? 'red' : 'default'}
            style={{
              color: isConsecutiveRise ? colorScheme.error : colorScheme.onSurface,
              borderColor: isConsecutiveRise ? colorScheme.error : colorScheme.outline,
              backgroundColor: isConsecutiveRise ? `${colorScheme.error}10` : colorScheme.surfaceVariant,
              fontSize: '11px'
            }}
          >
            {consecutive === '连续上涨' ? '连涨' : consecutive || '不足'}
          </Tag>
        );
      },
    },
    {
      title: '新高',
      dataIndex: '新高',
      key: '新高',
      width: 80,
      align: 'center',
      responsive: ['sm'],
      render: (newHigh: string) => {
        const isNewHigh = newHigh && newHigh !== '无新高' && newHigh !== '数据不足';
        const getNewHighColor = (type: string) => {
          if (type === '20日新高') return '#ff4d4f';
          if (type === '10日新高') return '#ff7a45';
          if (type === '5日新高') return '#ffa940';
          return colorScheme.onSurface;
        };

        return (
          <Tag
            size="small"
            color={isNewHigh ? 'red' : 'default'}
            style={{
              color: isNewHigh ? getNewHighColor(newHigh) : colorScheme.onSurface,
              borderColor: isNewHigh ? getNewHighColor(newHigh) : colorScheme.outline,
              backgroundColor: isNewHigh ? `${getNewHighColor(newHigh)}10` : colorScheme.surfaceVariant,
              fontSize: '11px'
            }}
          >
            {newHigh === '20日新高' ? '20日' : newHigh === '10日新高' ? '10日' : newHigh === '5日新高' ? '5日' : newHigh || '无'}
          </Tag>
        );
      },
    },
    {
      title: '市值',
      dataIndex: '总市值',
      key: '总市值',
      width: 80,
      align: 'right',
      sorter: (a, b) => a.总市值 - b.总市值,
      render: (value: number) => (
        <Text style={{ color: colorScheme.onSurface, fontSize: '13px' }}>
          {(value / 100000000).toFixed(0)}亿
        </Text>
      ),
    },
    {
      title: '换手',
      dataIndex: '换手率',
      key: '换手率',
      width: 70,
      align: 'right',
      sorter: (a, b) => a.换手率 - b.换手率,
      render: (rate: number) => (
        <Text style={{ color: colorScheme.onSurface, fontSize: '13px' }}>
          {rate.toFixed(1)}%
        </Text>
      ),
    },
    {
      title: '涨跌',
      key: 'updown',
      width: 70,
      align: 'center',
      render: (_, record: AllSectorData) => (
        <div
          style={{
            cursor: 'pointer',
            padding: '2px',
            borderRadius: '4px',
            transition: 'all 0.2s ease',
          }}
          onClick={(e) => {
            e.stopPropagation()
            // 在新窗口打开股票列表
            window.open(`/sectors/${record.板块代码}/stocks`, '_blank')
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.backgroundColor = colorScheme.surfaceContainerHighest
            e.currentTarget.style.transform = 'scale(1.05)'
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.backgroundColor = 'transparent'
            e.currentTarget.style.transform = 'scale(1)'
          }}
        >
          <div style={{ marginBottom: '1px' }}>
            <Text style={{
              color: colorScheme.error,
              fontSize: '11px',
              fontWeight: 600
            }}>
              ↑{record.上涨家数}
            </Text>
          </div>
          <div>
            <Text style={{
              color: colorScheme.success,
              fontSize: '11px',
              fontWeight: 600
            }}>
              ↓{record.下跌家数}
            </Text>
          </div>
        </div>
      ),
    },
    {
      title: '领涨股票',
      key: 'leader',
      width: 140,
      render: (_, record: AllSectorData) => (
        <div style={{
          lineHeight: '1.2',
          padding: '2px',
          borderRadius: '6px',
          transition: 'all 0.2s ease'
        }}>
          {/* 股票名称行 */}
          <div style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            marginBottom: '3px'
          }}>
            <Text style={{
              color: colorScheme.onSurface,
              fontSize: '13px',
              fontWeight: 600,
              maxWidth: '85px',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap'
            }}>
              {record.领涨股票}
            </Text>

            {/* 涨跌幅标签 */}
            <div style={{
              backgroundColor: `${getChangeColor(record['领涨股票-涨跌幅'])}20`,
              border: `1px solid ${getChangeColor(record['领涨股票-涨跌幅'])}40`,
              borderRadius: '4px',
              padding: '1px 4px',
              minWidth: '45px',
              textAlign: 'center'
            }}>
              <Text style={{
                color: getChangeColor(record['领涨股票-涨跌幅']),
                fontSize: '11px',
                fontWeight: 700
              }}>
                {record['领涨股票-涨跌幅'] > 0 ? '+' : ''}{record['领涨股票-涨跌幅'].toFixed(2)}%
              </Text>
            </div>
          </div>

          {/* 股票代码和价格行 */}
          <div style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            gap: '4px'
          }}>
            {/* 股票代码 */}
            {record.领涨股票代码 && record.领涨股票代码 !== '-' ? (
              <div style={{
                backgroundColor: colorScheme.surfaceContainerHigh,
                border: `1px solid ${colorScheme.outline}30`,
                borderRadius: '4px',
                padding: '2px 6px',
                flex: 1
              }}>
                <Text style={{
                  color: colorScheme.primary,
                  fontSize: '12px',
                  fontWeight: 600,
                  fontFamily: 'monospace'
                }}>
                  {record.领涨股票代码}
                </Text>
              </div>
            ) : (
              <div style={{ flex: 1 }}>
                <Text style={{
                  color: colorScheme.onSurfaceVariant,
                  fontSize: '11px',
                  fontStyle: 'italic'
                }}>
                  无代码
                </Text>
              </div>
            )}

            {/* 股票价格 */}
            {record.领涨股票价格 && record.领涨股票价格 > 0 && (
              <div style={{ textAlign: 'right' }}>
                <Text style={{
                  color: colorScheme.onSurface,
                  fontSize: '11px',
                  fontWeight: 500,
                  fontFamily: 'monospace'
                }}>
                  ¥{record.领涨股票价格.toFixed(2)}
                </Text>
              </div>
            )}
          </div>
        </div>
      ),
    },
  ]

  // 自动刷新
  useEffect(() => {
    let interval: NodeJS.Timeout
    if (autoRefresh) {
      interval = setInterval(handleManualRefresh, 30000) // 30秒刷新一次
    }
    return () => {
      if (interval) clearInterval(interval)
    }
  }, [autoRefresh])

  // 初始化数据（防止重复调用）
  useEffect(() => {
    if (!hasInitialLoad) {
      setHasInitialLoad(true)
      fetchAllSectorsData()
    }
  }, [])

  // 设置页面标题
  useEffect(() => {
    document.title = '板块概览 - 股票分析系统'

    // 组件卸载时恢复默认标题
    return () => {
      document.title = '股票分析系统'
    }
  }, [])

  // 组件卸载时清理异步任务
  useEffect(() => {
    return () => {
      // 停止板块刷新任务轮询
      sectorsRefreshTask.stopPolling()
      // 注意：指标更新已改为同步处理，无需清理
    }
  }, [])

  return (
    <div>
      {/* 页面标题 */}
      <div className="md3-margin-bottom-lg">
        <Title
          level={2}
          className="md3-title-large"
          style={{
            color: colorScheme.onSurface,
            margin: 0,
            marginBottom: '8px'
          }}
        >
          全部86板块数据
        </Title>
        <Text
          type="secondary"
          className="md3-body-medium"
          style={{ color: colorScheme.onSurfaceVariant }}
        >
          实时展示东方财富86个行业板块的详细数据和分析
        </Text>
      </div>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]} className="md3-margin-bottom-lg">
        <Col xs={12} sm={6} md={4}>
          <Card
            className="md3-card md3-transition-medium"
            style={{
              backgroundColor: colorScheme.surfaceContainerLow,
              borderColor: colorScheme.outlineVariant,
            }}
          >
            <Statistic
              title="总板块数"
              value={stats.totalSectors}
              prefix={<BarChartOutlined style={{ color: colorScheme.primary }} />}
              valueStyle={{
                color: colorScheme.onSurface,
                fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
                fontWeight: 500,
              }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={6} md={4}>
          <Card
            className="md3-card md3-transition-medium"
            style={{
              backgroundColor: colorScheme.surfaceContainerLow,
              borderColor: colorScheme.outlineVariant,
            }}
          >
            <Statistic
              title="上涨板块"
              value={stats.upSectors}
              prefix={<RiseOutlined style={{ color: colorScheme.error }} />}
              valueStyle={{
                color: colorScheme.error,
                fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
                fontWeight: 500,
              }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={6} md={4}>
          <Card
            className="md3-card md3-transition-medium"
            style={{
              backgroundColor: colorScheme.surfaceContainerLow,
              borderColor: colorScheme.outlineVariant,
            }}
          >
            <Statistic
              title="下跌板块"
              value={stats.downSectors}
              prefix={<FallOutlined style={{ color: colorScheme.success }} />}
              valueStyle={{
                color: colorScheme.success,
                fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
                fontWeight: 500,
              }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={6} md={4}>
          <Card
            className="md3-card md3-transition-medium"
            style={{
              backgroundColor: colorScheme.surfaceContainerLow,
              borderColor: colorScheme.outlineVariant,
            }}
          >
            <Statistic
              title="平均涨跌幅"
              value={stats.avgChangePercent}
              precision={2}
              suffix="%"
              prefix={getChangeIcon(stats.avgChangePercent)}
              valueStyle={{
                color: getChangeColor(stats.avgChangePercent),
                fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
                fontWeight: 500,
              }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={6} md={4}>
          <Card
            className="md3-card md3-transition-medium"
            style={{
              backgroundColor: colorScheme.surfaceContainerLow,
              borderColor: colorScheme.outlineVariant,
            }}
          >
            <Statistic
              title="最大涨幅"
              value={stats.maxChangePercent}
              precision={2}
              suffix="%"
              prefix={<TrophyOutlined style={{ color: colorScheme.error }} />}
              valueStyle={{
                color: colorScheme.error,
                fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
                fontWeight: 500,
              }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={6} md={4}>
          <Card
            className="md3-card md3-transition-medium"
            style={{
              backgroundColor: colorScheme.surfaceContainerLow,
              borderColor: colorScheme.outlineVariant,
            }}
          >
            <Statistic
              title="总市值"
              value={stats.totalMarketValue / 1000000000000}
              precision={1}
              suffix="万亿"
              prefix={<BarChartOutlined style={{ color: colorScheme.primary }} />}
              valueStyle={{
                color: colorScheme.onSurface,
                fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
                fontWeight: 500,
              }}
            />
          </Card>
        </Col>
      </Row>

      {/* 控制面板 */}
      <Card
        className="md3-card md3-margin-bottom-lg"
        style={{
          backgroundColor: colorScheme.surfaceContainerLow,
          borderColor: colorScheme.outlineVariant,
        }}
      >
        <Row gutter={[16, 16]} align="middle">
          <Col xs={24} sm={8} md={6}>
            <Input
              placeholder="搜索板块名称或代码"
              prefix={<SearchOutlined style={{ color: colorScheme.onSurfaceVariant }} />}
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              style={{
                backgroundColor: colorScheme.surface,
                borderColor: colorScheme.outline,
                color: colorScheme.onSurface,
              }}
            />
          </Col>
          <Col xs={24} sm={8} md={6}>
            <Select
              value={trendFilter}
              onChange={setTrendFilter}
              style={{ width: '100%' }}
              placeholder="选择趋势类型"
            >
              <Option value="all">全部</Option>
              <Option value="up">上涨</Option>
              <Option value="down">下跌</Option>
              <Option value="flat">平盘</Option>
            </Select>
          </Col>
          <Col xs={24} sm={12} md={8}>
            <Space.Compact>
              {/* 主按钮：智能更新 */}
              <Tooltip title="根据交易时间智能判断数据源，交易时间内获取最新数据，非交易时间使用缓存">
                <Button
                  type="primary"
                  icon={<SyncOutlined />}
                  loading={loading && !isForceRefreshing && !isIndicatorsUpdating && !isBatchUpdatingIndicators && !sectorsRefreshTask.isTaskRunning}
                  onClick={handleManualRefresh}
                  className="md3-button-filled"
                  disabled={isForceRefreshing || isIndicatorsUpdating || isBatchUpdatingIndicators || sectorsRefreshTask.isTaskRunning}
                  style={{
                    minWidth: '120px'
                  }}
                >
                  智能更新
                </Button>
              </Tooltip>

              {/* 下拉菜单：高级选项 */}
              <Dropdown
                menu={{
                  items: [
                    {
                      key: 'force-reload',
                      icon: <ThunderboltOutlined />,
                      label: sectorsRefreshTask.isTaskRunning ? '重载进行中...' : '完全重载',
                      disabled: loading || sectorsRefreshTask.isTaskRunning || isIndicatorsUpdating || isBatchUpdatingIndicators,
                      onClick: handleForceRefresh,
                    },
                    {
                      key: 'recalc-indicators',
                      icon: <CalculatorOutlined />,
                      label: isIndicatorsUpdating ? '重算进行中...' : '指标重算',
                      disabled: loading || sectorsRefreshTask.isTaskRunning || isIndicatorsUpdating || isBatchUpdatingIndicators,
                      onClick: handleUpdateIndicators,
                    },
                    {
                      key: 'batch-update-indicators',
                      icon: <BarChartOutlined />,
                      label: isBatchUpdatingIndicators ? '批量更新中...' : '批量更新指标',
                      disabled: loading || sectorsRefreshTask.isTaskRunning || isIndicatorsUpdating || isBatchUpdatingIndicators,
                      onClick: handleBatchUpdateIndicators,
                    },
                    {
                      type: 'divider',
                    },
                    {
                      key: 'help',
                      icon: <BulbOutlined />,
                      label: '功能说明',
                      onClick: () => showFunctionHelp(),
                    },
                  ],
                }}
                trigger={['click']}
                placement="bottomRight"
              >
                <Button
                  icon={<MoreOutlined />}
                  disabled={loading}
                  style={{
                    borderColor: colorScheme.outline,
                    color: colorScheme.onSurface,
                  }}
                />
              </Dropdown>
            </Space.Compact>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Space align="center">
              <Text style={{ color: colorScheme.onSurfaceVariant }}>自动刷新</Text>
              <Switch
                checked={autoRefresh}
                onChange={setAutoRefresh}
                size="small"
              />
              {lastUpdateTime && (
                <Tooltip title="最后更新时间">
                  <Text type="secondary" style={{ fontSize: '12px' }}>
                    <ClockCircleOutlined /> {lastUpdateTime}
                  </Text>
                </Tooltip>
              )}
            </Space>
          </Col>
        </Row>

        {/* 任务进度显示 */}
        {(sectorsRefreshTask.isTaskRunning || isIndicatorsUpdating) && (
          <Row style={{ marginTop: 16 }}>
            <Col span={24}>
              <Card
                size="small"
                style={{
                  backgroundColor: colorScheme.surfaceContainer,
                  borderColor: colorScheme.primary,
                  borderWidth: 1,
                }}
              >
                <Space direction="vertical" style={{ width: '100%' }}>
                  {sectorsRefreshTask.isTaskRunning && (
                    <div>
                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 8 }}>
                        <Text strong style={{ color: colorScheme.primary }}>
                          <SyncOutlined spin /> 板块数据刷新进行中
                        </Text>
                        <Text style={{ color: colorScheme.onSurfaceVariant }}>
                          {sectorsRefreshTask.progress}%
                        </Text>
                      </div>
                      <Progress
                        percent={sectorsRefreshTask.progress}
                        status="active"
                        strokeColor={colorScheme.primary}
                        size="small"
                      />
                      {sectorsRefreshTask.currentStep && (
                        <Text type="secondary" style={{ fontSize: '12px' }}>
                          {sectorsRefreshTask.currentStep}
                        </Text>
                      )}
                    </div>
                  )}

                  {isIndicatorsUpdating && (
                    <div>
                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 8 }}>
                        <Text strong style={{ color: colorScheme.primary }}>
                          <CalculatorOutlined spin /> 技术指标同步更新进行中
                        </Text>
                        <Text style={{ color: colorScheme.onSurfaceVariant }}>
                          处理中...
                        </Text>
                      </div>
                      <Progress
                        percent={100}
                        status="active"
                        strokeColor={colorScheme.primary}
                        size="small"
                        showInfo={false}
                      />
                      <Text type="secondary" style={{ fontSize: '12px' }}>
                        正在同步处理86个板块的技术指标，预计耗时30-60秒
                      </Text>
                    </div>
                  )}
                </Space>
              </Card>
            </Col>
          </Row>
        )}
      </Card>

      {/* 主要内容区域 - 板块数据表格 */}
      <Card
        title={
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', flexWrap: 'wrap', gap: '8px' }}>
            <span style={{ color: colorScheme.onSurface, fontSize: '16px', fontWeight: 500 }}>
              板块数据 ({filteredSectors.length}/{sectors.length})
            </span>
            {dataUpdateTime && (
              <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
                <ClockCircleOutlined style={{
                  color: colorScheme.onSurfaceVariant,
                  fontSize: '12px'
                }} />
                <Text style={{
                  color: colorScheme.onSurfaceVariant,
                  fontSize: '12px',
                  fontFamily: 'monospace'
                }}>
                  最后更新：{dataUpdateTime}
                </Text>
              </div>
            )}
          </div>
        }
        size="small"
        className="md3-card"
        style={{
          backgroundColor: colorScheme.surfaceContainerLow,
          borderColor: colorScheme.outlineVariant,
        }}
      >
        <Table
          columns={columns}
          dataSource={filteredSectors}
          rowKey={(record) => record.板块代码}
          size="small"
          loading={loading}
          pagination={false}
          style={{
            backgroundColor: 'transparent',
          }}
        />
      </Card>
    </div>
  )
}

export default AllSectors
