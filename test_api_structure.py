#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json

def test_api_structure():
    """测试86个板块API数据结构"""
    try:
        print('测试86个板块API数据结构...')
        response = requests.get('http://127.0.0.1:5000/api/sectors/all-realtime', timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success') and data.get('data'):
                sectors = data['data']
                print(f'获取到{len(sectors)}个板块数据')
                
                # 检查前3个板块的领涨股票字段
                print('\n前3个板块的领涨股票信息:')
                for i, sector in enumerate(sectors[:3]):
                    print(f'板块{i+1}: {sector.get("板块名称", "")}')
                    print(f'  领涨股票: {sector.get("领涨股票", "")}')
                    print(f'  领涨股票代码: {sector.get("领涨股票代码", "")}')
                    print(f'  领涨股票涨跌幅: {sector.get("领涨股票-涨跌幅", 0)}%')
                    print(f'  领涨股票价格: {sector.get("领涨股票价格", 0)}')
                    print()
                    
                # 检查数据结构中是否有多只股票信息
                sample_sector = sectors[0]
                print('样本板块的所有字段:')
                for key, value in sample_sector.items():
                    if '领涨' in key or '股票' in key:
                        print(f'  {key}: {value}')
                        
                # 检查是否有前三名股票的字段
                print('\n检查是否有前三名股票字段:')
                has_multiple_stocks = False
                for key in sample_sector.keys():
                    if any(x in key for x in ['前三', '前3', '第二', '第三', '领涨股票2', '领涨股票3']):
                        print(f'  发现多股票字段: {key}')
                        has_multiple_stocks = True
                        
                if not has_multiple_stocks:
                    print('  未发现前三名股票字段，API只返回单一领涨股票')
                    
            else:
                print('API返回数据格式错误')
        else:
            print(f'API请求失败，状态码: {response.status_code}')
            
    except Exception as e:
        print(f'测试失败: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_api_structure()
