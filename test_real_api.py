#!/usr/bin/env python3
"""
测试真实的概念N型待选股票API
"""
import requests
import json

def test_real_api():
    # 测试真实的概念N型待选股票API
    url = 'http://localhost:5000/api/concept-n-stocks/机器人'

    try:
        print('🚀 测试真实的概念N型待选股票API...')
        response = requests.get(url, timeout=60)
        print(f'状态码: {response.status_code}')
        if response.status_code == 200:
            result = response.json()
            print('✅ API响应成功!')
            print(f'📊 概念: {result.get("concept_name", "未知")}')
            print(f'📈 股票数量: {result.get("total", 0)}')
            print(f'⏱️ 查询耗时: {result.get("query_time_ms", 0)}ms')
            
            # 检查是否有实时数据字段
            if result.get('data'):
                first_stock = result['data'][0]
                has_change = '涨跌幅' in first_stock
                has_price = '最新价' in first_stock
                
                print(f'🔍 检查实时数据字段:')
                print(f'  涨跌幅字段: {"✅" if has_change else "❌"}')
                print(f'  最新价字段: {"✅" if has_price else "❌"}')
                
                if has_change and has_price:
                    print('\n📋 前3只股票的实时数据:')
                    for i, stock in enumerate(result['data'][:3]):
                        change = stock.get('涨跌幅')
                        price = stock.get('最新价')
                        code = stock.get('stock_code', '')
                        name = stock.get('stock_name', '')
                        
                        if change is not None and price is not None:
                            print(f'  {i+1}. {code} ({name}): {change:+.2f}% | ¥{price:.2f}')
                        else:
                            print(f'  {i+1}. {code} ({name}): 实时数据为空')
                            
                    print('\n🎉 修复成功！概念N型待选股票API现在包含实时涨跌幅数据')
                else:
                    print('\n⚠️ API响应中缺少实时数据字段，修复可能未生效')
                    print('📋 第一只股票的字段:')
                    for key in first_stock.keys():
                        print(f'  - {key}')
            else:
                print('\n❌ API响应中没有股票数据')
        else:
            print(f'❌ API调用失败: {response.status_code}')
            print(f'错误: {response.text}')
    except Exception as e:
        print(f'❌ 请求失败: {e}')

if __name__ == "__main__":
    test_real_api()
