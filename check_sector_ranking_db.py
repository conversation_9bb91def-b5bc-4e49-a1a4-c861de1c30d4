#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查板块排名数据库中的数据
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from datetime import date
from models.sector_ranking import SectorDailyRanking
from database import db
from app import create_app

def check_sector_ranking_db():
    """检查板块排名数据库中的数据"""
    print("=" * 80)
    print("检查板块排名数据库中的数据")
    print("=" * 80)
    
    # 创建应用上下文
    app = create_app()
    
    with app.app_context():
        try:
            test_date = date(2025, 7, 11)
            
            print(f"查询日期: {test_date}")
            print()
            
            # 查询数据库中的数据
            rankings = SectorDailyRanking.query.filter_by(ranking_date=test_date).order_by(SectorDailyRanking.ranking).all()
            
            if rankings:
                print(f"✅ 找到 {len(rankings)} 条记录")
                print()
                
                for ranking in rankings:
                    print(f"记录 {ranking.ranking}: {ranking.sector_name} ({ranking.sector_code})")
                    print(f"  涨跌幅: {ranking.price_change_pct:.2f}%")
                    print(f"  领涨股票名称: '{ranking.leading_stock_name}'")
                    print(f"  领涨股票代码: '{ranking.leading_stock_code}'")
                    print(f"  领涨股票价格: {ranking.leading_stock_price}")
                    print(f"  领涨股票涨跌幅: {ranking.leading_stock_change_pct}")
                    print(f"  创建时间: {ranking.created_at}")
                    print(f"  更新时间: {ranking.updated_at}")
                    print()
                    
                    # 检查是否为占位符
                    if ranking.leading_stock_name and '_领涨股' in ranking.leading_stock_name:
                        print(f"  ⚠️  这是占位符数据！")
                    elif ranking.leading_stock_name and ranking.leading_stock_name.strip():
                        print(f"  ✅ 这是真实的股票名称")
                    else:
                        print(f"  ❌ 领涨股票名称为空")
                    print("-" * 60)
            else:
                print(f"❌ 未找到 {test_date} 的数据")
                
                # 查看最近的数据
                print("\n查看最近的数据...")
                recent_rankings = SectorDailyRanking.query.order_by(SectorDailyRanking.created_at.desc()).limit(5).all()
                
                if recent_rankings:
                    print(f"最近的 {len(recent_rankings)} 条记录:")
                    for ranking in recent_rankings:
                        print(f"  {ranking.ranking_date}: {ranking.sector_name} - {ranking.leading_stock_name}")
                else:
                    print("数据库中没有任何记录")
                    
        except Exception as e:
            print(f"❌ 查询失败: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    check_sector_ranking_db()
