#!/usr/bin/env python3
"""
测试灵活技术分析生成策略
验证优化后的系统能够根据数据量灵活生成技术指标
"""

import sys
import os

# 添加backend目录到Python路径
backend_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'backend')
sys.path.insert(0, backend_dir)

from app import create_app
from database import db
from models import Sector, DailyQuote, TechnicalAnalysis
from services.database_service import database_service
from datetime import datetime, timedelta
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_flexible_vs_original_strategy():
    """对比测试灵活策略和原始策略"""
    print("=" * 80)
    print("🧪 测试灵活技术分析生成策略 vs 原始策略")
    print("=" * 80)
    
    app = create_app('local')
    with app.app_context():
        # 测试BK0440板块
        sector_code = 'BK0440'
        sector = Sector.query.filter_by(sector_code=sector_code).first()
        
        if not sector:
            print(f"❌ 未找到板块 {sector_code}")
            return
        
        print(f"📋 测试板块: {sector.sector_name} ({sector.sector_code})")
        
        # 获取当前数据状态
        quotes_count = DailyQuote.query.filter_by(sector_id=sector.id).count()
        print(f"📊 历史行情数据: {quotes_count}条")
        
        # 清除现有的技术分析数据
        print(f"\n🗑️ 清除现有技术分析数据...")
        TechnicalAnalysis.query.filter_by(sector_id=sector.id).delete()
        db.session.commit()
        
        # 测试1: 原始策略（20天起）
        print(f"\n🔄 测试1: 原始策略（需要20天数据）")
        try:
            success_original = database_service.generate_historical_analysis(
                sector_code, quotes_count, flexible=False
            )
            
            if success_original:
                original_count = TechnicalAnalysis.query.filter_by(sector_id=sector.id).count()
                print(f"✅ 原始策略成功生成 {original_count}条技术分析数据")
                
                # 显示样本数据
                analyses = TechnicalAnalysis.query.filter_by(sector_id=sector.id)\
                                                .order_by(TechnicalAnalysis.analysis_date.desc())\
                                                .limit(3).all()
                print(f"   样本数据:")
                for analysis in analyses:
                    ma_info = []
                    if analysis.ma5: ma_info.append(f"MA5:{analysis.ma5:.2f}")
                    if analysis.ma10: ma_info.append(f"MA10:{analysis.ma10:.2f}")
                    if analysis.ma20: ma_info.append(f"MA20:{analysis.ma20:.2f}")
                    if analysis.ma60: ma_info.append(f"MA60:{analysis.ma60:.2f}")
                    
                    ma_str = " | ".join(ma_info) if ma_info else "无移动平均线数据"
                    print(f"     {analysis.analysis_date} | {ma_str}")
            else:
                original_count = 0
                print(f"❌ 原始策略失败（数据不足20天）")
        except Exception as e:
            original_count = 0
            print(f"❌ 原始策略异常: {e}")
        
        # 清除数据准备下一个测试
        TechnicalAnalysis.query.filter_by(sector_id=sector.id).delete()
        db.session.commit()
        
        # 测试2: 灵活策略（3天起）
        print(f"\n🚀 测试2: 灵活策略（需要3天数据）")
        try:
            success_flexible = database_service.generate_historical_analysis(
                sector_code, quotes_count, flexible=True
            )
            
            if success_flexible:
                flexible_count = TechnicalAnalysis.query.filter_by(sector_id=sector.id).count()
                print(f"✅ 灵活策略成功生成 {flexible_count}条技术分析数据")
                
                # 分析数据完整性
                analyses = TechnicalAnalysis.query.filter_by(sector_id=sector.id)\
                                                .order_by(TechnicalAnalysis.analysis_date.asc())\
                                                .all()
                
                ma5_count = sum(1 for a in analyses if a.ma5 is not None)
                ma10_count = sum(1 for a in analyses if a.ma10 is not None)
                ma20_count = sum(1 for a in analyses if a.ma20 is not None)
                ma60_count = sum(1 for a in analyses if a.ma60 is not None)
                
                print(f"   数据完整性分析:")
                print(f"     MA5可用: {ma5_count}条 ({ma5_count/flexible_count*100:.1f}%)")
                print(f"     MA10可用: {ma10_count}条 ({ma10_count/flexible_count*100:.1f}%)")
                print(f"     MA20可用: {ma20_count}条 ({ma20_count/flexible_count*100:.1f}%)")
                print(f"     MA60可用: {ma60_count}条 ({ma60_count/flexible_count*100:.1f}%)")
                
                # 显示早期数据样本（数据量少时的表现）
                print(f"   早期数据样本（数据量较少时）:")
                for i, analysis in enumerate(analyses[:5]):
                    ma_info = []
                    if analysis.ma5: ma_info.append(f"MA5:{analysis.ma5:.2f}")
                    if analysis.ma10: ma_info.append(f"MA10:{analysis.ma10:.2f}")
                    if analysis.ma20: ma_info.append(f"MA20:{analysis.ma20:.2f}")
                    if analysis.ma60: ma_info.append(f"MA60:{analysis.ma60:.2f}")
                    
                    ma_str = " | ".join(ma_info) if ma_info else "仅基础分析"
                    data_count = i + 3  # 从第3天开始
                    print(f"     第{data_count}天 {analysis.analysis_date} | {ma_str}")
                
                # 显示最新数据样本（数据量充足时的表现）
                print(f"   最新数据样本（数据量充足时）:")
                for analysis in analyses[-3:]:
                    ma_info = []
                    if analysis.ma5: ma_info.append(f"MA5:{analysis.ma5:.2f}")
                    if analysis.ma10: ma_info.append(f"MA10:{analysis.ma10:.2f}")
                    if analysis.ma20: ma_info.append(f"MA20:{analysis.ma20:.2f}")
                    if analysis.ma60: ma_info.append(f"MA60:{analysis.ma60:.2f}")
                    
                    ma_str = " | ".join(ma_info) if ma_info else "无移动平均线数据"
                    print(f"     {analysis.analysis_date} | {ma_str}")
            else:
                flexible_count = 0
                print(f"❌ 灵活策略失败")
        except Exception as e:
            flexible_count = 0
            print(f"❌ 灵活策略异常: {e}")
        
        # 对比结果
        print(f"\n📊 策略对比结果:")
        print(f"   原始策略（20天起）: {original_count}条技术分析数据")
        print(f"   灵活策略（3天起）:  {flexible_count}条技术分析数据")
        
        if flexible_count > original_count:
            improvement = flexible_count - original_count
            improvement_pct = (improvement / max(original_count, 1)) * 100
            print(f"   🎉 灵活策略增加了 {improvement}条数据 (+{improvement_pct:.1f}%)")
            print(f"   ✅ 用户体验显著提升：更早看到技术分析结果")
        elif flexible_count == original_count:
            print(f"   📊 两种策略生成的数据量相同")
        else:
            print(f"   ⚠️ 灵活策略数据量少于原始策略（可能存在问题）")
        
        # 用户体验分析
        print(f"\n💡 用户体验改善分析:")
        if quotes_count >= 20:
            early_benefit = flexible_count - original_count
            print(f"   - 用户可以提前 {early_benefit}天看到技术分析结果")
            print(f"   - 在数据积累过程中，用户能看到逐步完善的技术指标")
            print(f"   - MA5从第5天开始可用，MA10从第10天开始可用")
        else:
            print(f"   - 在数据不足20天时，原始策略无法提供任何技术分析")
            print(f"   - 灵活策略仍能提供 {flexible_count}条基础技术分析")
            print(f"   - 大幅提升了数据稀少情况下的用户体验")

def test_update_sector_analysis_flexible():
    """测试单个板块更新的灵活策略"""
    print(f"\n" + "=" * 80)
    print("🔄 测试单个板块更新的灵活策略")
    print("=" * 80)
    
    app = create_app('local')
    with app.app_context():
        sector_code = 'BK0440'
        
        print(f"📋 测试板块: {sector_code}")
        
        # 测试灵活更新
        print(f"\n🚀 执行灵活更新...")
        success = database_service.update_sector_analysis(
            sector_code, 
            generate_historical=True, 
            flexible=True
        )
        
        if success:
            print(f"✅ 灵活更新成功")
            
            # 检查结果
            sector = Sector.query.filter_by(sector_code=sector_code).first()
            if sector:
                analysis_count = TechnicalAnalysis.query.filter_by(sector_id=sector.id).count()
                print(f"📊 生成的技术分析数据: {analysis_count}条")
                
                # 显示最新分析
                latest_analysis = TechnicalAnalysis.query.filter_by(sector_id=sector.id)\
                                                        .order_by(TechnicalAnalysis.analysis_date.desc())\
                                                        .first()
                if latest_analysis:
                    print(f"📈 最新技术分析 ({latest_analysis.analysis_date}):")
                    ma5_str = f"{latest_analysis.ma5:.2f}" if latest_analysis.ma5 is not None else 'N/A'
                    ma10_str = f"{latest_analysis.ma10:.2f}" if latest_analysis.ma10 is not None else 'N/A'
                    ma20_str = f"{latest_analysis.ma20:.2f}" if latest_analysis.ma20 is not None else 'N/A'
                    ma60_str = f"{latest_analysis.ma60:.2f}" if latest_analysis.ma60 is not None else 'N/A'

                    print(f"   MA5: {ma5_str}")
                    print(f"   MA10: {ma10_str}")
                    print(f"   MA20: {ma20_str}")
                    print(f"   MA60: {ma60_str}")
                    print(f"   趋势: {latest_analysis.trend_direction}")
        else:
            print(f"❌ 灵活更新失败")

def main():
    """主函数"""
    print("🔧 灵活技术分析生成策略测试")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 1. 对比测试灵活策略和原始策略
    test_flexible_vs_original_strategy()
    
    # 2. 测试单个板块更新的灵活策略
    test_update_sector_analysis_flexible()
    
    print(f"\n" + "=" * 80)
    print("🎉 灵活技术分析策略测试完成")
    print("=" * 80)
    
    print(f"\n💡 优化总结:")
    print(f"   ✅ 降低了技术分析生成的数据要求（从20天降到3天）")
    print(f"   ✅ 根据数据量灵活生成不同的技术指标")
    print(f"   ✅ 显著提升了用户体验，特别是在数据稀少时")
    print(f"   ✅ 保持了向后兼容性，支持原始策略")
    print(f"   ✅ 实现了渐进式技术分析，随数据增加而完善")

if __name__ == "__main__":
    main()
