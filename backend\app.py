from flask import Flask, jsonify, request, g
from flask_cors import CORS
import os
import sys
import logging
import time
from datetime import datetime
from dotenv import load_dotenv
from config import config
from database import db, migrate

# 加载环境变量
load_dotenv()

# 配置详细的日志系统
def setup_logging():
    """设置详细的日志配置"""
    # 创建根日志记录器
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.DEBUG)

    # 清除现有的处理器
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)

    # 创建控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.INFO)

    # 创建详细的日志格式
    detailed_formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )

    # 创建简洁的请求日志格式
    request_formatter = logging.Formatter(
        '%(asctime)s - %(message)s',
        datefmt='%H:%M:%S'
    )

    console_handler.setFormatter(detailed_formatter)
    root_logger.addHandler(console_handler)

    # 设置Flask相关日志级别
    logging.getLogger('werkzeug').setLevel(logging.INFO)
    logging.getLogger('flask').setLevel(logging.INFO)

    return root_logger

# 初始化日志
logger = setup_logging()

def create_app(config_name='local'):
    """应用工厂函数"""
    app = Flask(__name__)

    # 加载配置
    config_class = config[config_name]
    if config_name == 'production':
        # 对于生产环境，需要实例化配置类以触发检查
        app.config.from_object(config_class())
    else:
        app.config.from_object(config_class)

    # 配置CORS
    cors_origins = getattr(app.config, 'CORS_ORIGINS', ["*"])
    CORS(app, origins=cors_origins)

    # 初始化数据库
    db.init_app(app)
    migrate.init_app(app, db)

    # 导入模型（确保表能够被创建）
    from models import Sector, DailyQuote, TechnicalAnalysis, AsyncTask, TaskLog, SectorDailyRanking

    # 添加请求日志中间件
    setup_request_logging(app)

    # 添加异步任务日志增强
    setup_async_task_logging(app)

    # 注册API蓝图
    from api.routes import api_bp
    from api.async_routes import async_api_bp
    app.register_blueprint(api_bp)
    app.register_blueprint(async_api_bp)

    return app

def setup_async_task_logging(app):
    """设置异步任务相关的日志增强"""
    task_logger = logging.getLogger('async_task')

    # 在应用上下文中添加任务日志记录器
    @app.before_request
    def setup_task_logging():
        """为异步任务相关的请求设置特殊日志"""
        # 记录所有API请求
        if request.path.startswith('/api/'):
            logger.info(f"📡 API请求: {request.method} {request.path}")
            if request.is_json and request.json:
                logger.debug(f"📝 请求数据: {request.json}")

        if '/api/tasks/' in request.path:
            g.is_task_request = True
            g.task_logger = task_logger
        else:
            g.is_task_request = False

    # 添加任务操作的详细日志
    @app.after_request
    def log_task_operations(response):
        """记录异步任务操作的详细信息"""
        # 记录所有API响应
        if request.path.startswith('/api/'):
            logger.info(f"📤 API响应: {request.method} {request.path} - {response.status_code}")
            if response.status_code >= 400:
                logger.error(f"❌ API错误响应: {response.status_code} - {request.path}")

        if not g.get('is_task_request', False):
            return response

        try:
            # 解析响应数据
            if response.is_json and response.status_code in [200, 202]:
                data = response.get_json()

                if request.path.endswith('/refresh') and request.method == 'POST':
                    # 任务创建日志
                    if data and data.get('success'):
                        task_logger.info(
                            f"🚀 异步任务创建成功 | "
                            f"任务ID: {data.get('task_id', 'N/A')} | "
                            f"任务名称: {data.get('task_name', 'N/A')} | "
                            f"预计时长: {data.get('estimated_duration', 'N/A')}"
                        )
                    else:
                        task_logger.error(
                            f"❌ 异步任务创建失败 | "
                            f"错误: {data.get('error', '未知错误') if data else '无响应数据'}"
                        )

                elif '/status' in request.path and request.method == 'GET':
                    # 任务状态查询日志
                    if data and data.get('success'):
                        status_info = data
                        progress = status_info.get('progress', 0)
                        current_step = status_info.get('current_step', '')
                        is_finished = status_info.get('is_finished', False)
                        is_successful = status_info.get('is_successful', False)

                        if is_finished:
                            if is_successful:
                                task_logger.info(
                                    f"✅ 任务完成 | "
                                    f"任务ID: {status_info.get('task_id', 'N/A')} | "
                                    f"耗时: {status_info.get('duration', 'N/A')}秒"
                                )
                            else:
                                task_logger.error(
                                    f"❌ 任务失败 | "
                                    f"任务ID: {status_info.get('task_id', 'N/A')} | "
                                    f"错误: {status_info.get('error_message', '未知错误')}"
                                )
                        else:
                            task_logger.info(
                                f"🔄 任务进行中 | "
                                f"进度: {progress}% | "
                                f"当前步骤: {current_step or '准备中'}"
                            )

                elif '/list' in request.path and request.method == 'GET':
                    # 任务列表查询日志
                    if data and data.get('success'):
                        total_count = data.get('total_count', 0)
                        running_count = data.get('running_count', 0)
                        task_logger.info(
                            f"📋 任务列表查询 | "
                            f"总任务: {total_count} | "
                            f"运行中: {running_count}"
                        )

        except Exception as e:
            task_logger.warning(f"⚠️ 任务日志记录异常: {e}")

        return response

def setup_request_logging(app):
    """设置请求和响应日志"""
    request_logger = logging.getLogger('request')

    @app.before_request
    def log_request_info():
        """记录请求信息"""
        g.start_time = time.time()

        # 跳过静态文件的日志
        if request.path.startswith('/static'):
            return

        # 记录请求信息
        request_data = ""
        if request.is_json and request.get_json():
            try:
                json_data = request.get_json()
                # 隐藏敏感信息
                if isinstance(json_data, dict):
                    safe_data = {k: v for k, v in json_data.items() if 'password' not in k.lower()}
                    request_data = f" | 数据: {safe_data}"
            except:
                request_data = " | 数据: [无法解析]"
        elif request.form:
            request_data = f" | 表单: {dict(request.form)}"
        elif request.args:
            request_data = f" | 参数: {dict(request.args)}"

        request_logger.info(
            f"🔵 {request.method} {request.path} | "
            f"来源: {request.remote_addr} | "
            f"用户代理: {request.headers.get('User-Agent', 'Unknown')[:50]}...{request_data}"
        )

    @app.after_request
    def log_response_info(response):
        """记录响应信息"""
        # 跳过静态文件的日志
        if request.path.startswith('/static'):
            return response

        # 计算响应时间
        duration = round((time.time() - g.get('start_time', time.time())) * 1000, 2)

        # 确定状态颜色
        if response.status_code < 300:
            status_icon = "✅"
        elif response.status_code < 400:
            status_icon = "🔄"
        elif response.status_code < 500:
            status_icon = "⚠️"
        else:
            status_icon = "❌"

        # 记录响应信息
        request_logger.info(
            f"{status_icon} {response.status_code} {request.method} {request.path} | "
            f"耗时: {duration}ms | "
            f"大小: {response.content_length or len(response.get_data())}字节"
        )

        return response

    @app.errorhandler(Exception)
    def log_exception(error):
        """记录异常信息"""
        error_logger = logging.getLogger('error')
        error_logger.error(
            f"💥 异常发生在 {request.method} {request.path} | "
            f"错误: {str(error)} | "
            f"类型: {type(error).__name__}",
            exc_info=True
        )

        # 返回JSON格式的错误响应
        return jsonify({
            'success': False,
            'error': '服务器内部错误',
            'message': str(error) if app.debug else '请稍后重试',
            'timestamp': datetime.now().isoformat()
        }), 500

# 创建应用实例
config_name = os.getenv('FLASK_CONFIG', 'local')
app = create_app(config_name)

# Celery初始化（仅在需要时）
celery = None
if app.config.get('CELERY_BROKER_URL'):
    from celery import Celery
    
    def make_celery(app):
        celery = Celery(
            app.import_name,
            backend=app.config['CELERY_RESULT_BACKEND'],
            broker=app.config['CELERY_BROKER_URL']
        )
        
        class ContextTask(celery.Task):
            """Make celery tasks work with Flask app context."""
            def __call__(self, *args, **kwargs):
                with app.app_context():
                    return self.run(*args, **kwargs)
        
        celery.Task = ContextTask
        return celery
    
    celery = make_celery(app)

# 根路径路由
@app.route('/')
def index():
    """API根路径，提供服务信息"""
    return jsonify({
        'name': 'Stock Analysis API',
        'version': '1.0.0',
        'status': 'running',
        'config': config_name,
        'endpoints': {
            'health': '/health',
            'api_test': '/api/test',
            'api_docs': '/api/docs (开发中)',
            'api_base': '/api/v1 (开发中)'
        },
        'message': '股票分析系统后端API服务'
    })

# 健康检查接口
@app.route('/health')
def health_check():
    """系统健康检查接口"""
    return jsonify({
        'status': 'healthy',
        'message': 'Stock Analysis API is running',
        'config': config_name
    })

# API路由
@app.route('/api/test')
def api_test():
    """API测试接口"""
    return jsonify({
        'message': 'API is working',
        'version': '1.0.0',
        'database': 'Connected' if db else 'Not connected'
    })

# API文档路由
@app.route('/api/docs')
def api_docs():
    """API文档接口"""
    return jsonify({
        'title': 'Stock Analysis API Documentation',
        'version': '1.0.0',
        'base_url': f"http://{app.config.get('API_HOST', '127.0.0.1')}:{app.config.get('API_PORT', 5000)}",
        'endpoints': [
            {
                'path': '/',
                'method': 'GET',
                'description': 'API服务信息'
            },
            {
                'path': '/health',
                'method': 'GET',
                'description': '健康检查'
            },
            {
                'path': '/api/test',
                'method': 'GET',
                'description': 'API测试接口'
            },
            {
                'path': '/api/docs',
                'method': 'GET',
                'description': 'API文档'
            }
        ],
        'models': [
            {
                'name': 'Sector',
                'description': '申万行业板块模型'
            },
            {
                'name': 'DailyQuote', 
                'description': '日行情数据模型'
            },
            {
                'name': 'TechnicalAnalysis',
                'description': '技术分析结果模型'
            }
        ]
    })

if __name__ == '__main__':
    # 显示启动信息
    print("=" * 60)
    print("🚀 股票分析后端服务启动中...")
    print("=" * 60)
    print(f"启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()

    # 创建数据库表
    with app.app_context():
        try:
            db.create_all()
            logger.info("✅ 数据库表创建成功")
        except Exception as e:
            logger.warning(f"⚠️ 数据库初始化警告: {e}")

    # 启动定时任务（仅在非调试模式下）
    if not app.config.get('DEBUG', False):
        try:
            from services.scheduler_service import start_scheduler
            start_scheduler()
            logger.info("✅ 定时任务服务已启动")
        except Exception as e:
            logger.warning(f"⚠️ 定时任务启动警告: {e}")

    # 启动应用
    host = getattr(app.config, 'API_HOST', '127.0.0.1')
    port = getattr(app.config, 'API_PORT', 5000)
    debug = app.config.get('DEBUG', False)

    print(f"� 服务地址: http://{host}:{port}")
    print(f"🔧 调试模式: {'开启' if debug else '关闭'}")
    print(f"⚙️ 配置环境: {config_name}")
    print(f"📝 日志级别: {'DEBUG' if debug else 'INFO'}")
    print()
    print("=" * 60)
    print("📋 API端点列表:")
    print("=" * 60)
    print("基础端点:")
    print("  GET  /              - 服务信息")
    print("  GET  /health        - 健康检查")
    print("  GET  /api/test      - API测试")
    print()
    print("板块数据端点:")
    print("  GET  /api/sectors                    - 获取所有板块")
    print("  GET  /api/sectors/all-realtime       - 获取实时数据")
    print("  POST /api/sectors/refresh            - 刷新板块数据")
    print()
    print("异步任务端点:")
    print("  POST /api/tasks/sectors/refresh      - 创建板块刷新任务")
    print("  GET  /api/tasks/<id>/status          - 查询任务状态")
    print("  GET  /api/tasks/<id>/result          - 获取任务结果")
    print("  GET  /api/tasks/list                 - 任务列表")
    print("  GET  /api/tasks/health               - 数据库健康检查")
    print()
    print("=" * 60)
    print("🔍 实时日志监控已启用")
    print("=" * 60)
    print()

    try:
        # 设置Flask应用的日志级别
        if debug:
            app.logger.setLevel(logging.DEBUG)
            logging.getLogger('werkzeug').setLevel(logging.INFO)
        else:
            app.logger.setLevel(logging.INFO)
            logging.getLogger('werkzeug').setLevel(logging.WARNING)

        logger.info(f"🌟 Flask服务器启动 - {host}:{port}")
        app.run(host=host, port=port, debug=debug, use_reloader=False)

    except KeyboardInterrupt:
        logger.info("🛑 服务器被用户中断")
    except Exception as e:
        logger.error(f"💥 服务器启动失败: {e}", exc_info=True)
    finally:
        # 应用关闭时停止定时任务
        if not debug:
            try:
                from services.scheduler_service import stop_scheduler
                stop_scheduler()
                logger.info("✅ 定时任务服务已停止")
            except Exception as e:
                logger.warning(f"⚠️ 定时任务停止警告: {e}")

        logger.info("👋 股票分析后端服务已关闭")