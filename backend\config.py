import os
from dotenv import load_dotenv

load_dotenv()

class Config:
    """基础配置类"""
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'your-secret-key-here'

    # 数据库配置
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or \
        'postgresql://stock_user:stock_password@localhost:5432/stock_analysis'
    SQLALCHEMY_TRACK_MODIFICATIONS = False

    # MySQL缓存数据库配置 - 长时间操作优化版本
    MYSQL_CACHE_CONFIG = {
        'host': 'stock-31bk-stock-31bankuai.c.aivencloud.com',
        'port': 26991,
        'user': 'avnadmin',
        'password': 'AVNS_daq_tCJ6LP2VwbS_633',
        'database': 'defaultdb',
        'charset': 'utf8mb4',
        'ssl_disabled': False,
        'ssl_verify_cert': False,  # 优化：禁用SSL证书验证以加快连接
        'ssl_verify_identity': False,  # 优化：禁用SSL身份验证以加快连接
        'autocommit': True,
        'pool_size': 3,            # 增加：支持更多并发连接
        'max_overflow': 8,        # 增加：支持长时间操作的连接溢出
        'pool_timeout': 5,        # 增加：支持长时间等待
        'pool_recycle': 3600,      # 增加：2小时回收，支持长时间操作
        'pool_pre_ping': True,     # 启用：预ping以检测失效连接
        # 长时间操作超时优化参数
        'connect_timeout': 10,     # 增加：支持慢速连接
        'read_timeout': 60,       # 增加：支持长时间读取操作（5分钟）
        'write_timeout': 60,      # 增加：支持长时间写入操作（5分钟）
        # 新增：长时间操作支持（仅用于init_command，不直接传递给连接）
        'session_wait_timeout': 28800,     # 8小时会话超时
        'session_interactive_timeout': 28800,  # 8小时交互超时
        'session_innodb_lock_wait_timeout': 120  # 2分钟锁等待超时
    }

    # Redis配置
    REDIS_URL = os.environ.get('REDIS_URL') or 'redis://localhost:6379/0'

    # Celery配置
    CELERY_BROKER_URL = REDIS_URL
    CELERY_RESULT_BACKEND = REDIS_URL

    # AKshare配置
    AKSHARE_TIMEOUT = 30
    AKSHARE_RETRY_COUNT = 3

    # 数据更新配置
    UPDATE_INTERVAL = 300  # 5分钟更新一次
    CACHE_TIMEOUT = 900    # 15分钟缓存过期

    # 数据缓存配置 - 智能TTL策略
    CACHE_STRATEGY = {
        'stock_basic_info': {'ttl': 86400, 'table': 'stock_basic_cache'},  # 24小时
        'realtime_quotes': {'ttl': 300, 'table': 'realtime_quotes_cache'},  # 5分钟
        'historical_data': {'ttl': 86400 * 30, 'table': 'historical_data_cache'},  # 30天 - 历史数据长期保存
        'sector_data': {'ttl': 1800, 'table': 'sector_data_cache'},  # 30分钟 - 智能更新：交易时间内快速过期
        'financial_data': {'ttl': 86400 * 7, 'table': 'financial_data_cache'},  # 7天 - 财务数据长期保存
        'sector_calendar': {'ttl': 86400, 'table': 'sector_calendar_cache'},  # 24小时 - 板块日历数据
    }

    # API配置
    API_RATE_LIMIT = "100/hour"

    # 日志配置
    LOG_LEVEL = os.environ.get('LOG_LEVEL') or 'INFO'

class DevelopmentConfig(Config):
    """开发环境配置"""
    DEBUG = True
    FLASK_ENV = 'development'

class LocalConfig(Config):
    """本地虚拟环境配置"""
    DEBUG = True
    FLASK_ENV = 'development'

    # 本地环境也使用MySQL数据库 - 统一数据库架构
    # 构建MySQL连接URL，只包含PyMySQL支持的参数
    mysql_config = Config.MYSQL_CACHE_CONFIG
    SQLALCHEMY_DATABASE_URI = (
        f"mysql+pymysql://{mysql_config['user']}:{mysql_config['password']}"
        f"@{mysql_config['host']}:{mysql_config['port']}/{mysql_config['database']}"
        f"?charset={mysql_config['charset']}"
        f"&ssl_verify_cert=false&ssl_verify_identity=false"
    )

    # Flask-SQLAlchemy引擎配置
    SQLALCHEMY_ENGINE_OPTIONS = {
        'pool_size': 5,
        'max_overflow': 10,
        'pool_timeout': 10,
        'pool_recycle': 3600,
        'pool_pre_ping': True,
        'connect_args': {
            'connect_timeout': 10,
            'read_timeout': 60,
            'write_timeout': 60,
            'charset': 'utf8mb4',
            'init_command': 'SET SESSION wait_timeout=28800'
        }
    }

    # 禁用Redis和Celery（本地环境）
    REDIS_URL = None
    CELERY_BROKER_URL = None
    CELERY_RESULT_BACKEND = None

    # 本地API配置
    API_HOST = '127.0.0.1'
    API_PORT = 5000

    # CORS配置
    CORS_ORIGINS = ["http://localhost:3000", "http://127.0.0.1:3000"]

class ProductionConfig(Config):
    """生产环境配置"""
    DEBUG = False
    FLASK_ENV = 'production'
    
    def __init__(self):
        """初始化时检查生产环境必需的配置"""
        super().__init__()
        # 生产环境安全配置检查
        if not os.environ.get('SECRET_KEY'):
            raise ValueError("No SECRET_KEY set for production environment")
        self.SECRET_KEY = os.environ.get('SECRET_KEY')

class TestingConfig(Config):
    """测试环境配置"""
    TESTING = True
    # 测试环境也使用MySQL数据库，但使用测试数据库
    mysql_config = Config.MYSQL_CACHE_CONFIG
    SQLALCHEMY_DATABASE_URI = (
        f"mysql+pymysql://{mysql_config['user']}:{mysql_config['password']}"
        f"@{mysql_config['host']}:{mysql_config['port']}/{mysql_config['database']}"
        f"?charset={mysql_config['charset']}"
        f"&ssl_verify_cert=false&ssl_verify_identity=false"
    )

    # Flask-SQLAlchemy引擎配置
    SQLALCHEMY_ENGINE_OPTIONS = {
        'pool_size': 5,
        'max_overflow': 10,
        'pool_timeout': 10,
        'pool_recycle': 3600,
        'pool_pre_ping': True,
        'connect_args': {
            'connect_timeout': 10,
            'read_timeout': 60,
            'write_timeout': 60,
            'charset': 'utf8mb4',
            'init_command': 'SET SESSION wait_timeout=28800'
        }
    }

# 配置字典
config = {
    'development': DevelopmentConfig,
    'local': LocalConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': LocalConfig
} 