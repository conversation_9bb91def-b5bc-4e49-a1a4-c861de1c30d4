#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
选择性技术指标更新优化验证脚本

验证优化后的智能更新功能：
1. 只对涨跌幅前10的板块重新计算技术指标
2. 其余76个板块使用缓存技术指标
3. 验证性能提升效果
4. 确认"新"标记正确添加
"""

import requests
import json
import time
from datetime import datetime

def test_selective_update_performance():
    """测试选择性更新的性能效果"""
    print("🚀 测试选择性技术指标更新性能")
    print("=" * 60)
    
    try:
        # 1. 获取更新前的数据状态
        print("1. 获取更新前的数据状态...")
        before_response = requests.get("http://localhost:5000/api/sectors/all-realtime", timeout=30)
        
        if before_response.status_code == 200:
            before_data = before_response.json()
            before_sectors = before_data.get('data', []) if before_data.get('success') else []
            
            # 分析更新前的涨跌幅排序
            if before_sectors:
                sorted_sectors = sorted(before_sectors, key=lambda x: x.get('涨跌幅', 0), reverse=True)
                print(f"📊 更新前涨跌幅前10板块:")
                for i, sector in enumerate(sorted_sectors[:10], 1):
                    name = sector.get('板块名称', 'N/A')
                    code = sector.get('板块代码', 'N/A')
                    change = sector.get('涨跌幅', 0)
                    has_new_mark = '新' in name
                    print(f"   {i}. {name} ({code}) 涨跌幅: {change:.2f}% {'[已有新标记]' if has_new_mark else ''}")
            
            # 2. 执行选择性更新
            print(f"\n2. 执行选择性技术指标更新...")
            update_start_time = datetime.now()
            print(f"更新开始时间: {update_start_time.strftime('%Y-%m-%d %H:%M:%S')}")
            
            start_time = time.time()
            
            # 使用优化后的智能更新API
            update_response = requests.post(
                "http://localhost:5000/api/data/update",
                json={
                    "update_mode": "incremental",
                    "force_user_update": True
                },
                timeout=420  # 7分钟超时
            )
            
            end_time = time.time()
            elapsed_time = end_time - start_time
            update_end_time = datetime.now()
            
            print(f"更新结束时间: {update_end_time.strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"总耗时: {elapsed_time:.2f} 秒 ({elapsed_time/60:.2f} 分钟)")
            
            # 3. 分析性能改进效果
            print(f"\n📊 性能分析:")
            
            expected_time_before = 172.73  # 之前的执行时间
            performance_improvement = (expected_time_before - elapsed_time) / expected_time_before * 100
            
            print(f"   优化前预期时间: {expected_time_before:.2f} 秒")
            print(f"   优化后实际时间: {elapsed_time:.2f} 秒")
            print(f"   性能提升: {performance_improvement:.1f}%")
            
            if elapsed_time < expected_time_before * 0.5:  # 如果时间减少超过50%
                print(f"✅ 性能优化目标达成: 时间减少超过50%")
            else:
                print(f"⚠️  性能优化效果待验证")
            
            if update_response.status_code == 200:
                update_data = update_response.json()
                print(f"✅ 更新结果: {update_data.get('success', False)}")
                print(f"📝 更新消息: {update_data.get('message', '无消息')}")
                
                # 4. 验证选择性更新效果
                print(f"\n3. 验证选择性更新效果...")
                after_response = requests.get("http://localhost:5000/api/sectors/all-realtime", timeout=30)
                
                if after_response.status_code == 200:
                    after_data = after_response.json()
                    after_sectors = after_data.get('data', []) if after_data.get('success') else []
                    
                    if after_sectors:
                        # 分析更新后的状态
                        sorted_after = sorted(after_sectors, key=lambda x: x.get('涨跌幅', 0), reverse=True)
                        
                        print(f"📊 更新后涨跌幅前10板块:")
                        new_marked_count = 0
                        for i, sector in enumerate(sorted_after[:10], 1):
                            name = sector.get('板块名称', 'N/A')
                            code = sector.get('板块代码', 'N/A')
                            change = sector.get('涨跌幅', 0)
                            has_new_mark = '新' in name
                            if has_new_mark:
                                new_marked_count += 1
                            print(f"   {i}. {name} ({code}) 涨跌幅: {change:.2f}% {'✅[新]' if has_new_mark else '❌[无新标记]'}")
                        
                        # 检查其余板块是否没有新标记
                        other_sectors_with_new = 0
                        for sector in sorted_after[10:]:
                            if '新' in sector.get('板块名称', ''):
                                other_sectors_with_new += 1
                        
                        print(f"\n📋 选择性更新验证:")
                        print(f"   前10板块有'新'标记: {new_marked_count}/10")
                        print(f"   其余板块有'新'标记: {other_sectors_with_new}/76")
                        
                        if new_marked_count >= 8 and other_sectors_with_new == 0:  # 允许少量失败
                            print(f"✅ 选择性更新验证成功")
                        else:
                            print(f"⚠️  选择性更新可能存在问题")
                    
                    return {
                        'success': update_data.get('success', False),
                        'elapsed_time': elapsed_time,
                        'performance_improvement': performance_improvement,
                        'new_marked_count': new_marked_count if 'new_marked_count' in locals() else 0,
                        'optimization_effective': elapsed_time < expected_time_before * 0.5
                    }
                else:
                    print(f"❌ 获取更新后数据失败: {after_response.status_code}")
                    return None
            else:
                print(f"❌ 智能更新失败: {update_response.status_code}")
                print(f"错误响应: {update_response.text[:500]}")
                return None
        else:
            print(f"❌ 获取更新前数据失败: {before_response.status_code}")
            return None
            
    except requests.exceptions.Timeout:
        elapsed_time = time.time() - start_time
        print(f"\n⚠️  请求超时 (耗时: {elapsed_time:.2f} 秒)")
        return None
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return None

def analyze_optimization_benefits():
    """分析优化收益"""
    print(f"\n📈 选择性更新优化分析")
    print("=" * 60)
    
    print("🔧 优化策略:")
    print("1. 选择性计算: 只对涨跌幅前10的板块重新计算技术指标")
    print("2. 缓存利用: 其余76个板块直接使用缓存技术指标")
    print("3. 智能标记: 为更新的板块添加'新'标记")
    
    print("\n📊 预期性能提升:")
    print("- 技术指标计算量: 减少 88.4% (86 → 10)")
    print("- 历史数据获取: 减少 88.4% (86次 → 10次)")
    print("- 总体执行时间: 预计减少 60-70%")
    print("- 从约173秒降低到约50-60秒")
    
    print("\n💡 优化亮点:")
    print("- 保持最活跃板块(涨跌幅前10)的技术指标准确性")
    print("- 大幅减少不必要的重复计算")
    print("- 通过'新'标记清晰标识更新状态")
    print("- 显著提升用户体验")

def verify_code_changes():
    """验证代码修改"""
    print(f"\n📋 验证代码修改")
    print("=" * 60)
    
    try:
        with open('backend/api/routes.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键优化点
        optimizations_verified = []
        
        # 1. 检查选择性更新逻辑
        if '选择性更新策略' in content and 'top_10_sectors' in content:
            optimizations_verified.append("✅ 选择性更新策略已实现")
        else:
            optimizations_verified.append("❌ 选择性更新策略未实现")
        
        # 2. 检查涨跌幅排序逻辑
        if 'sort_values' in content and '涨跌幅' in content:
            optimizations_verified.append("✅ 涨跌幅排序逻辑已添加")
        else:
            optimizations_verified.append("❌ 涨跌幅排序逻辑未添加")
        
        # 3. 检查"新"标记逻辑
        if '新标记' in content and 'endswith' in content:
            optimizations_verified.append("✅ '新'标记逻辑已添加")
        else:
            optimizations_verified.append("❌ '新'标记逻辑未添加")
        
        # 4. 检查性能统计日志
        if '选择性技术指标更新完成' in content and '性能提升' in content:
            optimizations_verified.append("✅ 性能统计日志已添加")
        else:
            optimizations_verified.append("❌ 性能统计日志未添加")
        
        for optimization in optimizations_verified:
            print(optimization)
        
        return all('✅' in opt for opt in optimizations_verified)
        
    except FileNotFoundError:
        print("❌ 无法找到后端路由文件")
        return False
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🔧 选择性技术指标更新优化验证")
    print("=" * 60)
    print("验证时间:", datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
    print("优化目标: 只对涨跌幅前10的板块重新计算技术指标，大幅提升性能")
    
    # 1. 验证代码修改
    code_ok = verify_code_changes()
    
    # 2. 分析优化收益
    analyze_optimization_benefits()
    
    # 3. 测试实际性能
    if code_ok:
        test_result = test_selective_update_performance()
    else:
        test_result = None
    
    # 4. 总结验证结果
    print(f"\n📊 验证总结")
    print("=" * 60)
    
    if code_ok:
        print("✅ 代码修改验证通过")
    else:
        print("❌ 代码修改验证失败")
    
    if test_result:
        if test_result['optimization_effective']:
            print("🎉 性能优化验证成功！")
            print(f"   - 执行时间: {test_result['elapsed_time']:.2f} 秒")
            print(f"   - 性能提升: {test_result['performance_improvement']:.1f}%")
            print(f"   - 新标记数量: {test_result['new_marked_count']}/10")
        else:
            print("⚠️  性能优化效果待确认")
            print(f"   - 执行时间: {test_result['elapsed_time']:.2f} 秒")
    else:
        print("⚠️  性能测试未完成")
    
    print(f"\n💡 优化要点回顾:")
    print("1. 实现了选择性技术指标更新策略")
    print("2. 只对涨跌幅前10的板块重新计算")
    print("3. 其余76个板块使用缓存技术指标")
    print("4. 添加了'新'标记标识更新状态")
    print("5. 预计性能提升60-70%")

if __name__ == "__main__":
    main()
