#!/usr/bin/env python3
"""
调试BK0440板块技术分析数据问题
分析为什么历史数据21条，技术分析数据只有2条
"""

import sys
import os

# 添加backend目录到Python路径
backend_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'backend')
sys.path.insert(0, backend_dir)

from app import create_app
from database import db
from models import Sector, DailyQuote, TechnicalAnalysis
from datetime import datetime, timedelta

def debug_bk0440_data():
    """调试BK0440板块数据问题"""
    print("=" * 80)
    print("🔍 调试BK0440板块技术分析数据问题")
    print("=" * 80)
    
    app = create_app('local')
    with app.app_context():
        # 1. 获取板块信息
        sector = Sector.query.filter_by(sector_code='BK0440').first()
        if not sector:
            print("❌ 未找到BK0440板块")
            return
        
        print(f"📋 板块信息:")
        print(f"   ID: {sector.id}")
        print(f"   代码: {sector.sector_code}")
        print(f"   名称: {sector.sector_name}")
        print(f"   描述: {sector.description}")
        
        # 2. 检查历史行情数据
        print(f"\n📈 历史行情数据分析:")
        quotes = DailyQuote.query.filter_by(sector_id=sector.id)\
                                .order_by(DailyQuote.quote_date.desc())\
                                .all()
        
        print(f"   总计: {len(quotes)}条历史行情数据")
        
        if quotes:
            print(f"   最新日期: {quotes[0].quote_date}")
            print(f"   最早日期: {quotes[-1].quote_date}")
            
            # 显示最近10条数据
            print(f"\n   最近10条行情数据:")
            for i, quote in enumerate(quotes[:10]):
                print(f"     {i+1:2d}. {quote.quote_date} | 收盘: {quote.close_price:8.2f} | 涨跌幅: {quote.price_change_pct:6.2f}%")
        
        # 3. 检查技术分析数据
        print(f"\n📊 技术分析数据分析:")
        analyses = TechnicalAnalysis.query.filter_by(sector_id=sector.id)\
                                        .order_by(TechnicalAnalysis.analysis_date.desc())\
                                        .all()
        
        print(f"   总计: {len(analyses)}条技术分析数据")
        
        if analyses:
            print(f"   最新分析日期: {analyses[0].analysis_date}")
            print(f"   最早分析日期: {analyses[-1].analysis_date}")
            
            # 显示所有分析数据
            print(f"\n   所有技术分析数据:")
            for i, analysis in enumerate(analyses):
                print(f"     {i+1:2d}. {analysis.analysis_date} | MA5: {analysis.ma5:8.2f} | MA10: {analysis.ma10:8.2f} | MA20: {analysis.ma20:8.2f} | 趋势: {analysis.trend_direction}")
        
        # 4. 分析数据不一致的原因
        print(f"\n🔍 数据不一致原因分析:")
        
        # 检查历史数据生成逻辑
        if len(quotes) >= 20:
            quotes_sorted = sorted(quotes, key=lambda x: x.quote_date)
            print(f"   ✅ 历史数据足够生成技术分析 (共{len(quotes)}条，需要至少20条)")
            
            # 计算应该生成多少条技术分析数据
            expected_analysis_count = len(quotes_sorted) - 19  # 从第20个数据开始
            print(f"   📊 预期技术分析数据条数: {expected_analysis_count}条 (从第20条数据开始)")
            print(f"   📊 实际技术分析数据条数: {len(analyses)}条")
            print(f"   📊 差异: {expected_analysis_count - len(analyses)}条")
            
            # 检查哪些日期缺少技术分析
            analysis_dates = {analysis.analysis_date for analysis in analyses}
            missing_dates = []
            
            for i in range(19, len(quotes_sorted)):
                target_date = quotes_sorted[i].quote_date
                if target_date not in analysis_dates:
                    missing_dates.append(target_date)
            
            if missing_dates:
                print(f"\n   ❌ 缺少技术分析的日期 ({len(missing_dates)}个):")
                for date in missing_dates[:10]:  # 只显示前10个
                    print(f"      - {date}")
                if len(missing_dates) > 10:
                    print(f"      ... 还有{len(missing_dates) - 10}个日期")
            else:
                print(f"   ✅ 所有应该有技术分析的日期都已生成")
        
        else:
            print(f"   ❌ 历史数据不足 (共{len(quotes)}条，需要至少20条)")
        
        # 5. 检查最近30天的数据
        print(f"\n📅 最近30天数据检查:")
        thirty_days_ago = datetime.now().date() - timedelta(days=30)
        
        recent_quotes = DailyQuote.query.filter_by(sector_id=sector.id)\
                                      .filter(DailyQuote.quote_date >= thirty_days_ago)\
                                      .order_by(DailyQuote.quote_date.desc())\
                                      .all()
        
        recent_analyses = TechnicalAnalysis.query.filter_by(sector_id=sector.id)\
                                                .filter(TechnicalAnalysis.analysis_date >= thirty_days_ago)\
                                                .order_by(TechnicalAnalysis.analysis_date.desc())\
                                                .all()
        
        print(f"   最近30天行情数据: {len(recent_quotes)}条")
        print(f"   最近30天分析数据: {len(recent_analyses)}条")
        
        # 6. 检查数据库约束和重复数据
        print(f"\n🔒 数据完整性检查:")
        
        # 检查是否有重复的技术分析数据
        duplicate_check = db.session.query(TechnicalAnalysis.analysis_date, db.func.count(TechnicalAnalysis.id))\
                                  .filter_by(sector_id=sector.id)\
                                  .group_by(TechnicalAnalysis.analysis_date)\
                                  .having(db.func.count(TechnicalAnalysis.id) > 1)\
                                  .all()
        
        if duplicate_check:
            print(f"   ⚠️ 发现重复的技术分析数据:")
            for date, count in duplicate_check:
                print(f"      {date}: {count}条记录")
        else:
            print(f"   ✅ 没有重复的技术分析数据")
        
        # 7. 生成修复建议
        print(f"\n💡 修复建议:")
        if len(quotes) >= 20 and len(analyses) < (len(quotes) - 19):
            print(f"   1. 历史数据充足但技术分析数据不完整")
            print(f"   2. 建议重新生成历史技术分析数据")
            print(f"   3. 可以调用 database_service.generate_historical_analysis('BK0440') 方法")
        elif len(quotes) < 20:
            print(f"   1. 历史数据不足，无法生成完整的技术分析")
            print(f"   2. 建议先获取更多历史数据")
        else:
            print(f"   1. 数据看起来正常，可能是查询条件的问题")

if __name__ == "__main__":
    debug_bk0440_data()
