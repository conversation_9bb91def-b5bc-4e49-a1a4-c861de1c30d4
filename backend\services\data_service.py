"""
数据服务层 - 基于AKShare API的统一数据访问层
集成MySQL缓存功能，提供智能数据缓存和获取
"""
import akshare as ak
import pandas as pd
import numpy as np
from datetime import datetime, date, timedelta
from typing import Optional, List, Dict, Any
import logging
import time
import threading
from functools import wraps
import os
import sys

# 禁用tqdm进度条以避免多个进度条同时显示
try:
    from tqdm import tqdm
    # 设置全局禁用tqdm进度条
    tqdm.disable = True
    print("已禁用tqdm进度条，避免多个进度条同时显示")
except ImportError:
    pass

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 添加utils路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'utils'))

try:
    from mysql_cache_manager import MySQLCacheManager
    from cache_decorator import SmartCacheService
    from concurrent_api_controller import ConcurrentAP<PERSON>ontroller, SequentialAPIController, APICallStatus
    MYSQL_CACHE_AVAILABLE = True
    logger.info("MySQL缓存模块导入成功")
except ImportError as e:
    logger.warning(f"MySQL缓存模块导入失败: {e}")
    MYSQL_CACHE_AVAILABLE = False

class DataService:
    """统一数据访问层 - 单例模式优化版本"""

    _instance = None
    _lock = threading.Lock()
    _initialized = False

    def __new__(cls, cache_timeout=900, retry_count=8, max_persistent_retries=10, mysql_config=None, use_concurrent_api=False):
        """单例模式实现"""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super(DataService, cls).__new__(cls)
        return cls._instance

    def __init__(self, cache_timeout=900, retry_count=8, max_persistent_retries=10, mysql_config=None, use_concurrent_api=False):
        # 避免重复初始化
        if self._initialized:
            logger.debug("DataService已初始化，跳过重复初始化")
            return

        with self._lock:
            if self._initialized:
                return

        self.cache_timeout = cache_timeout
        self.retry_count = retry_count  # 增加到8次
        self.max_persistent_retries = max_persistent_retries  # 持久化重试最大次数
        self._cache = {}
        self.use_concurrent_api = use_concurrent_api

        # 初始化MySQL缓存
        self.mysql_cache_manager = None
        self.cache_service = None

        if MYSQL_CACHE_AVAILABLE and mysql_config:
            try:
                self.mysql_cache_manager = MySQLCacheManager(mysql_config['MYSQL_CACHE_CONFIG'])
                self.cache_service = SmartCacheService(
                    self.mysql_cache_manager,
                    mysql_config['CACHE_STRATEGY']
                )
                logger.info("MySQL缓存服务初始化成功")
            except Exception as e:
                logger.error(f"MySQL缓存服务初始化失败: {e}")
                self.mysql_cache_manager = None
                self.cache_service = None

        # 初始化API控制器 - 优先使用顺序控制器
        if MYSQL_CACHE_AVAILABLE:
            try:
                # 总是初始化顺序控制器
                self.sequential_controller = SequentialAPIController(retry_count=3, retry_delay=10.0)
                logger.info("API顺序控制器初始化成功")

                # 只有在明确启用并发模式时才初始化并发控制器
                if self.use_concurrent_api:
                    self.concurrent_controller = ConcurrentAPIController(max_workers=3)
                    logger.info("API并发控制器初始化成功")
                else:
                    self.concurrent_controller = None
                    logger.info("并发模式已禁用，仅使用顺序调用模式")
            except Exception as e:
                logger.error(f"API控制器初始化失败: {e}")
                self.concurrent_controller = None
                self.sequential_controller = None
        else:
            self.concurrent_controller = None
            self.sequential_controller = None

        # 标记为已初始化
        self._initialized = True
        logger.info("DataService单例初始化完成")
        
    def _retry_on_failure(self, func):
        """增强重试装饰器 - 实现渐进式等待和智能重试"""
        @wraps(func)
        def wrapper(*args, **kwargs):
            last_exception = None
            # 优化的等待时间：增加到10秒起步，更适合网络连接问题
            wait_times = [10, 15, 20, 25, 30, 35, 40, 45]

            for attempt in range(self.retry_count):
                try:
                    result = func(*args, **kwargs)

                    # 检查返回结果是否为空或无效
                    if result is None:
                        raise ValueError("API返回None")
                    if hasattr(result, 'empty') and result.empty:
                        raise ValueError("API返回空DataFrame")
                    if hasattr(result, '__len__') and len(result) == 0:
                        raise ValueError("API返回空数据")

                    # 成功返回结果
                    if attempt > 0:
                        logger.info(f"API调用在第{attempt + 1}次尝试后成功")
                    return result

                except Exception as e:
                    last_exception = e
                    error_msg = str(e).lower()

                    # 判断是否为临时错误（可重试）
                    is_temporary_error = any(keyword in error_msg for keyword in [
                        'timeout', 'connection', 'network', 'rate limit',
                        'too many requests', '429', '503', '502', '504'
                    ])

                    # 判断是否为数据问题（快速失败）
                    is_data_error = any(keyword in error_msg for keyword in [
                        'empty', 'none', 'index', 'bounds', 'subscriptable',
                        'not found', 'invalid', 'no data'
                    ])

                    logger.warning(f"API调用失败 (尝试 {attempt + 1}/{self.retry_count}): {e}")

                    # 如果是数据错误且已经尝试3次，快速失败
                    if is_data_error and attempt >= 2:
                        logger.warning(f"检测到数据错误，快速失败避免过度重试")
                        break

                    # 如果是最后一次尝试，不再等待
                    if attempt < self.retry_count - 1:
                        # 根据尝试次数选择等待时间
                        wait_time = wait_times[min(attempt, len(wait_times) - 1)]

                        # 对于临时错误，稍微增加等待时间
                        if is_temporary_error:
                            wait_time = min(wait_time * 1.2, 60)  # 最大1分钟
                        elif is_data_error:
                            wait_time = min(wait_time * 0.5, 10)  # 数据错误减少等待

                        logger.info(f"等待 {wait_time} 秒后重试...")
                        time.sleep(wait_time)
                    else:
                        logger.error(f"API调用最终失败，已尝试 {self.retry_count} 次")

            raise last_exception
        return wrapper

    def _get_stock_realtime_data_with_fallback(self) -> Optional[pd.DataFrame]:
        """
        使用故障转移机制获取A股实时行情数据
        支持并发调用和顺序调用两种模式
        """
        # 定义两种数据源的获取方法 - 优先使用新浪财经API
        data_sources = [
            {
                'name': '新浪财经数据源',
                'method': lambda: ak.stock_zh_a_spot(),
                'description': 'stock_zh_a_spot'
            },
            {
                'name': '东方财富数据源',
                'method': lambda: ak.stock_zh_a_spot_em(),
                'description': 'stock_zh_a_spot_em'
            }
        ]

        # 优先使用顺序调用模式，避免并发请求触发API限制
        if self.sequential_controller:
            try:
                logger.info("✅ 使用顺序模式获取A股实时行情数据（非并发模式）")
                logger.info(f"📋 数据源列表: {[source['name'] for source in data_sources]}")
                result = self.sequential_controller.call_with_fallback(data_sources)
                if result.status == APICallStatus.SUCCESS:
                    logger.info(f"顺序模式成功获取数据，数据源: {result.source_name}，共{len(result.data)}只股票")
                    return result.data
                else:
                    logger.warning(f"顺序模式失败: {result.error}")
                    # 降级到原始方法
                    logger.info("降级到原始顺序调用方法")
                    return self._get_stock_realtime_data_legacy()
            except Exception as e:
                logger.error(f"顺序控制器调用失败: {e}")
                # 降级到原始方法
                return self._get_stock_realtime_data_legacy()
        else:
            # 使用原始的顺序调用方法
            logger.info("使用原始顺序调用方法获取A股实时行情数据")
            return self._get_stock_realtime_data_legacy()

    def _get_stock_realtime_data_legacy(self) -> Optional[pd.DataFrame]:
        """
        原始的顺序调用方法（作为备选方案）
        """
        # 定义两种数据源的获取方法 - 优先使用新浪财经API
        data_sources = [
            {
                'name': '新浪财经数据源',
                'method': lambda: ak.stock_zh_a_spot(),
                'description': 'stock_zh_a_spot'
            },
            {
                'name': '东方财富数据源',
                'method': lambda: ak.stock_zh_a_spot_em(),
                'description': 'stock_zh_a_spot_em'
            }
        ]

        # 对每种数据源尝试3次（顺序调用，非并发）
        for source_idx, source in enumerate(data_sources):
            logger.info(f"🔄 开始尝试{source['name']} ({source['description']}) - 顺序调用第{source_idx + 1}个数据源")

            for attempt in range(3):  # 每种数据源最多重试3次
                try:
                    logger.info(f"[{source['name']}] 第{attempt + 1}次尝试获取A股实时行情数据")
                    stock_realtime_df = source['method']()

                    # 验证数据有效性
                    if stock_realtime_df is not None and not stock_realtime_df.empty:
                        logger.info(f"[{source['name']}] 获取A股实时行情数据成功，共{len(stock_realtime_df)}只股票")
                        return stock_realtime_df
                    else:
                        raise ValueError("返回的数据为空")

                except Exception as e:
                    logger.warning(f"[{source['name']}] 第{attempt + 1}次获取失败: {e}")

                    # 如果不是最后一次尝试，等待10秒后重试
                    if attempt < 2:
                        logger.info(f"[{source['name']}] 等待10秒后重试...")
                        time.sleep(10)
                    else:
                        logger.error(f"[{source['name']}] 所有重试都失败")

            # 如果当前数据源失败，尝试下一个数据源
            if source_idx < len(data_sources) - 1:
                logger.info(f"[{source['name']}] 失败，切换到下一个数据源")
                time.sleep(10)  # 切换数据源前等待10秒

        logger.error("所有数据源都失败，无法获取A股实时行情数据")
        return None

    def _persistent_retry(self, func, *args, **kwargs):
        """持久化重试逻辑 - 用于最重要的数据获取"""
        last_exception = None
        successful_methods = []
        failed_methods = []

        for attempt in range(self.max_persistent_retries):
            try:
                logger.info(f"持久化重试第 {attempt + 1}/{self.max_persistent_retries} 次")
                result = func(*args, **kwargs)

                if result is not None and not (hasattr(result, 'empty') and result.empty):
                    logger.info(f"持久化重试在第 {attempt + 1} 次尝试后成功")
                    return result
                else:
                    raise ValueError("返回空数据")

            except Exception as e:
                last_exception = e
                error_msg = str(e).lower()

                # 记录失败的方法
                method_name = func.__name__ if hasattr(func, '__name__') else str(func)
                failed_methods.append(f"{method_name}({error_msg[:50]})")

                logger.warning(f"持久化重试失败 (尝试 {attempt + 1}/{self.max_persistent_retries}): {e}")

                # 判断是否应该继续重试
                permanent_errors = ['invalid', 'not found', '404', 'unauthorized', '401', '403']
                if any(err in error_msg for err in permanent_errors):
                    logger.error(f"检测到永久性错误，停止重试: {e}")
                    break

                # 如果不是最后一次尝试，等待更长时间
                if attempt < self.max_persistent_retries - 1:
                    # 持久化重试使用更长的等待时间
                    wait_time = min(60 + (attempt * 30), 300)  # 60s到300s
                    logger.info(f"持久化重试等待 {wait_time} 秒...")
                    time.sleep(wait_time)

        logger.error(f"持久化重试最终失败，已尝试 {self.max_persistent_retries} 次")
        logger.debug(f"失败的方法: {failed_methods}")
        raise last_exception



    def _get_cache_key(self, func_name: str, *args, **kwargs) -> str:
        """生成缓存键"""
        key_parts = [func_name] + [str(arg) for arg in args]
        for k, v in sorted(kwargs.items()):
            key_parts.append(f"{k}={v}")
        return "|".join(key_parts)
    
    def _get_from_cache(self, cache_key: str) -> Optional[Any]:
        """从缓存获取数据"""
        if cache_key in self._cache:
            data, timestamp = self._cache[cache_key]
            if time.time() - timestamp < self.cache_timeout:
                return data
            else:
                del self._cache[cache_key]
        return None
    
    def _set_cache(self, cache_key: str, data: Any):
        """设置缓存"""
        self._cache[cache_key] = (data, time.time())
    

    
    def get_sector_historical_data(self, sector_code: str, start_date: str = None,
                                 end_date: str = None) -> pd.DataFrame:
        """获取板块历史数据 - 严格使用AkShare API，支持MySQL缓存"""
        if start_date is None:
            start_date = (datetime.now() - timedelta(days=365)).strftime("%Y%m%d")
        if end_date is None:
            end_date = datetime.now().strftime("%Y%m%d")

        # 如果启用了MySQL缓存，先尝试从缓存获取
        if self.cache_service:
            cached_data = self.cache_service.cache_manager.get_cache(
                'historical_data_cache',
                'sector_historical_data',
                sector_code=sector_code,
                start_date=start_date,
                end_date=end_date
            )
            if cached_data is not None:
                logger.info(f"从MySQL缓存获取板块历史数据: {sector_code}")
                return cached_data

        cache_key = self._get_cache_key("sector_hist", sector_code, start_date, end_date)
        cached_data = self._get_from_cache(cache_key)
        if cached_data is not None:
            return cached_data

        logger.info(f"开始获取板块 {sector_code} 历史数据，日期范围: {start_date} - {end_date}")

        # 方法1: 通过申万代码映射到东财代码获取历史数据
        try:
            df = self._fetch_akshare_sector_hist_by_mapping(sector_code, start_date, end_date)
            if df is not None and not df.empty:
                logger.info(f"✅ 通过代码映射成功获取板块 {sector_code} 历史数据，共 {len(df)} 条记录")
                self._set_cache(cache_key, df)
                # 存储到MySQL缓存
                if self.cache_service:
                    self.cache_service.cache_manager.set_cache(
                        'historical_data_cache',
                        'sector_historical_data',
                        df,
                        3600,  # 1小时TTL
                        sector_code=sector_code,
                        start_date=start_date,
                        end_date=end_date
                    )
                return df
        except Exception as e:
            logger.warning(f"通过代码映射获取失败: {e}")

        # 方法2: 通过板块名称获取历史数据（备用方法）
        try:
            df = self._fetch_akshare_sector_hist_by_name(sector_code, start_date, end_date)
            if df is not None and not df.empty:
                logger.info(f"✅ 通过名称成功获取板块 {sector_code} 历史数据，共 {len(df)} 条记录")
                self._set_cache(cache_key, df)
                # 存储到MySQL缓存
                if self.cache_service:
                    self.cache_service.cache_manager.set_cache(
                        'historical_data_cache',
                        'sector_historical_data',
                        df,
                        3600,  # 1小时TTL
                        sector_code=sector_code,
                        start_date=start_date,
                        end_date=end_date
                    )
                return df
        except Exception as e:
            logger.warning(f"通过名称获取失败: {e}")

        # 方法3: 直接使用板块代码获取历史数据（最后尝试）
        try:
            df = self._fetch_akshare_sector_hist(sector_code, start_date, end_date)
            if df is not None and not df.empty:
                logger.info(f"✅ 直接获取板块 {sector_code} 历史数据成功，共 {len(df)} 条记录")
                self._set_cache(cache_key, df)
                # 存储到MySQL缓存
                if self.cache_service:
                    self.cache_service.cache_manager.set_cache(
                        'historical_data_cache',
                        'sector_historical_data',
                        df,
                        3600,  # 1小时TTL
                        sector_code=sector_code,
                        start_date=start_date,
                        end_date=end_date
                    )
                return df
        except Exception as e:
            logger.warning(f"直接获取板块代码失败: {e}")

        # 最终失败
        logger.error(f"❌ 无法获取板块 {sector_code} 的历史数据")
        return pd.DataFrame()

    def _get_sector_code_mapping(self) -> Dict[str, str]:
        """获取申万代码到东财代码的映射表"""
        # 申万一级行业代码到东财代码的映射（基于行业相似性）
        mapping = {
            '801010': 'BK0433',  # 农林牧渔 -> 农牧饲渔
            '801020': 'BK0437',  # 采掘 -> 煤炭行业
            '801030': 'BK1019',  # 化工 -> 化学原料
            '801040': 'BK0479',  # 钢铁 -> 钢铁行业
            '801050': 'BK0478',  # 有色金属 -> 有色金属
            '801080': 'BK1036',  # 电子 -> 半导体
            '801110': 'BK0456',  # 家用电器 -> 家电行业
            '801120': 'BK0438',  # 食品饮料 -> 食品饮料
            '801130': 'BK0436',  # 纺织服装 -> 纺织服装
            '801140': 'BK0440',  # 轻工制造 -> 家用轻工
            '801150': 'BK0465',  # 医药生物 -> 化学制药
            '801160': 'BK0427',  # 公用事业 -> 公用事业
            '801170': 'BK0421',  # 交通运输 -> 铁路公路
            '801180': 'BK0451',  # 房地产 -> 房地产开发
            '801200': 'BK0482',  # 商业贸易 -> 商业百货
            '801210': 'BK0485',  # 休闲服务 -> 旅游酒店
            '801230': 'BK0539',  # 综合 -> 综合行业
            '801710': 'BK0424',  # 建筑材料 -> 水泥建材
            '801720': 'BK0725',  # 建筑装饰 -> 装修装饰
            '801730': 'BK0457',  # 电气设备 -> 电网设备
            '801740': 'BK0480',  # 国防军工 -> 航天航空
            '801750': 'BK0737',  # 计算机 -> 软件开发
            '801760': 'BK0486',  # 传媒 -> 文化传媒
            '801770': 'BK0448',  # 通信 -> 通信设备
            '801780': 'BK0475',  # 银行 -> 银行
            '801790': 'BK0738',  # 非银金融 -> 多元金融
            '801880': 'BK1029',  # 汽车 -> 汽车整车
            '801890': 'BK0739',  # 机械设备 -> 工程机械
            # 添加缺失的板块映射（修复801950、801960、801970失败问题）
            '801950': 'BK0437',  # 煤炭 -> 煤炭行业
            '801960': 'BK0474',  # 石油石化 -> 石油行业
            '801970': 'BK0493',  # 环保 -> 环保工程
        }
        return mapping

    def _fetch_akshare_sector_hist_by_mapping(self, sector_code: str, start_date: str, end_date: str) -> pd.DataFrame:
        """通过申万代码映射到东财代码获取板块历史数据"""
        try:
            mapping = self._get_sector_code_mapping()

            if sector_code not in mapping:
                logger.warning(f"申万代码 {sector_code} 未找到对应的东财代码映射")
                logger.debug(f"当前映射表包含 {len(mapping)} 个代码映射")
                logger.debug(f"建议在_get_sector_code_mapping方法中添加 {sector_code} 的映射关系")
                return pd.DataFrame()

            em_code = mapping[sector_code]
            logger.info(f"申万代码 {sector_code} 映射到东财代码 {em_code}")

            @self._retry_on_failure
            def fetch_hist_data():
                return ak.stock_board_industry_hist_em(
                    symbol=em_code,
                    start_date=start_date,
                    end_date=end_date,
                    period="日k",
                    adjust=""
                )
            df = fetch_hist_data()

            # 过滤周末数据，确保数据一致性
            if not df.empty and '日期' in df.columns:
                df = self._filter_trading_days(df, '日期')
                logger.info(f"板块 {sector_code} 映射获取后保留 {len(df)} 条交易日数据")

            return df
        except Exception as e:
            logger.error(f"AkShare通过映射获取板块 {sector_code} 历史数据失败: {e}")
            return pd.DataFrame()

    def _fetch_akshare_sector_hist(self, sector_code: str, start_date: str, end_date: str) -> pd.DataFrame:
        """使用AkShare API获取板块历史数据（按代码）"""
        try:
            @self._retry_on_failure
            def fetch_hist_data():
                return ak.stock_board_industry_hist_em(
                    symbol=sector_code,
                    start_date=start_date,
                    end_date=end_date,
                    period="日k",
                    adjust=""
                )
            df = fetch_hist_data()

            # 过滤周末数据，确保数据一致性
            if not df.empty and '日期' in df.columns:
                df = self._filter_trading_days(df, '日期')
                logger.info(f"板块 {sector_code} 过滤后保留 {len(df)} 条交易日数据")

            return df
        except Exception as e:
            logger.error(f"AkShare获取板块 {sector_code} 历史数据失败: {e}")
            return pd.DataFrame()

    def _fetch_akshare_sector_hist_by_name(self, sector_code: str, start_date: str, end_date: str) -> pd.DataFrame:
        """使用AkShare API获取板块历史数据（按名称）"""
        try:
            # 先获取实时数据找到对应的板块名称
            realtime_df = self.get_sector_realtime_data()
            sector_row = realtime_df[realtime_df['板块代码'] == sector_code]

            if sector_row.empty:
                logger.warning(f"未找到板块代码 {sector_code} 对应的名称")
                logger.debug(f"实时数据中可用的板块代码: {list(realtime_df['板块代码'].unique())[:10]}...")
                logger.debug(f"这通常表示 {sector_code} 是申万代码，而实时数据使用东财代码格式")
                return pd.DataFrame()

            sector_name = sector_row.iloc[0]['板块名称']

            @self._retry_on_failure
            def fetch_hist_data():
                return ak.stock_board_industry_hist_em(
                    symbol=sector_name,
                    start_date=start_date,
                    end_date=end_date,
                    period="日k",
                    adjust=""
                )
            df = fetch_hist_data()

            # 过滤周末数据，确保数据一致性
            if not df.empty and '日期' in df.columns:
                df = self._filter_trading_days(df, '日期')
                logger.info(f"板块 {sector_code} ({sector_name}) 过滤后保留 {len(df)} 条交易日数据")

            return df
        except Exception as e:
            logger.error(f"AkShare通过名称获取板块 {sector_code} 历史数据失败: {e}")
            return pd.DataFrame()




    def get_sector_realtime_data(self) -> pd.DataFrame:
        """获取申万行业实时数据，支持MySQL缓存，交易时间内优先使用实时数据"""
        # 导入交易日历工具
        from utils.trading_calendar import trading_calendar

        # 判断当前是否为交易时间
        is_trading_time = trading_calendar.is_trading_time()

        # 如果是交易时间，跳过缓存直接获取实时数据
        if is_trading_time:
            logger.info("当前为交易时间，跳过缓存直接获取申万行业实时数据")
            try:
                # 获取申万行业实时数据
                df = ak.stock_board_industry_name_em()
                logger.info(f"交易时间内成功获取申万行业实时数据，共{len(df)}个板块")

                # 即使在交易时间，也更新缓存供非交易时间使用
                cache_key = self._get_cache_key("sector_realtime")
                self._set_cache(cache_key, df)

                if self.cache_service:
                    self.cache_service.cache_manager.set_cache(
                        'realtime_quotes_cache',
                        'sector_realtime_data',
                        df,
                        300  # 5分钟TTL
                    )

                return df
            except Exception as e:
                logger.error(f"交易时间内获取申万行业实时数据失败: {e}")
                # 如果实时数据获取失败，降级使用缓存数据
                logger.warning("降级使用缓存数据")

        # 非交易时间或实时数据获取失败时，使用缓存数据
        logger.info("非交易时间，优先使用缓存数据")

        # 先尝试从MySQL缓存获取
        if self.cache_service:
            cached_data = self.cache_service.cache_manager.get_cache(
                'realtime_quotes_cache',
                'sector_realtime_data'
            )
            if cached_data is not None:
                logger.info("从MySQL缓存获取申万行业实时数据")
                return cached_data

        cache_key = self._get_cache_key("sector_realtime")
        cached_data = self._get_from_cache(cache_key)
        if cached_data is not None:
            logger.info("从内存缓存获取申万行业实时数据")
            return cached_data

        try:
            # 缓存中没有数据，获取申万行业实时数据
            df = ak.stock_board_industry_name_em()
            logger.info(f"成功获取申万行业实时数据，共{len(df)}个板块")
            self._set_cache(cache_key, df)

            # 存储到MySQL缓存
            if self.cache_service:
                self.cache_service.cache_manager.set_cache(
                    'realtime_quotes_cache',
                    'sector_realtime_data',
                    df,
                    300  # 5分钟TTL
                )

            return df
        except Exception as e:
            logger.error(f"获取申万行业实时数据失败: {e}")
            raise

    def get_enhanced_sector_realtime_data_with_indicators(self) -> pd.DataFrame:
        """
        获取86个板块实时数据并计算技术指标
        为表格显示添加四个新列：趋势判断、震荡判断、连续上涨判断、新高判断
        """
        try:
            # 获取基础实时数据
            realtime_df = self.get_enhanced_sector_realtime_data()

            if realtime_df.empty:
                logger.warning("获取实时数据为空，无法计算技术指标")
                return realtime_df

            # 导入技术分析服务
            from services.analysis_service import TechnicalAnalysisService
            analysis_service = TechnicalAnalysisService()

            # 为每个板块计算技术指标
            enhanced_data = []

            for _, row in realtime_df.iterrows():
                sector_code = row['板块代码']
                sector_name = row['板块名称']

                try:
                    # 获取板块历史数据用于技术指标计算
                    historical_data = self._get_sector_historical_for_indicators(sector_code)

                    if not historical_data.empty:
                        # 计算技术指标
                        indicators = analysis_service.calculate_sector_table_indicators(historical_data)

                        # 添加技术指标到行数据
                        row_dict = row.to_dict()
                        row_dict['趋势'] = indicators['trend_judgment']
                        row_dict['震荡'] = indicators['oscillation_judgment']
                        row_dict['连续上涨'] = indicators['consecutive_rise_judgment']
                        row_dict['新高'] = indicators['new_high_judgment']

                        enhanced_data.append(row_dict)
                        logger.debug(f"板块 {sector_name}({sector_code}) 技术指标计算完成")
                    else:
                        # 历史数据为空，使用默认值
                        row_dict = row.to_dict()
                        row_dict['趋势'] = "数据不足"
                        row_dict['震荡'] = "数据不足"
                        row_dict['连续上涨'] = "数据不足"
                        row_dict['新高'] = "数据不足"

                        enhanced_data.append(row_dict)
                        logger.warning(f"板块 {sector_name}({sector_code}) 历史数据为空，使用默认值")

                except Exception as e:
                    # 单个板块计算失败，使用默认值
                    row_dict = row.to_dict()
                    row_dict['趋势'] = "计算失败"
                    row_dict['震荡'] = "计算失败"
                    row_dict['连续上涨'] = "计算失败"
                    row_dict['新高'] = "计算失败"

                    enhanced_data.append(row_dict)
                    logger.error(f"板块 {sector_name}({sector_code}) 技术指标计算失败: {e}")

            # 转换为DataFrame
            enhanced_df = pd.DataFrame(enhanced_data)
            logger.info(f"成功为 {len(enhanced_df)} 个板块计算技术指标")

            return enhanced_df

        except Exception as e:
            logger.error(f"获取增强板块数据失败: {e}")
            # 返回基础数据，不包含技术指标
            return self.get_enhanced_sector_realtime_data()

    def _get_sector_historical_for_indicators(self, sector_code: str, days: int = 60) -> pd.DataFrame:
        """
        获取板块历史数据用于技术指标计算
        优化版本：优先使用MySQL缓存，减少API调用
        """
        try:
            # 计算日期范围
            end_date = datetime.now().strftime('%Y%m%d')
            start_date = (datetime.now() - timedelta(days=days)).strftime('%Y%m%d')

            # 优先从MySQL缓存获取历史数据
            if self.cache_service:
                cached_data = self.cache_service.cache_manager.get_cache(
                    'historical_data_cache',
                    'sector_historical_data',
                    sector_code=sector_code,
                    start_date=start_date,
                    end_date=end_date
                )
                if cached_data is not None:
                    logger.debug(f"从MySQL缓存获取板块 {sector_code} 历史数据用于指标计算，共 {len(cached_data)} 条记录")
                    return cached_data

            # MySQL缓存未命中，获取历史数据
            logger.debug(f"MySQL缓存未命中，开始获取板块 {sector_code} 历史数据")
            historical_data = self.get_sector_historical_data(sector_code, start_date, end_date)

            if not historical_data.empty:
                logger.debug(f"成功获取板块 {sector_code} 历史数据用于指标计算，共 {len(historical_data)} 条记录")
            else:
                logger.warning(f"板块 {sector_code} 历史数据为空，无法计算技术指标")

            return historical_data

        except Exception as e:
            logger.error(f"获取板块 {sector_code} 历史数据用于指标计算失败: {e}")
            return pd.DataFrame()

    def get_enhanced_sector_realtime_data(self) -> pd.DataFrame:
        """获取增强的申万行业实时数据，包含领涨股票的详细信息，确保数据一致性"""
        try:
            # 导入交易日历工具
            from utils.trading_calendar import trading_calendar

            # 判断当前是否为交易时间
            is_trading_time = trading_calendar.is_trading_time()

            logger.info("🔄 开始获取增强板块数据 - 步骤1: 获取基础板块数据")
            # 获取基础板块数据
            base_df = self.get_sector_realtime_data()
            logger.info(f"✅ 步骤1完成: 获取基础板块数据，共{len(base_df)}个板块")

            # 确保步骤1完全完成后再开始步骤2，避免并发API调用
            logger.info("⏳ 等待2秒确保步骤1完全完成...")
            time.sleep(2)

            logger.info("🔄 开始获取增强板块数据 - 步骤2: 获取A股实时行情数据")
            # 获取A股实时行情数据用于查找股票详细信息，使用故障转移机制
            stock_realtime_df = self._get_stock_realtime_data_with_fallback()
            logger.info("✅ 步骤2完成: A股实时行情数据获取完毕")

            if stock_realtime_df is None:
                logger.error("所有数据源都失败，尝试从MySQL缓存获取数据")
                # 尝试从MySQL缓存获取最后成功的数据
                cached_data = self.get_cached_sector_data()
                if not cached_data.empty:
                    logger.info(f"API失败降级：从MySQL缓存获取86个板块数据成功，共{len(cached_data)}个板块")
                    return cached_data
                else:
                    logger.error("API失败且缓存为空，将添加默认的增强字段")
                    # 添加默认的增强字段
                    enhanced_df = base_df.copy()
                    enhanced_df['领涨股票代码'] = '-'
                    enhanced_df['领涨股票价格'] = 0.0
                    enhanced_df['数据更新时间'] = pd.Timestamp.now().strftime("%Y-%m-%d %H:%M:%S")
                    return enhanced_df

            # 为每个板块的领涨股票添加详细信息
            enhanced_data = []
            current_time = pd.Timestamp.now().strftime("%Y-%m-%d %H:%M:%S")

            for _, row in base_df.iterrows():
                enhanced_row = row.copy()
                leader_stock_name = row.get('领涨股票', '')

                if leader_stock_name and leader_stock_name != '-':
                    # 在A股数据中查找该股票
                    stock_info = stock_realtime_df[stock_realtime_df['名称'] == leader_stock_name]

                    if not stock_info.empty:
                        stock = stock_info.iloc[0]
                        # 添加股票代码和当前价格
                        enhanced_row['领涨股票代码'] = stock['代码']
                        enhanced_row['领涨股票价格'] = stock['最新价']

                        # 关键修复：在交易时间内，使用A股实时数据中的涨跌幅来更新领涨股票涨跌幅
                        # 确保价格和涨跌幅数据来源一致
                        if is_trading_time and '涨跌幅' in stock:
                            enhanced_row['领涨股票-涨跌幅'] = stock['涨跌幅']
                            logger.debug(f"交易时间内更新领涨股票 {leader_stock_name} 的涨跌幅: {stock['涨跌幅']}%")

                        enhanced_row['数据更新时间'] = current_time
                        logger.debug(f"找到领涨股票 {leader_stock_name} 的详细信息: 代码={stock['代码']}, 价格={stock['最新价']}")
                    else:
                        # 未找到股票信息（可能是退市股票或特殊股票），保留原有涨跌幅但标记为无法获取价格
                        enhanced_row['领涨股票代码'] = '-'
                        enhanced_row['领涨股票价格'] = 0.0
                        enhanced_row['数据更新时间'] = current_time
                        logger.debug(f"未找到领涨股票 {leader_stock_name} 的详细信息（可能为退市股票或特殊股票）")
                else:
                    # 没有领涨股票信息
                    enhanced_row['领涨股票代码'] = '-'
                    enhanced_row['领涨股票价格'] = 0.0
                    enhanced_row['数据更新时间'] = current_time

                enhanced_data.append(enhanced_row)

            enhanced_df = pd.DataFrame(enhanced_data)
            logger.info(f"成功增强板块数据，添加了领涨股票详细信息")
            return enhanced_df

        except Exception as e:
            logger.error(f"获取增强的申万行业实时数据失败: {e}")
            # 如果增强失败，返回基础数据
            try:
                return self.get_sector_realtime_data()
            except:
                raise e
    
    def get_sector_constituents(self, sector_name: str) -> pd.DataFrame:
        """获取板块成分股"""
        cache_key = self._get_cache_key("sector_constituents", sector_name)
        cached_data = self._get_from_cache(cache_key)
        if cached_data is not None:
            return cached_data
        
        try:
            # 获取行业成分股
            df = ak.stock_board_industry_cons_em(symbol=sector_name)
            logger.info(f"成功获取{sector_name}成分股，共{len(df)}只股票")
            self._set_cache(cache_key, df)
            return df
        except Exception as e:
            logger.error(f"获取{sector_name}成分股失败: {e}")
            raise
    
    def get_all_sectors_data(self) -> List[Dict[str, Any]]:
        """获取所有申万一级行业的基本信息 - 优化版本：从86个板块中筛选"""
        try:
            # 优化方案：从86个板块数据中筛选申万31个行业
            logger.info("正在从86个板块数据中筛选申万31个行业...")

            # 获取86个板块的实时数据（已有缓存机制）
            industry_86_df = self.get_sector_realtime_data()

            if industry_86_df.empty:
                logger.warning("86个板块数据为空，使用备用数据")
                return self._get_fallback_sectors_data()

            # 申万代码到东财代码的映射
            mapping = self._get_sector_code_mapping()

            # 申万31个行业的名称映射
            sw31_names = {
                '801010': '农林牧渔', '801020': '采掘', '801030': '化工', '801040': '钢铁',
                '801050': '有色金属', '801080': '电子', '801110': '家用电器', '801120': '食品饮料',
                '801130': '纺织服装', '801140': '轻工制造', '801150': '医药生物', '801160': '公用事业',
                '801170': '交通运输', '801180': '房地产', '801200': '商业贸易', '801210': '休闲服务',
                '801230': '综合', '801710': '建筑材料', '801720': '建筑装饰', '801730': '电气设备',
                '801740': '国防军工', '801750': '计算机', '801760': '传媒', '801770': '通信',
                '801780': '银行', '801790': '非银金融', '801880': '汽车', '801890': '机械设备',
                '801950': '煤炭', '801960': '石油石化', '801970': '环保'
            }

            # 从86个板块中筛选申万31个行业
            sw_sectors = []
            board_codes_set = set(industry_86_df['板块代码'].tolist())

            for sw_code, sw_name in sw31_names.items():
                bk_code = mapping.get(sw_code)
                if bk_code and bk_code in board_codes_set:
                    sw_sectors.append({
                        "code": sw_code,
                        "name": sw_name
                    })
                    logger.debug(f"筛选到申万行业: {sw_code} - {sw_name} (对应东财代码: {bk_code})")
                else:
                    logger.warning(f"申万行业 {sw_code} - {sw_name} 在86个板块中未找到对应")

            if sw_sectors:
                logger.info(f"从86个板块中成功筛选出{len(sw_sectors)}个申万行业，减少了API调用")
                return sw_sectors
            else:
                logger.warning("筛选结果为空，使用备用数据")
                return self._get_fallback_sectors_data()

        except Exception as e:
            logger.error(f"从86个板块筛选申万行业失败: {e}，使用备用数据")
            return self._get_fallback_sectors_data()

    def _get_fallback_sectors_data(self) -> List[Dict[str, Any]]:
        """获取备用申万一级行业数据（硬编码）"""
        # 备用数据：申万一级行业列表（31个）
        sw_sectors = [
            {"code": "801010", "name": "农林牧渔"},
            {"code": "801020", "name": "采掘"},
            {"code": "801030", "name": "化工"},
            {"code": "801040", "name": "钢铁"},
            {"code": "801050", "name": "有色金属"},
            {"code": "801080", "name": "电子"},
            {"code": "801110", "name": "家用电器"},
            {"code": "801120", "name": "食品饮料"},
            {"code": "801130", "name": "纺织服装"},
            {"code": "801140", "name": "轻工制造"},
            {"code": "801150", "name": "医药生物"},
            {"code": "801160", "name": "公用事业"},
            {"code": "801170", "name": "交通运输"},
            {"code": "801180", "name": "房地产"},
            {"code": "801200", "name": "商业贸易"},
            {"code": "801210", "name": "休闲服务"},
            {"code": "801230", "name": "综合"},
            {"code": "801710", "name": "建筑材料"},
            {"code": "801720", "name": "建筑装饰"},
            {"code": "801730", "name": "电气设备"},
            {"code": "801740", "name": "国防军工"},
            {"code": "801750", "name": "计算机"},
            {"code": "801760", "name": "传媒"},
            {"code": "801770", "name": "通信"},
            {"code": "801780", "name": "银行"},
            {"code": "801790", "name": "非银金融"},
            {"code": "801880", "name": "汽车"},
            {"code": "801890", "name": "机械设备"},
            {"code": "801950", "name": "煤炭"},
            {"code": "801960", "name": "石油石化"},
            {"code": "801970", "name": "环保"}
        ]

        logger.info(f"使用备用申万一级行业数据，共{len(sw_sectors)}个板块")
        return sw_sectors

    def get_shenwan_31_realtime_data(self) -> pd.DataFrame:
        """获取申万31个行业的实时数据 - 优化版本：从86个板块中筛选"""
        try:
            logger.info("正在从86个板块数据中筛选申万31个行业实时数据...")

            # 获取86个板块的实时数据（已有缓存机制）
            industry_86_df = self.get_sector_realtime_data()

            if industry_86_df.empty:
                logger.warning("86个板块数据为空")
                return pd.DataFrame()

            # 申万代码到东财代码的映射
            mapping = self._get_sector_code_mapping()

            # 申万31个行业的名称映射
            sw31_names = {
                '801010': '农林牧渔', '801020': '采掘', '801030': '化工', '801040': '钢铁',
                '801050': '有色金属', '801080': '电子', '801110': '家用电器', '801120': '食品饮料',
                '801130': '纺织服装', '801140': '轻工制造', '801150': '医药生物', '801160': '公用事业',
                '801170': '交通运输', '801180': '房地产', '801200': '商业贸易', '801210': '休闲服务',
                '801230': '综合', '801710': '建筑材料', '801720': '建筑装饰', '801730': '电气设备',
                '801740': '国防军工', '801750': '计算机', '801760': '传媒', '801770': '通信',
                '801780': '银行', '801790': '非银金融', '801880': '汽车', '801890': '机械设备',
                '801950': '煤炭', '801960': '石油石化', '801970': '环保'
            }

            # 筛选申万31个行业的数据
            sw31_data = []
            for sw_code, sw_name in sw31_names.items():
                bk_code = mapping.get(sw_code)
                if bk_code:
                    # 在86个板块中查找对应的数据
                    board_row = industry_86_df[industry_86_df['板块代码'] == bk_code]
                    if not board_row.empty:
                        row_data = board_row.iloc[0].copy()
                        # 替换为申万代码和名称
                        row_data['板块代码'] = sw_code
                        row_data['板块名称'] = sw_name
                        sw31_data.append(row_data)
                        logger.debug(f"筛选到申万行业实时数据: {sw_code} - {sw_name}")
                    else:
                        logger.warning(f"申万行业 {sw_code} - {sw_name} 对应的东财代码 {bk_code} 在86个板块中未找到")
                else:
                    logger.warning(f"申万行业 {sw_code} - {sw_name} 没有对应的东财代码映射")

            if sw31_data:
                result_df = pd.DataFrame(sw31_data)
                logger.info(f"从86个板块中成功筛选出{len(result_df)}个申万行业实时数据")
                return result_df
            else:
                logger.warning("未能筛选到任何申万行业实时数据")
                return pd.DataFrame()

        except Exception as e:
            logger.error(f"从86个板块筛选申万31个行业实时数据失败: {e}")
            return pd.DataFrame()

    def get_cached_sector_data(self) -> pd.DataFrame:
        """
        优先从MySQL缓存获取86个板块数据，不触发API调用
        用于页面加载时快速返回数据
        """
        try:
            if self.cache_service:
                # 尝试从MySQL缓存获取数据
                cache_key = "enhanced_sector_realtime_data"
                cached_data = self.cache_service.get_cache(
                    'sector_data_cache',
                    cache_key
                )

                if cached_data is not None:
                    logger.info(f"从MySQL缓存获取86个板块数据成功，共{len(cached_data)}个板块")
                    return cached_data
                else:
                    logger.info("MySQL缓存中无86个板块数据")
                    return pd.DataFrame()
            else:
                logger.warning("MySQL缓存服务未初始化")
                return pd.DataFrame()

        except Exception as e:
            logger.error(f"从MySQL缓存获取86个板块数据失败: {e}")
            return pd.DataFrame()

    def get_cached_shenwan31_data(self) -> pd.DataFrame:
        """
        严格从MySQL缓存获取申万31行业数据，绝不触发API调用
        用于页面启动时快速返回数据，确保启动性能
        """
        try:
            if self.cache_service:
                # 尝试从MySQL缓存获取数据
                cache_key = "shenwan31_realtime_data"
                cached_data = self.cache_service.get_cache(
                    'sector_data_cache',
                    cache_key
                )

                if cached_data is not None:
                    logger.info(f"页面启动：从MySQL缓存获取申万31行业数据成功，共{len(cached_data)}个行业")
                    return cached_data
                else:
                    logger.info("页面启动：MySQL缓存中无申万31行业数据，建议用户手动刷新")
                    return pd.DataFrame()
            else:
                logger.warning("页面启动：MySQL缓存服务未初始化")
                return pd.DataFrame()

        except Exception as e:
            logger.error(f"页面启动：从MySQL缓存获取申万31行业数据失败: {e}")
            return pd.DataFrame()

    def get_enhanced_sector_realtime_data_optimized(self, force_refresh: bool = False, smart_update: bool = False) -> pd.DataFrame:
        """
        优化的86个板块实时数据获取方法
        实现智能缓存策略，根据交易时间和数据新鲜度决定是否更新

        Args:
            force_refresh: 是否强制刷新（忽略缓存）
            smart_update: 是否启用智能更新（检查数据新鲜度）

        Returns:
            86个板块实时数据DataFrame
        """
        try:
            # 🔧 修复 #1：最优先处理强制刷新
            if force_refresh:
                logger.info("🟢 用户触发强制刷新，跳过所有缓存，直接调用API")
                fresh_data = self.get_enhanced_sector_realtime_data_with_indicators()
                if not fresh_data.empty:
                    # 🔧 修复：强制刷新时更新所有板块的数据更新时间
                    current_time = pd.Timestamp.now().strftime("%Y-%m-%d %H:%M:%S")
                    fresh_data['数据更新时间'] = current_time
                    logger.info(f"强制刷新完成，更新了{len(fresh_data)}个板块的数据时间戳为: {current_time}")

                    # 更新缓存
                    if self.cache_service:
                        self.cache_service.cache_manager.set_cache(
                            'realtime_quotes_cache',
                            'sector_realtime_data',
                            fresh_data,
                            300  # 5分钟TTL
                        )
                    return fresh_data
                else:
                    logger.warning("强制刷新失败，回退到缓存数据")

            # 如果启用智能更新，检查数据新鲜度
            if smart_update and not force_refresh:
                should_update = self._should_update_sector_data()
                if not should_update:
                    cached_data = self.get_cached_sector_data()
                    if not cached_data.empty:
                        logger.info(f"智能更新：数据新鲜，使用缓存数据，共{len(cached_data)}个板块")
                        return cached_data
                else:
                    logger.info("智能更新：数据过时，需要从API获取最新数据")

            # 如果不是强制刷新且未启用智能更新，优先从缓存获取
            elif not force_refresh and not smart_update and self.cache_service:
                cached_data = self.get_cached_sector_data()
                if not cached_data.empty:
                    logger.info(f"从MySQL缓存获取86个板块数据成功，共{len(cached_data)}个板块")
                    return cached_data
                else:
                    logger.info("MySQL缓存中无86个板块数据，开始API调用")

            # 缓存未命中或强制刷新，调用API获取数据
            logger.info("开始API调用获取86个板块实时数据")
            realtime_data = self.get_enhanced_sector_realtime_data()

            if not realtime_data.empty:
                # API成功，缓存数据
                if self.cache_service:
                    try:
                        self.cache_service.set_cache(
                            'sector_data_cache',
                            'enhanced_sector_realtime_data',
                            realtime_data,
                            ttl=86400  # 24小时 - 与配置保持一致
                        )
                        logger.info("86个板块实时数据已缓存到MySQL")
                    except Exception as e:
                        logger.error(f"缓存86个板块实时数据失败: {e}")

                return realtime_data
            else:
                # API失败，尝试从缓存获取（降级策略）
                logger.warning("API获取86个板块数据失败，尝试从MySQL缓存获取")
                cached_data = self.get_cached_sector_data()

                if not cached_data.empty:
                    logger.info(f"API失败降级：从MySQL缓存获取86个板块数据成功，共{len(cached_data)}个板块")
                    return cached_data
                else:
                    logger.error("API失败且缓存为空，无法获取86个板块数据")
                    return pd.DataFrame()

        except Exception as e:
            logger.error(f"获取86个板块数据失败: {e}")
            # 异常情况下尝试从缓存获取
            try:
                if self.cache_service:
                    cached_data = self.get_cached_sector_data()
                    if not cached_data.empty:
                        logger.info(f"异常降级：从MySQL缓存获取86个板块数据成功，共{len(cached_data)}个板块")
                        return cached_data
            except Exception as cache_e:
                logger.error(f"异常降级：从缓存获取数据也失败: {cache_e}")

            return pd.DataFrame()

    def _should_update_sector_data(self) -> bool:
        """
        智能判断是否需要更新板块数据
        基于交易时间和数据更新时间进行判断

        Returns:
            bool: True表示需要更新，False表示使用缓存
        """
        try:
            from utils.trading_calendar import trading_calendar

            # 获取缓存数据
            cached_data = self.get_cached_sector_data()
            if cached_data.empty:
                logger.info("缓存为空，需要更新")
                return True

            # 检查数据更新时间
            if '数据更新时间' not in cached_data.columns:
                logger.info("缺少数据更新时间字段，需要更新")
                return True

            # 获取最新的数据更新时间
            update_times = cached_data['数据更新时间'].dropna()
            if update_times.empty:
                logger.info("无有效的数据更新时间，需要更新")
                return True

            latest_update_str = str(update_times.iloc[0])
            try:
                latest_update = datetime.strptime(latest_update_str, "%Y-%m-%d %H:%M:%S")
            except ValueError:
                logger.warning(f"数据更新时间格式错误: {latest_update_str}，需要更新")
                return True

            current_time = datetime.now()
            time_diff = current_time - latest_update

            # 🔧 修复：完善非交易时间的数据新鲜度判断逻辑
            if not trading_calendar.is_trading_time():
                # 检查数据是否是今天的
                if latest_update.date() == current_time.date():
                    # 🔧 修复：检查今天是否为交易日
                    is_trading_day = trading_calendar.is_trading_day(current_time.date())

                    if is_trading_day:
                        # 如果今天是交易日，检查当前时间是否在交易结束后
                        trading_end_time = current_time.replace(hour=15, minute=0, second=0, microsecond=0)

                        if current_time >= trading_end_time:
                            # 当前时间在交易结束后，检查数据获取时间是否也在交易结束后
                            if latest_update >= trading_end_time:
                                logger.info(f"非交易时间，今日交易已结束，数据是交易结束后获取的({latest_update.strftime('%H:%M:%S')} >= 15:00)，使用缓存")
                                return False
                            else:
                                logger.info(f"非交易时间，今日交易已结束，但数据是交易结束前获取的({latest_update.strftime('%H:%M:%S')} < 15:00)，需要更新获取最新数据")
                                return True
                        else:
                            # 当前时间在交易结束前（盘前时间），使用当日数据
                            logger.info(f"非交易时间，今日交易尚未结束，数据是今天的，使用缓存")
                            return False
                    else:
                        # 今天不是交易日，使用当日数据
                        logger.info(f"非交易时间，今天不是交易日，数据是今天的，使用缓存")
                        return False
                else:
                    logger.info("非交易时间但数据不是今天的，需要更新")
                    return True

            # 🔧 修复：交易时间内，缩短新鲜度阈值到1分钟，提高数据实时性
            freshness_threshold = 60  # 1分钟（从15分钟进一步缩短）
            if time_diff.total_seconds() > freshness_threshold:
                logger.info(f"交易时间内数据已过时({time_diff.total_seconds():.1f}秒 > 1分钟)，需要更新")
                return True
            else:
                logger.info(f"交易时间内数据较新({time_diff.total_seconds():.1f}秒 <= 1分钟)，使用缓存")
                return False

        except Exception as e:
            logger.error(f"数据新鲜度检查失败: {e}，默认需要更新")
            return True

    def get_enhanced_sector_realtime_data_with_cache_fallback(self) -> pd.DataFrame:
        """
        获取增强的申万行业实时数据，API失败时使用MySQL缓存作为降级策略
        （保留原方法以保持兼容性）
        """
        return self.get_enhanced_sector_realtime_data_optimized(force_refresh=False)

    def get_shenwan_31_realtime_data_optimized(self, force_refresh: bool = False) -> pd.DataFrame:
        """
        优化的申万31行业实时数据获取方法
        实现缓存优先策略，避免不必要的API调用

        Args:
            force_refresh: 是否强制刷新（忽略缓存）

        Returns:
            申万31行业实时数据DataFrame
        """
        try:
            # 如果不是强制刷新，优先从缓存获取
            if not force_refresh and self.cache_service:
                cached_data = self.get_cached_shenwan31_data()
                if not cached_data.empty:
                    logger.info(f"从MySQL缓存获取申万31行业数据成功，共{len(cached_data)}个行业")
                    return cached_data
                else:
                    logger.info("MySQL缓存中无申万31行业数据，开始API调用")

            # 缓存未命中或强制刷新，调用API获取数据
            logger.info("开始API调用获取申万31行业实时数据")
            realtime_data = self.get_shenwan_31_realtime_data()

            if not realtime_data.empty:
                # API成功，缓存数据
                if self.cache_service:
                    try:
                        self.cache_service.set_cache(
                            'sector_data_cache',
                            'shenwan31_realtime_data',
                            realtime_data,
                            ttl=86400  # 24小时 - 与配置保持一致
                        )
                        logger.info("申万31行业实时数据已缓存到MySQL")
                    except Exception as e:
                        logger.error(f"缓存申万31行业实时数据失败: {e}")

                return realtime_data
            else:
                # API失败，尝试从缓存获取（降级策略）
                logger.warning("API获取申万31行业数据失败，尝试从MySQL缓存获取")
                cached_data = self.get_cached_shenwan31_data()

                if not cached_data.empty:
                    logger.info(f"API失败降级：从MySQL缓存获取申万31行业数据成功，共{len(cached_data)}个行业")
                    return cached_data
                else:
                    logger.error("API失败且缓存为空，无法获取申万31行业数据")
                    return pd.DataFrame()

        except Exception as e:
            logger.error(f"获取申万31行业数据失败: {e}")
            # 异常情况下尝试从缓存获取
            try:
                if self.cache_service:
                    cached_data = self.get_cached_shenwan31_data()
                    if not cached_data.empty:
                        logger.info(f"异常降级：从MySQL缓存获取申万31行业数据成功，共{len(cached_data)}个行业")
                        return cached_data
            except Exception as cache_e:
                logger.error(f"异常降级：从缓存获取数据也失败: {cache_e}")

            return pd.DataFrame()

    def get_shenwan_31_realtime_data_with_cache_fallback(self) -> pd.DataFrame:
        """
        获取申万31行业实时数据，API失败时使用MySQL缓存作为降级策略
        （保留原方法以保持兼容性）
        """
        return self.get_shenwan_31_realtime_data_optimized(force_refresh=False)

    def test_akshare_connection(self) -> Dict[str, Any]:
        """测试AKShare API连接"""
        test_results = {
            "akshare_available": False,
            "sectors_api": False,
            "historical_api": False,
            "realtime_api": False,
            "error_messages": []
        }

        try:
            import akshare as ak
            test_results["akshare_available"] = True
            logger.info(f"AKShare版本: {ak.__version__}")

            # 测试申万行业数据API
            try:
                df = ak.stock_board_industry_name_em()
                if not df.empty:
                    test_results["sectors_api"] = True
                    logger.info(f"申万行业数据API测试成功，返回{len(df)}条记录")
                else:
                    test_results["error_messages"].append("申万行业数据API返回空数据")
            except Exception as e:
                test_results["error_messages"].append(f"申万行业数据API测试失败: {e}")

            # 测试历史数据API（使用一个常见的板块代码）
            try:
                end_date = datetime.now().strftime("%Y%m%d")
                start_date = (datetime.now() - timedelta(days=30)).strftime("%Y%m%d")
                df = ak.stock_board_industry_hist_em(
                    symbol="BK0447",  # 通信设备
                    start_date=start_date,
                    end_date=end_date,
                    period="日k",
                    adjust=""
                )
                if not df.empty:
                    test_results["historical_api"] = True
                    logger.info(f"历史数据API测试成功，返回{len(df)}条记录")
                else:
                    test_results["error_messages"].append("历史数据API返回空数据")
            except Exception as e:
                test_results["error_messages"].append(f"历史数据API测试失败: {e}")

        except ImportError as e:
            test_results["error_messages"].append(f"AKShare导入失败: {e}")
        except Exception as e:
            test_results["error_messages"].append(f"AKShare测试失败: {e}")

        return test_results

    def _is_trading_day(self, date_obj):
        """检查是否为交易日（排除周末）"""
        if isinstance(date_obj, str):
            date_obj = datetime.strptime(date_obj, '%Y-%m-%d').date()
        elif isinstance(date_obj, datetime):
            date_obj = date_obj.date()

        # 检查是否为周末 (0=周一, 6=周日)
        if date_obj.weekday() >= 5:  # 5=周六, 6=周日
            return False

        # 这里可以添加更多的节假日检查逻辑
        return True

    def _filter_trading_days(self, df, date_column='日期'):
        """过滤掉非交易日的数据"""
        if df.empty or date_column not in df.columns:
            return df

        original_count = len(df)

        # 转换日期列
        df[date_column] = pd.to_datetime(df[date_column])

        # 过滤周末
        df = df[df[date_column].dt.weekday < 5]

        filtered_count = len(df)
        if original_count != filtered_count:
            logger.warning(f"过滤掉 {original_count - filtered_count} 条非交易日数据，保留 {filtered_count} 条")

        return df

    def get_sector_latest_quote(self, sector_code: str) -> Dict[str, Any]:
        """获取板块最新行情数据"""
        try:
            # 方法1: 从实时数据中获取
            try:
                realtime_df = self.get_sector_realtime_data()
                sector_row = realtime_df[realtime_df['板块代码'] == sector_code]

                if not sector_row.empty:
                    row = sector_row.iloc[0]
                    quote_data = {
                        "date": datetime.now().strftime("%Y-%m-%d"),
                        "close": float(row.get("最新价", 0)),
                        "change": float(row.get("涨跌额", 0)),
                        "change_pct": float(row.get("涨跌幅", 0)),
                        "market_value": float(row.get("总市值", 0)),
                        "turnover_rate": float(row.get("换手率", 0)),
                        "up_count": int(row.get("上涨家数", 0)),
                        "down_count": int(row.get("下跌家数", 0))
                    }
                    logger.info(f"从实时数据获取板块{sector_code}最新行情")
                    return quote_data
            except Exception as e1:
                logger.warning(f"从实时数据获取{sector_code}行情失败: {e1}")

            # 方法2: 从历史数据获取最新一条
            try:
                historical_df = self.get_sector_historical_data(
                    sector_code=sector_code,
                    start_date=(datetime.now() - timedelta(days=7)).strftime("%Y%m%d"),
                    end_date=datetime.now().strftime("%Y%m%d")
                )

                if not historical_df.empty:
                    latest = historical_df.iloc[-1]
                    quote_data = {
                        "date": str(latest.get("日期", "")),
                        "open": float(latest.get("开盘", 0)),
                        "high": float(latest.get("最高", 0)),
                        "low": float(latest.get("最低", 0)),
                        "close": float(latest.get("收盘", 0)),
                        "volume": int(latest.get("成交量", 0)),
                        "amount": float(latest.get("成交额", 0)),
                        "change": float(latest.get("涨跌额", 0)),
                        "change_pct": float(latest.get("涨跌幅", 0))
                    }
                    logger.info(f"从历史数据获取板块{sector_code}最新行情")
                    return quote_data
            except Exception as e2:
                logger.warning(f"从历史数据获取{sector_code}行情失败: {e2}")

            logger.warning(f"无法获取板块{sector_code}的最新行情数据")
            return {}

        except Exception as e:
            logger.error(f"获取板块{sector_code}最新行情失败: {e}")
            return {}



    def clear_cache(self):
        """清空缓存"""
        self._cache.clear()
        logger.info("缓存已清空")

# 全局数据服务实例 - 优化配置以提高成功率，启用MySQL缓存
def create_global_data_service():
    """创建全局数据服务实例，自动配置MySQL缓存"""
    try:
        # 导入配置
        import sys
        import os
        sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
        from config import LocalConfig

        config = LocalConfig()
        mysql_config = {
            'MYSQL_CACHE_CONFIG': config.MYSQL_CACHE_CONFIG,
            'CACHE_STRATEGY': config.CACHE_STRATEGY
        }

        return DataService(
            cache_timeout=900,
            retry_count=8,  # 增加到8次重试
            max_persistent_retries=10,  # 持久化重试10次
            mysql_config=mysql_config,  # 启用MySQL缓存
            use_concurrent_api=False  # 明确禁用并发模式，严格使用顺序模式
        )
    except Exception as e:
        logger.warning(f"创建带MySQL缓存的全局数据服务失败: {e}")
        logger.info("降级创建不带缓存的数据服务实例")
        return DataService(
            cache_timeout=900,
            retry_count=8,
            max_persistent_retries=10,
            use_concurrent_api=False  # 明确禁用并发模式，严格使用顺序模式
        )

# 创建全局数据服务实例
data_service = create_global_data_service()
