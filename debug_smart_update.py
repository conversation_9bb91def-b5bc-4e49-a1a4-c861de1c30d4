#!/usr/bin/env python3
"""
智能更新数据时间显示问题诊断脚本
用于测试和验证智能更新功能的数据时间戳更新机制
"""

import requests
import json
import time
from datetime import datetime

def test_smart_update_flow():
    """测试完整的智能更新流程"""
    print("🔍 智能更新数据时间显示问题诊断")
    print("=" * 60)
    
    base_url = "http://localhost:5000/api"
    
    try:
        # 1. 获取更新前的数据时间
        print("\n1️⃣ 获取更新前的数据时间...")
        before_response = requests.get(f"{base_url}/sectors/all-realtime", timeout=30)
        before_data = before_response.json()
        
        before_update_times = []
        if before_data.get('success') and before_data.get('data'):
            before_update_times = [
                s.get('数据更新时间') for s in before_data['data'] 
                if s.get('数据更新时间')
            ]
        
        before_latest = max(before_update_times) if before_update_times else "无"
        print(f"   📅 更新前最新时间: {before_latest}")
        print(f"   📊 数据源: {before_data.get('data_source', '未知')}")
        print(f"   🔢 板块数量: {len(before_data.get('data', []))}")
        
        # 2. 执行智能更新
        print("\n2️⃣ 执行智能更新...")
        start_time = time.time()
        
        update_response = requests.post(
            f"{base_url}/data/update",
            json={
                "update_mode": "incremental",
                "force_user_update": True
            },
            timeout=350
        )
        
        update_time = time.time() - start_time
        update_data = update_response.json()
        
        print(f"   ⏱️ 更新耗时: {update_time:.2f}秒")
        print(f"   ✅ 更新状态: {update_data.get('success', False)}")
        print(f"   📝 更新消息: {update_data.get('message', '无消息')}")
        
        # 3. 获取更新后的数据时间
        print("\n3️⃣ 获取更新后的数据时间...")
        after_response = requests.get(f"{base_url}/sectors/all-realtime", timeout=30)
        after_data = after_response.json()
        
        after_update_times = []
        if after_data.get('success') and after_data.get('data'):
            after_update_times = [
                s.get('数据更新时间') for s in after_data['data'] 
                if s.get('数据更新时间')
            ]
        
        after_latest = max(after_update_times) if after_update_times else "无"
        print(f"   📅 更新后最新时间: {after_latest}")
        print(f"   📊 数据源: {after_data.get('data_source', '未知')}")
        print(f"   🔢 板块数量: {len(after_data.get('data', []))}")
        
        # 4. 分析结果
        print("\n4️⃣ 结果分析...")
        if before_latest == after_latest:
            print("   ❌ 问题确认: 数据时间戳未更新")
            print(f"   🔍 时间戳保持: {before_latest}")
            
            # 检查是否是缓存问题
            if before_data.get('data_source') == 'cache' and after_data.get('data_source') == 'cache':
                print("   💡 可能原因: 智能更新未触发强制刷新，仍使用缓存数据")
            
        else:
            print("   ✅ 时间戳已更新")
            print(f"   📈 从 {before_latest} 更新到 {after_latest}")
        
        # 5. 检查具体的板块数据
        print("\n5️⃣ 检查具体板块数据...")
        if after_data.get('success') and after_data.get('data'):
            sample_sectors = after_data['data'][:3]  # 检查前3个板块
            for i, sector in enumerate(sample_sectors, 1):
                print(f"   板块{i}: {sector.get('板块名称', '未知')} - 时间: {sector.get('数据更新时间', '无')}")
        
        return {
            'before_time': before_latest,
            'after_time': after_latest,
            'update_success': update_data.get('success', False),
            'time_changed': before_latest != after_latest,
            'update_duration': update_time
        }
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return None

def test_force_refresh():
    """测试强制刷新功能"""
    print("\n🔄 测试强制刷新功能")
    print("=" * 40)
    
    base_url = "http://localhost:5000/api"
    
    try:
        # 调用强制刷新API
        print("执行强制刷新...")
        start_time = time.time()
        
        response = requests.post(
            f"{base_url}/sectors/refresh",
            json={
                "data_type": "all",
                "force_refresh": True
            },
            timeout=350
        )
        
        refresh_time = time.time() - start_time
        data = response.json()
        
        print(f"⏱️ 刷新耗时: {refresh_time:.2f}秒")
        print(f"✅ 刷新状态: {data.get('success', False)}")
        print(f"📝 刷新消息: {data.get('message', '无消息')}")
        
        # 获取刷新后的数据
        after_response = requests.get(f"{base_url}/sectors/all-realtime", timeout=30)
        after_data = after_response.json()
        
        if after_data.get('success') and after_data.get('data'):
            update_times = [
                s.get('数据更新时间') for s in after_data['data'] 
                if s.get('数据更新时间')
            ]
            latest_time = max(update_times) if update_times else "无"
            print(f"📅 强制刷新后时间: {latest_time}")
        
        return data.get('success', False)
        
    except Exception as e:
        print(f"❌ 强制刷新测试失败: {e}")
        return False

def main():
    """主函数"""
    print(f"🚀 开始诊断 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 测试智能更新流程
    smart_update_result = test_smart_update_flow()
    
    if smart_update_result and not smart_update_result['time_changed']:
        print("\n🔧 检测到时间戳未更新，尝试强制刷新...")
        force_refresh_result = test_force_refresh()
        
        if force_refresh_result:
            print("✅ 强制刷新成功，建议检查智能更新的强制刷新逻辑")
        else:
            print("❌ 强制刷新也失败，可能存在更深层的问题")
    
    print(f"\n🏁 诊断完成 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
