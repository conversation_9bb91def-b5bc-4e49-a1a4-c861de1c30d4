#!/usr/bin/env python3
"""
板块日历定时任务启动脚本
确保板块排名数据能够自动收集
"""

import sys
import os
import time
import schedule
import requests
from datetime import datetime, timedelta
import logging

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('sector_calendar_scheduler.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def collect_daily_sector_rankings():
    """收集每日板块排名数据"""
    try:
        logger.info("开始收集每日板块排名数据...")
        
        # 获取今天的日期
        today = datetime.now().strftime('%Y-%m-%d')
        
        # 调用API收集数据
        response = requests.post(
            "http://localhost:5000/api/sector-calendar/collect",
            json={
                "target_date": today,
                "top_n": 10
            },
            timeout=120  # 2分钟超时
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                result = data.get('data', {})
                collected_count = result.get('collected_count', 0)
                logger.info(f"✅ 成功收集 {today} 的数据: {collected_count} 条记录")
                return True
            else:
                error_msg = data.get('error', '未知错误')
                logger.error(f"❌ 数据收集失败: {error_msg}")
                return False
        else:
            logger.error(f"❌ API调用失败: HTTP {response.status_code}")
            return False
            
    except requests.exceptions.Timeout:
        logger.error("❌ 数据收集超时")
        return False
    except Exception as e:
        logger.error(f"❌ 数据收集异常: {e}")
        return False

def check_data_freshness():
    """检查数据新鲜度"""
    try:
        response = requests.get(
            "http://localhost:5000/api/sector-calendar/rankings?limit=1",
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success') and data.get('data'):
                latest_date = data['data'][0].get('ranking_date')
                today = datetime.now().strftime('%Y-%m-%d')
                
                if latest_date == today:
                    logger.info(f"✅ 数据是最新的: {latest_date}")
                    return True
                else:
                    logger.warning(f"⚠️  数据不是最新的: 最新={latest_date}, 今天={today}")
                    return False
            else:
                logger.warning("⚠️  没有可用数据")
                return False
        else:
            logger.error(f"❌ 数据检查失败: HTTP {response.status_code}")
            return False
            
    except Exception as e:
        logger.error(f"❌ 数据检查异常: {e}")
        return False

def scheduled_collection_job():
    """定时收集任务"""
    logger.info("=" * 50)
    logger.info("执行定时板块数据收集任务")
    
    # 首先检查数据新鲜度
    if check_data_freshness():
        logger.info("数据已是最新，跳过收集")
        return
    
    # 收集数据
    success = collect_daily_sector_rankings()
    
    if success:
        logger.info("✅ 定时收集任务完成")
    else:
        logger.error("❌ 定时收集任务失败")
    
    logger.info("=" * 50)

def start_scheduler():
    """启动定时任务调度器"""
    logger.info("🚀 启动板块日历定时任务调度器...")
    
    # 设置定时任务
    # 工作日上午9:30（开盘后）收集数据
    schedule.every().monday.at("09:35").do(scheduled_collection_job)
    schedule.every().tuesday.at("09:35").do(scheduled_collection_job)
    schedule.every().wednesday.at("09:35").do(scheduled_collection_job)
    schedule.every().thursday.at("09:35").do(scheduled_collection_job)
    schedule.every().friday.at("09:35").do(scheduled_collection_job)
    
    # 工作日下午3:05（收盘后）再次收集确保数据完整
    schedule.every().monday.at("15:05").do(scheduled_collection_job)
    schedule.every().tuesday.at("15:05").do(scheduled_collection_job)
    schedule.every().wednesday.at("15:05").do(scheduled_collection_job)
    schedule.every().thursday.at("15:05").do(scheduled_collection_job)
    schedule.every().friday.at("15:05").do(scheduled_collection_job)
    
    logger.info("📅 定时任务已设置:")
    logger.info("   - 工作日 09:35: 开盘后数据收集")
    logger.info("   - 工作日 15:05: 收盘后数据收集")
    
    # 立即执行一次收集（如果需要）
    logger.info("🔍 检查是否需要立即收集数据...")
    if not check_data_freshness():
        logger.info("🚀 立即执行数据收集...")
        collect_daily_sector_rankings()
    
    # 开始调度循环
    logger.info("⏰ 定时任务调度器已启动，等待执行...")
    
    try:
        while True:
            schedule.run_pending()
            time.sleep(60)  # 每分钟检查一次
    except KeyboardInterrupt:
        logger.info("📴 定时任务调度器已停止")

def manual_collection():
    """手动收集数据"""
    logger.info("🔧 手动收集板块排名数据...")
    
    # 收集最近3天的数据
    for i in range(3):
        target_date = (datetime.now() - timedelta(days=i)).strftime('%Y-%m-%d')
        
        try:
            logger.info(f"📅 收集 {target_date} 的数据...")
            
            response = requests.post(
                "http://localhost:5000/api/sector-calendar/collect",
                json={
                    "target_date": target_date,
                    "top_n": 10
                },
                timeout=120
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    result = data.get('data', {})
                    collected_count = result.get('collected_count', 0)
                    logger.info(f"   ✅ 成功收集 {collected_count} 条数据")
                else:
                    error_msg = data.get('error', '未知错误')
                    logger.info(f"   ❌ 收集失败: {error_msg}")
            else:
                logger.info(f"   ❌ HTTP错误: {response.status_code}")
                
        except requests.exceptions.Timeout:
            logger.info(f"   ⏰ 请求超时，跳过 {target_date}")
        except Exception as e:
            logger.info(f"   ❌ 异常: {e}")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='板块日历定时任务管理')
    parser.add_argument('--mode', choices=['scheduler', 'manual'], default='scheduler',
                       help='运行模式: scheduler(定时任务) 或 manual(手动收集)')
    
    args = parser.parse_args()
    
    if args.mode == 'scheduler':
        start_scheduler()
    elif args.mode == 'manual':
        manual_collection()

if __name__ == '__main__':
    main()
